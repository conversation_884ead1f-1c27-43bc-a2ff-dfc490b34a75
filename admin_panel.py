#!/usr/bin/env python3
"""
Simple Admin Panel for MCP Automated Pipeline
A web-based dashboard to monitor the entire project status
"""

import os
import json
import csv
from datetime import datetime
from pathlib import Path
import subprocess
from flask import Flask, render_template, jsonify, request
import pandas as pd

app = Flask(__name__)

class PipelineProgressTracker:
    """Enhanced progress tracking system that works with both CSV and Redis"""

    def __init__(self, base_dir=".", use_redis=False):
        self.base_dir = Path(base_dir)
        self.use_redis = use_redis
        self.redis_client = None

        if use_redis:
            try:
                import redis
                self.redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
                self.redis_client.ping()
                print("✅ Connected to Redis for progress tracking")
            except Exception as e:
                print(f"⚠️ Redis not available, falling back to CSV: {e}")
                self.use_redis = False

    def get_pipeline_state(self):
        """Get comprehensive pipeline state across all URLs"""
        try:
            # Load all URLs from master_data.csv
            master_file = self.base_dir / "data" / "master_data.csv"
            if not master_file.exists():
                return {"total_urls": 0, "scraped": 0, "generated": 0, "pending": 0, "failed": 0}

            import pandas as pd
            try:
                df_master = pd.read_csv(master_file)
                total_urls = len(df_master)
            except Exception as e:
                print(f"Error reading master_data.csv: {e}")
                return {"error": f"Error reading master_data.csv: {e}"}

            # Count scraped URLs
            scraped_urls = set()
            completed_file = self.base_dir / "data" / "completed_data.csv"
            if completed_file.exists():
                try:
                    # Try reading with encoding fallback for corrupted files
                    df_completed = pd.read_csv(completed_file, encoding='utf-8', on_bad_lines='skip', engine='python')
                    scraped_urls = set(df_completed['repo_url'].tolist())
                except UnicodeDecodeError:
                    # Handle encoding issues by trying different encodings
                    try:
                        df_completed = pd.read_csv(completed_file, encoding='latin-1', on_bad_lines='skip', engine='python')
                        scraped_urls = set(df_completed['repo_url'].tolist())
                    except Exception as e:
                        print(f"Error reading completed_data.csv with fallback encoding: {e}")
                        scraped_urls = set()
                except Exception as e:
                    print(f"Error reading completed_data.csv: {e}")
                    scraped_urls = set()

            # Count generated URLs
            generated_urls = set()
            generated_file = self.base_dir / "data" / "generated_content.json"
            if generated_file.exists():
                try:
                    import json
                    with open(generated_file, 'r') as f:
                        generated_data = json.load(f)
                        for item in generated_data:
                            content = item.get('content', {})
                            github_url = content.get('github_url', '')
                            if github_url:
                                generated_urls.add(github_url)
                except Exception as e:
                    print(f"Error reading generated_content.json: {e}")

            # Calculate states
            scraped_count = len(scraped_urls)
            generated_count = len(generated_urls)
            pending_scrape = total_urls - scraped_count
            pending_generation = scraped_count - generated_count

            # Get failed URLs from Redis or estimate
            failed_count = 0
            if self.use_redis and self.redis_client:
                try:
                    failed_count = self.redis_client.scard("pipeline:failed_urls") or 0
                except:
                    failed_count = 0

            return {
                "total_urls": total_urls,
                "scraped": scraped_count,
                "generated": generated_count,
                "pending_scrape": pending_scrape,
                "pending_generation": pending_generation,
                "failed": failed_count,
                "scraped_urls": list(scraped_urls),
                "generated_urls": list(generated_urls),
                "completion_percentage": round((generated_count / total_urls * 100) if total_urls > 0 else 0, 2)
            }
        except Exception as e:
            return {"error": str(e)}

    def get_resume_point(self, operation_mode="full"):
        """Determine where to resume based on operation mode"""
        state = self.get_pipeline_state()

        if operation_mode == "scrape_only":
            # Resume scraping from unscraped URLs
            master_file = self.base_dir / "data" / "master_data.csv"
            if master_file.exists():
                import pandas as pd
                df_master = pd.read_csv(master_file)
                all_urls = set(df_master['url'].tolist())
                scraped_urls = set(state.get('scraped_urls', []))
                pending_urls = list(all_urls - scraped_urls)
                return {
                    "operation": "scrape_only",
                    "pending_urls": pending_urls,
                    "total_pending": len(pending_urls),
                    "resume_from": "unscraped_urls"
                }

        elif operation_mode == "generate_only":
            # Resume generation from scraped but not generated URLs
            scraped_urls = set(state.get('scraped_urls', []))
            generated_urls = set(state.get('generated_urls', []))
            pending_urls = list(scraped_urls - generated_urls)
            return {
                "operation": "generate_only",
                "pending_urls": pending_urls,
                "total_pending": len(pending_urls),
                "resume_from": "scraped_not_generated"
            }

        else:  # full pipeline
            # Resume from the earliest incomplete stage
            scraped_urls = set(state.get('scraped_urls', []))
            generated_urls = set(state.get('generated_urls', []))

            master_file = self.base_dir / "data" / "master_data.csv"
            if master_file.exists():
                import pandas as pd
                df_master = pd.read_csv(master_file)
                all_urls = set(df_master['url'].tolist())

                unscraped_urls = list(all_urls - scraped_urls)
                ungenerated_urls = list(scraped_urls - generated_urls)

                return {
                    "operation": "full_pipeline",
                    "unscraped_urls": unscraped_urls,
                    "ungenerated_urls": ungenerated_urls,
                    "total_unscraped": len(unscraped_urls),
                    "total_ungenerated": len(ungenerated_urls),
                    "resume_from": "mixed_stages"
                }

        return {"error": "Invalid operation mode"}

    def mark_url_failed(self, url, error_message=""):
        """Mark a URL as failed"""
        if self.use_redis and self.redis_client:
            try:
                self.redis_client.sadd("pipeline:failed_urls", url)
                if error_message:
                    self.redis_client.hset("pipeline:failed_details", url, error_message)
            except Exception as e:
                print(f"Failed to mark URL as failed in Redis: {e}")
        else:
            # Store in CSV file
            failed_file = self.base_dir / "data" / "failed_urls.csv"
            import pandas as pd
            from datetime import datetime

            failed_data = {
                'url': url,
                'error_message': error_message,
                'failed_at': datetime.now().isoformat()
            }

            if failed_file.exists():
                df_failed = pd.read_csv(failed_file)
                # Avoid duplicates
                if url not in df_failed['url'].values:
                    df_failed = pd.concat([df_failed, pd.DataFrame([failed_data])], ignore_index=True)
                    df_failed.to_csv(failed_file, index=False)
            else:
                pd.DataFrame([failed_data]).to_csv(failed_file, index=False)

    def _read_csv_with_encoding_fallback(self, csv_file):
        """Try to read CSV with multiple encoding strategies"""
        import pandas as pd

        # List of encodings to try
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'utf-16']

        for encoding in encodings:
            try:
                print(f"Trying to read CSV with {encoding} encoding...")
                df = pd.read_csv(csv_file, encoding=encoding, on_bad_lines='skip', engine='python')
                print(f"Successfully read CSV with {encoding} encoding")
                return df
            except UnicodeDecodeError as e:
                print(f"Failed with {encoding}: {e}")
                continue
            except Exception as e:
                print(f"Other error with {encoding}: {e}")
                continue

        print("All encoding attempts failed")
        return None

    def _extract_urls_from_malformed_csv(self, csv_file):
        """Extract URLs from malformed CSV by reading line by line with encoding fallback"""
        urls = set()
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                print(f"Trying to read CSV line by line with {encoding} encoding...")
                with open(csv_file, 'r', encoding=encoding, errors='ignore') as f:
                    lines = f.readlines()

                # Skip header line
                for line_num, line in enumerate(lines[1:], start=2):
                    try:
                        # Try to extract the first field which should be the URL
                        if line.strip():
                            # Split by comma and take the first field
                            parts = line.split(',')
                            if parts and parts[0].strip():
                                url = parts[0].strip().strip('"')
                                if url.startswith('https://github.com/'):
                                    urls.add(url)
                    except Exception as e:
                        continue

                if urls:
                    print(f"Successfully extracted {len(urls)} URLs with {encoding} encoding")
                    break

            except Exception as e:
                print(f"Error reading CSV file line by line with {encoding}: {e}")
                continue

        return urls

    def _count_scraped_from_directory(self):
        """Last resort: try to count scraped URLs from directory structure or other files"""
        urls = set()
        try:
            # Check if there's a scraped data directory
            scraped_dir = self.base_dir / "scraped_data"
            if scraped_dir.exists():
                for file_path in scraped_dir.glob("*.json"):
                    # Extract URL from filename or content
                    try:
                        import json
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            data = json.load(f)
                            if isinstance(data, dict) and 'url' in data:
                                urls.add(data['url'])
                    except:
                        continue

            print(f"Found {len(urls)} URLs from directory scan")
        except Exception as e:
            print(f"Error scanning directory: {e}")

        return urls

class PipelineStatus:
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.progress_tracker = PipelineProgressTracker(base_dir)
        
    def get_scraping_status(self):
        """Get status of data scraping"""
        try:
            # Check if scraped data exists
            scraped_file = self.base_dir / "data" / "github_repos.csv"
            completed_file = self.base_dir / "data" / "completed_data.csv"
            
            status = {
                "scraped_data_exists": scraped_file.exists(),
                "completed_data_exists": completed_file.exists(),
                "scraped_count": 0,
                "completed_count": 0,
                "last_updated": None
            }
            
            if scraped_file.exists():
                df = pd.read_csv(scraped_file)
                status["scraped_count"] = len(df)
                status["last_updated"] = datetime.fromtimestamp(scraped_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            if completed_file.exists():
                df = pd.read_csv(completed_file)
                status["completed_count"] = len(df)
                
            return status
        except Exception as e:
            return {"error": str(e)}
    
    def get_content_generation_status(self):
        """Get status of content generation"""
        try:
            generated_file = self.base_dir / "data" / "generated_content.json"
            
            status = {
                "generated_content_exists": generated_file.exists(),
                "generated_count": 0,
                "last_updated": None,
                "file_size": 0
            }
            
            if generated_file.exists():
                with open(generated_file, 'r') as f:
                    data = json.load(f)
                    status["generated_count"] = len(data)
                    status["file_size"] = round(generated_file.stat().st_size / 1024 / 1024, 2)  # MB
                    status["last_updated"] = datetime.fromtimestamp(generated_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            
            return status
        except Exception as e:
            return {"error": str(e)}
    
    def get_system_status(self):
        """Get overall system status"""
        try:
            # Check if required directories exist
            required_dirs = ["data", "logs", "scrapper", "content_processor"]
            dir_status = {}
            
            for dir_name in required_dirs:
                dir_path = self.base_dir / dir_name
                dir_status[dir_name] = dir_path.exists()
            
            # Check log files
            log_dir = self.base_dir / "logs"
            log_files = []
            if log_dir.exists():
                log_files = [f.name for f in log_dir.glob("*.log")]
            
            return {
                "directories": dir_status,
                "log_files": log_files,
                "base_dir_exists": self.base_dir.exists()
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_recent_logs(self, lines=50):
        """Get recent log entries"""
        try:
            log_files = [
                self.base_dir / "logs" / "scraping.log",
                self.base_dir / "logs" / "content_generation.log"
            ]

            recent_logs = []
            for log_file in log_files:
                if log_file.exists():
                    try:
                        with open(log_file, 'r') as f:
                            log_lines = f.readlines()
                            recent_logs.extend([
                                {
                                    "file": log_file.name,
                                    "line": line.strip(),
                                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                }
                                for line in log_lines[-lines:]
                            ])
                    except Exception:
                        continue

            return recent_logs[-lines:]
        except Exception as e:
            return [{"error": str(e)}]

    def get_content_data(self):
        """Get combined content data from scraped and generated sources"""
        try:
            content_data = []

            # Load scraped data
            scraped_file = self.base_dir / "data" / "completed_data.csv"
            scraped_data = {}

            if scraped_file.exists():
                import csv
                with open(scraped_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        repo_url = row.get('repo_url', '')
                        if repo_url:
                            scraped_data[repo_url] = {
                                "repo_url": repo_url,
                                "slug": row.get('slug', ''),
                                "stars": row.get('stars', '0'),
                                "language": row.get('language', 'Unknown'),
                                "category": row.get('category', 'Other'),
                                "author": row.get('author', ''),
                                "description_short": row.get('description_short', ''),
                                "scraped_at": row.get('stored_at', ''),
                                "status": "scraped"
                            }

            # Load generated data
            generated_file = self.base_dir / "data" / "generated_content.json"
            generated_data = {}

            if generated_file.exists():
                with open(generated_file, 'r', encoding='utf-8') as f:
                    generated_items = json.load(f)
                    for item in generated_items:
                        content = item.get('content', {})
                        repo_url = content.get('github_url', '')
                        if repo_url:
                            generated_data[repo_url] = {
                                "id": content.get('id', ''),
                                "title": content.get('title', ''),
                                "repo_url": repo_url,
                                "github_repo": content.get('github_repo', ''),
                                "github_stars": content.get('github_stars', 0),
                                "language": content.get('language', 'Unknown'),
                                "generated_at": content.get('last_content_update', ''),
                                "status": "generated"
                            }

            # Combine data
            all_repos = set(scraped_data.keys()) | set(generated_data.keys())
            for repo_url in all_repos:
                scraped = scraped_data.get(repo_url, {})
                generated = generated_data.get(repo_url, {})

                combined_item = {
                    "repo_url": repo_url,
                    "slug": scraped.get('slug', generated.get('github_repo', '')),
                    "title": generated.get('title', scraped.get('slug', '').split('/')[-1] if scraped.get('slug') else ''),
                    "stars": generated.get('github_stars', self._parse_star_count(scraped.get('stars', '0'))),
                    "language": generated.get('language', scraped.get('language', 'Unknown')),
                    "category": scraped.get('category', 'Other'),
                    "categories": scraped.get('category', 'Other'),  # Add for compatibility
                    "author": scraped.get('author', ''),
                    "description": scraped.get('description_short', ''),
                    "scraped_at": scraped.get('scraped_at', ''),
                    "generated_at": generated.get('generated_at', ''),
                    "has_scraped": bool(scraped),
                    "has_generated": bool(generated),
                    "status": self._determine_status(scraped, generated),
                    "content_id": generated.get('id', self._generate_content_id(repo_url)),
                    # Add missing fields that frontend expects
                    "compatibility": {
                        "claude_desktop": True,
                        "cursor": True,
                        "vscode": True,
                        "windsurf": True
                    }
                }
                content_data.append(combined_item)

            # Sort by stars (descending)
            content_data.sort(key=lambda x: x['stars'], reverse=True)
            return content_data

        except Exception as e:
            return [{"error": str(e)}]

    def _parse_star_count(self, star_text):
        """Parse star count from various formats"""
        if not star_text:
            return 0
        try:
            # Clean the text - remove 'Star', newlines, and extra whitespace
            cleaned = str(star_text).replace('Star', '').replace('\n', '').strip()
            if not cleaned:
                return 0
            # Handle 'k' suffix (thousands)
            if cleaned.endswith('k'):
                number_part = cleaned[:-1]
                return int(float(number_part) * 1000)
            # Handle 'M' suffix (millions)
            elif cleaned.endswith('M'):
                number_part = cleaned[:-1]
                return int(float(number_part) * 1000000)
            # Handle regular numbers (remove commas)
            else:
                return int(cleaned.replace(',', ''))
        except (ValueError, TypeError):
            return 0

    def _determine_status(self, scraped, generated):
        """Determine the overall status of content"""
        if generated:
            return "generated"
        elif scraped:
            return "scraped"
        else:
            return "unknown"

    def _generate_content_id(self, repo_url):
        """Generate a content ID from repo URL"""
        if not repo_url:
            return "unknown"
        # Extract owner/repo from URL
        parts = repo_url.replace('https://github.com/', '').split('/')
        if len(parts) >= 2:
            return f"{parts[0]}-{parts[1]}"
        return repo_url.replace('/', '-').replace(':', '-')

    def get_content_by_id(self, content_id):
        """Get detailed content data by ID"""
        try:
            # Load generated content
            generated_file = self.base_dir / "data" / "generated_content.json"
            if generated_file.exists():
                with open(generated_file, 'r', encoding='utf-8') as f:
                    generated_items = json.load(f)
                    for item in generated_items:
                        content = item.get('content', {})
                        if content.get('id') == content_id:
                            return content

            # If not found in generated, try to find in scraped data
            content_data = self.get_content_data()
            for item in content_data:
                if item.get('content_id') == content_id:
                    return item

            return {"error": "Content not found"}
        except Exception as e:
            return {"error": str(e)}

    def get_github_urls_from_content_ids(self, content_ids):
        """Convert content IDs to GitHub URLs"""
        try:
            github_urls = []
            content_data = self.get_content_data()

            for content_id in content_ids:
                for item in content_data:
                    if item.get('content_id') == content_id:
                        repo_url = item.get('repo_url')
                        if repo_url:
                            github_urls.append(repo_url)
                        break

            return github_urls
        except Exception:
            return []

    def add_urls_to_master_data(self, github_urls):
        """Add GitHub URLs to master_data.csv if they don't exist"""
        try:
            import pandas as pd
            from datetime import datetime

            master_file = self.base_dir / "data" / "master_data.csv"

            # Read existing master data or create new
            if master_file.exists():
                df_master = pd.read_csv(master_file)
            else:
                df_master = pd.DataFrame(columns=['url', 'stored_at', 'is_scrapped'])

            # Add new URLs that don't exist
            new_rows = []
            existing_urls = set(df_master['url'].tolist()) if not df_master.empty else set()

            for url in github_urls:
                if url not in existing_urls:
                    new_rows.append({
                        'url': url,
                        'stored_at': datetime.now().isoformat(),
                        'is_scrapped': False
                    })

            if new_rows:
                df_new = pd.DataFrame(new_rows)
                df_master = pd.concat([df_master, df_new], ignore_index=True)
                df_master = df_master.sort_values('stored_at', ascending=False)
                df_master.to_csv(master_file, index=False)

            return len(new_rows)
        except Exception:
            return 0

pipeline_status = PipelineStatus()

@app.route('/')
def dashboard():
    """Enhanced main dashboard page"""
    return render_template('enhanced_dashboard.html')

@app.route('/pipeline')
def pipeline_dashboard():
    """Enhanced pipeline management dashboard"""
    return render_template('pipeline_dashboard.html')

@app.route('/content')
def content_management():
    """Content management page"""
    return render_template('content_management.html')

@app.route('/content/<content_id>')
def content_details(content_id):
    """Content details page"""
    return render_template('content_details.html', content_id=content_id)

@app.route('/config-generator')
def config_generator():
    """MCP Configuration Generator"""
    return render_template('configuration_generator.html')

@app.route('/api/status')
def api_status():
    """API endpoint for status data"""
    return jsonify({
        "scraping": pipeline_status.get_scraping_status(),
        "content_generation": pipeline_status.get_content_generation_status(),
        "system": pipeline_status.get_system_status(),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.route('/api/logs')
def api_logs():
    """API endpoint for recent logs"""
    lines = request.args.get('lines', 50, type=int)
    return jsonify({
        "logs": pipeline_status.get_recent_logs(lines),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.route('/api/content')
def api_content():
    """API endpoint for content data"""
    return jsonify({
        "content": pipeline_status.get_content_data(),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.route('/api/content/<content_id>')
def api_content_details(content_id):
    """API endpoint for specific content details"""
    return jsonify({
        "content": pipeline_status.get_content_by_id(content_id),
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@app.route('/api/run_scraping', methods=['POST'])
def run_scraping():
    """Trigger scraping process with various options"""
    try:
        data = request.get_json() or {}

        # Build command with options
        cmd = ['python', 'scrapper/scripts/main.py']

        # Add flags based on request
        if data.get('extract'): cmd.append('--extract')
        if data.get('dedup'): cmd.append('--dedup')
        if data.get('scrape'): cmd.append('--scrape')
        if data.get('automated'): cmd.append('--automated')
        if data.get('fresh_start'): cmd.append('--fresh-start')
        if data.get('force_rescrape'): cmd.append('--force-rescrape')
        if data.get('output_final_only'): cmd.append('--output-final-only')

        # Add parameters
        if data.get('max_repos'): cmd.extend(['--max-repos', str(data['max_repos'])])
        if data.get('delay'): cmd.extend(['--delay', str(data['delay'])])
        if data.get('mode'): cmd.extend(['--mode', data['mode']])
        if data.get('extract_url'): cmd.extend(['--extract-url', data['extract_url']])
        if data.get('max_depth'): cmd.extend(['--max-depth', str(data['max_depth'])])
        if data.get('update_old'): cmd.extend(['--update-old', str(data['update_old'])])

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600, cwd=pipeline_status.base_dir)

        return jsonify({
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr,
            "command": ' '.join(cmd)
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/run_content_generation', methods=['POST'])
def run_content_generation():
    """Trigger content generation process with options"""
    try:
        data = request.get_json() or {}

        # Build command with options
        cmd = ['python', 'run_content_generation.py']

        # Add parameters
        if data.get('max_repos'): cmd.extend(['--max-repos', str(data['max_repos'])])
        if data.get('batch_size'): cmd.extend(['--batch-size', str(data['batch_size'])])
        if data.get('input_file'): cmd.extend(['--input-file', data['input_file']])
        if data.get('output_file'): cmd.extend(['--output-file', data['output_file']])

        # Add flags
        if data.get('event_driven'): cmd.append('--event-driven')
        if data.get('check_only'): cmd.append('--check-only')

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=900, cwd=pipeline_status.base_dir)

        return jsonify({
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr,
            "command": ' '.join(cmd)
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/run_continuous_scraping', methods=['POST'])
def run_continuous_scraping():
    """Start continuous scraping mode"""
    try:
        data = request.get_json() or {}
        interval_hours = data.get('interval_hours', 6)

        cmd = ['python', 'scrapper/scripts/main.py', '--continuous', '--interval-hours', str(interval_hours)]

        # Run in background
        result = subprocess.Popen(cmd, cwd=pipeline_status.base_dir)

        return jsonify({
            "success": True,
            "message": f"Continuous scraping started with {interval_hours}h intervals",
            "pid": result.pid
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/cleanup_data', methods=['POST'])
def cleanup_data():
    """Clean up data files"""
    try:
        data = request.get_json() or {}

        if data.get('fresh_start'):
            cmd = ['python', 'scrapper/scripts/main.py', '--fresh-start']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, cwd=pipeline_status.base_dir)

            return jsonify({
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "message": "Fresh start cleanup completed"
            })

        return jsonify({"success": False, "error": "No cleanup action specified"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/process_repositories', methods=['POST'])
def process_repositories():
    """Smart repository processing using existing CLI infrastructure"""
    try:
        data = request.get_json() or {}

        # Extract parameters
        github_urls = data.get('github_urls', [])
        content_ids = data.get('content_ids', [])
        operation_mode = data.get('mode', 'regenerate')  # regenerate, fresh_start, resume
        force_rescrape = data.get('force_rescrape', False)
        max_repos = data.get('max_repos', None)
        batch_size = data.get('batch_size', 3)

        if not github_urls and not content_ids:
            return jsonify({"success": False, "error": "No GitHub URLs or content IDs provided"})

        # Convert content IDs to GitHub URLs if needed
        if content_ids and not github_urls:
            github_urls = pipeline_status.get_github_urls_from_content_ids(content_ids)

        if not github_urls:
            return jsonify({"success": False, "error": "Could not resolve GitHub URLs from provided data"})

        # Validate and normalize GitHub URLs
        valid_urls = []
        for url in github_urls:
            if 'github.com' in url and '/' in url:
                # Normalize URL format
                if not url.startswith('https://'):
                    if url.startswith('github.com/'):
                        url = f"https://{url}"
                    elif '/' in url and not url.startswith('http'):
                        url = f"https://github.com/{url}"
                valid_urls.append(url)

        if not valid_urls:
            return jsonify({"success": False, "error": "No valid GitHub URLs provided"})

        # Create temporary CSV file with target URLs (mimicking master_data.csv format)
        import tempfile
        import csv
        from datetime import datetime

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, dir=pipeline_status.base_dir / "data")
        temp_file_path = temp_file.name

        # Write URLs in the format expected by the scrapper
        with open(temp_file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            # Use the same format as master_data.csv
            writer.writerow(['url', 'stored_at', 'is_scrapped'])
            for url in valid_urls:
                writer.writerow([url, datetime.now().isoformat(), False])

        # Build CLI commands using existing infrastructure
        commands = []

        # Step 1: Handle scraping based on operation mode
        if operation_mode == 'fresh_start':
            # Fresh start: force re-scrape everything
            scrape_cmd = [
                'python', 'scrapper/scripts/main.py',
                '--scrape',
                '--force-rescrape',
                '--max-repos', str(len(valid_urls)),
                '--output-final-only'
            ]
            # Temporarily replace master_data.csv with our target URLs
            import shutil
            master_backup = None
            master_file = pipeline_status.base_dir / "data" / "master_data.csv"
            if master_file.exists():
                master_backup = str(master_file) + '.backup'
                shutil.copy(str(master_file), master_backup)
            shutil.copy(temp_file_path, str(master_file))
            commands.append(('scraping', scrape_cmd, master_backup))

        elif operation_mode == 'regenerate' and force_rescrape:
            # Regenerate with fresh scraping
            scrape_cmd = [
                'python', 'scrapper/scripts/main.py',
                '--scrape',
                '--force-rescrape',
                '--max-repos', str(len(valid_urls))
            ]
            # Add our URLs to existing master_data.csv
            pipeline_status.add_urls_to_master_data(valid_urls)
            commands.append(('scraping', scrape_cmd, None))

        # Step 2: Content Generation (always run) with URL filtering
        generation_cmd = [
            'python', 'run_content_generation.py',
            '--filter-urls'
        ] + valid_urls + [
            '--batch-size', str(batch_size)
        ]
        commands.append(('generation', generation_cmd, None))

        # Execute pipeline
        results = []
        background_processes = []

        for step_name, cmd, backup_file in commands:
            try:
                if len(valid_urls) > 3 or data.get('background', False):
                    # Run in background for large operations
                    process = subprocess.Popen(cmd, cwd=pipeline_status.base_dir,
                                             stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    background_processes.append({
                        "step": step_name,
                        "process_id": process.pid,
                        "command": ' '.join(cmd),
                        "backup_file": backup_file
                    })
                else:
                    # Run synchronously for small operations
                    result = subprocess.run(cmd, capture_output=True, text=True,
                                          timeout=600, cwd=pipeline_status.base_dir)
                    results.append({
                        "step": step_name,
                        "success": result.returncode == 0,
                        "output": result.stdout,
                        "error": result.stderr,
                        "command": ' '.join(cmd)
                    })

                    # Restore backup if needed
                    if backup_file and os.path.exists(backup_file):
                        shutil.move(backup_file, str(pipeline_status.base_dir / "data" / "master_data.csv"))

            except Exception as e:
                # Restore backup on error
                if backup_file and os.path.exists(backup_file):
                    shutil.move(backup_file, str(pipeline_status.base_dir / "data" / "master_data.csv"))
                raise e

        # Cleanup temporary file
        try:
            os.unlink(temp_file_path)
        except:
            pass

        # Return results
        if background_processes:
            return jsonify({
                "success": True,
                "message": f"Processing pipeline started for {len(valid_urls)} repositories",
                "mode": operation_mode,
                "repositories": valid_urls,
                "background_processes": background_processes,
                "background": True
            })
        else:
            return jsonify({
                "success": all(r.get('success', False) for r in results),
                "message": f"Processing completed for {len(valid_urls)} repositories",
                "mode": operation_mode,
                "repositories": valid_urls,
                "results": results,
                "background": False
            })

    except subprocess.TimeoutExpired:
        return jsonify({
            "success": False,
            "error": "Processing timed out. Some processes may still be running in background.",
            "message": "Check logs for progress updates."
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/regenerate_content', methods=['POST'])
def regenerate_content():
    """Legacy endpoint - redirects to smart processing"""
    data = request.get_json() or {}
    data['mode'] = 'regenerate'
    return process_repositories()

@app.route('/api/scrape_and_generate', methods=['POST'])
def scrape_and_generate():
    """Scrape specific GitHub URLs and generate content using CLI"""
    try:
        data = request.get_json() or {}
        github_urls = data.get('github_urls', [])

        if not github_urls:
            return jsonify({"success": False, "error": "No GitHub URLs provided"})

        # Validate and normalize URLs
        valid_urls = []
        for url in github_urls:
            if 'github.com' in url and '/' in url:
                if not url.startswith('https://'):
                    if url.startswith('github.com/'):
                        url = f"https://{url}"
                    elif '/' in url and not url.startswith('http'):
                        url = f"https://github.com/{url}"
                valid_urls.append(url)

        if not valid_urls:
            return jsonify({"success": False, "error": "No valid GitHub URLs provided"})

        # Add URLs to master_data.csv
        added_count = pipeline_status.add_urls_to_master_data(valid_urls)

        # Build CLI commands
        commands = []

        # Step 1: Scrape the new URLs
        scrape_cmd = [
            'python', 'scrapper/scripts/main.py',
            '--scrape',
            '--force-rescrape',
            '--max-repos', str(len(valid_urls)),
            '--output-final-only'
        ]
        commands.append(('scraping', scrape_cmd))

        # Step 2: Generate content with URL filtering
        generation_cmd = [
            'python', 'run_content_generation.py',
            '--filter-urls'
        ] + valid_urls + [
            '--batch-size', str(min(3, len(valid_urls)))
        ]
        commands.append(('generation', generation_cmd))

        # Execute pipeline
        background_processes = []

        for step_name, cmd in commands:
            if len(valid_urls) > 1 or data.get('background', True):
                # Run in background
                process = subprocess.Popen(cmd, cwd=pipeline_status.base_dir,
                                         stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                background_processes.append({
                    "step": step_name,
                    "process_id": process.pid,
                    "command": ' '.join(cmd)
                })
            else:
                # Run synchronously for single URL
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      timeout=300, cwd=pipeline_status.base_dir)
                if result.returncode != 0:
                    return jsonify({
                        "success": False,
                        "error": f"Step {step_name} failed: {result.stderr}",
                        "command": ' '.join(cmd)
                    })

        if background_processes:
            return jsonify({
                "success": True,
                "message": f"Scraping and content generation started for {len(valid_urls)} repositories",
                "repositories": valid_urls,
                "added_to_master": added_count,
                "background_processes": background_processes,
                "background": True
            })
        else:
            return jsonify({
                "success": True,
                "message": f"Scraping and content generation completed for {len(valid_urls)} repositories",
                "repositories": valid_urls,
                "added_to_master": added_count,
                "background": False
            })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/enhanced_pipeline', methods=['POST'])
def enhanced_pipeline():
    """Enhanced pipeline operations with smart resume and progress tracking"""
    try:
        data = request.get_json() or {}
        operation_mode = data.get('operation_mode', 'full_pipeline')  # full_pipeline, scrape_only, generate_only, smart_resume
        batch_size = data.get('batch_size', 3)
        max_urls = data.get('max_urls', None)
        force_restart = data.get('force_restart', False)
        use_redis = data.get('use_redis', False)

        # Initialize enhanced progress tracker
        tracker = PipelineProgressTracker(pipeline_status.base_dir, use_redis=use_redis)

        # Get current pipeline state
        pipeline_state = tracker.get_pipeline_state()

        commands = []
        background_processes = []

        if operation_mode == 'smart_resume':
            # Intelligent resume based on current state
            resume_info = tracker.get_resume_point("full")

            unscraped_urls = resume_info.get('unscraped_urls', [])
            ungenerated_urls = resume_info.get('ungenerated_urls', [])

            if unscraped_urls:
                # Create temporary file with unscraped URLs
                import tempfile, csv
                temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False,
                                                     dir=pipeline_status.base_dir / "data")
                with open(temp_file.name, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['url', 'stored_at', 'is_scrapped'])
                    for url in unscraped_urls[:max_urls] if max_urls else unscraped_urls:
                        writer.writerow([url, datetime.now().isoformat(), False])

                # Backup and replace master_data.csv temporarily
                import shutil
                master_file = pipeline_status.base_dir / "data" / "master_data.csv"
                backup_file = str(master_file) + '.backup'
                if master_file.exists():
                    shutil.copy(str(master_file), backup_file)
                shutil.copy(temp_file.name, str(master_file))

                scrape_cmd = [
                    'python', 'scrapper/scripts/main.py',
                    '--scrape',
                    '--max-repos', str(len(unscraped_urls[:max_urls] if max_urls else unscraped_urls))
                ]
                commands.append(('smart_resume_scraping', scrape_cmd, backup_file, temp_file.name))

            if ungenerated_urls:
                # Generate content for scraped but not generated URLs
                generation_cmd = [
                    'python', 'run_content_generation.py',
                    '--filter-urls'
                ] + (ungenerated_urls[:max_urls] if max_urls else ungenerated_urls) + [
                    '--batch-size', str(batch_size)
                ]
                commands.append(('smart_resume_generation', generation_cmd, None, None))

        elif operation_mode == 'full_pipeline':
            if force_restart:
                # Fresh start
                scrape_cmd = [
                    'python', 'scrapper/scripts/main.py',
                    '--automated',
                    '--fresh-start',
                    '--force-rescrape'
                ]
                if max_urls:
                    scrape_cmd.extend(['--max-repos', str(max_urls)])
                commands.append(('full_pipeline_scraping', scrape_cmd, None, None))

                generation_cmd = [
                    'python', 'run_content_generation.py',
                    '--batch-size', str(batch_size)
                ]
                if max_urls:
                    generation_cmd.extend(['--max-repos', str(max_urls)])
                commands.append(('full_pipeline_generation', generation_cmd, None, None))
            else:
                # Smart resume for full pipeline
                return enhanced_pipeline_smart_resume(data)

        elif operation_mode == 'scrape_only':
            resume_info = tracker.get_resume_point("scrape_only")
            pending_urls = resume_info.get('pending_urls', [])

            if pending_urls:
                scrape_cmd = [
                    'python', 'scrapper/scripts/main.py',
                    '--scrape',
                    '--max-repos', str(len(pending_urls[:max_urls] if max_urls else pending_urls))
                ]
                commands.append(('scrape_only', scrape_cmd, None, None))

        elif operation_mode == 'generate_only':
            resume_info = tracker.get_resume_point("generate_only")
            pending_urls = resume_info.get('pending_urls', [])

            if pending_urls:
                generation_cmd = [
                    'python', 'run_content_generation.py',
                    '--filter-urls'
                ] + (pending_urls[:max_urls] if max_urls else pending_urls) + [
                    '--batch-size', str(batch_size)
                ]
                commands.append(('generate_only', generation_cmd, None, None))

        # Execute commands
        for step_name, cmd, backup_file, temp_file in commands:
            try:
                process = subprocess.Popen(cmd, cwd=pipeline_status.base_dir,
                                         stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                background_processes.append({
                    "step": step_name,
                    "process_id": process.pid,
                    "command": ' '.join(cmd),
                    "backup_file": backup_file,
                    "temp_file": temp_file
                })
            except Exception as e:
                # Restore backup if needed
                if backup_file and os.path.exists(backup_file):
                    shutil.move(backup_file, str(pipeline_status.base_dir / "data" / "master_data.csv"))
                raise e

        return jsonify({
            "success": True,
            "message": f"Enhanced pipeline '{operation_mode}' started",
            "operation_mode": operation_mode,
            "pipeline_state": pipeline_state,
            "background_processes": background_processes,
            "commands_executed": len(commands)
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

def enhanced_pipeline_smart_resume(data):
    """Helper function for smart resume logic"""
    # This would contain the smart resume logic
    # For now, redirect to the smart_resume mode
    data['operation_mode'] = 'smart_resume'
    return enhanced_pipeline()

@app.route('/api/pipeline_status', methods=['GET'])
def get_pipeline_status():
    """Get detailed pipeline status and progress"""
    try:
        use_redis = request.args.get('use_redis', 'false').lower() == 'true'
        tracker = PipelineProgressTracker(pipeline_status.base_dir, use_redis=use_redis)

        pipeline_state = tracker.get_pipeline_state()
        resume_points = {
            'full_pipeline': tracker.get_resume_point('full'),
            'scrape_only': tracker.get_resume_point('scrape_only'),
            'generate_only': tracker.get_resume_point('generate_only')
        }

        return jsonify({
            "success": True,
            "pipeline_state": pipeline_state,
            "resume_points": resume_points,
            "storage_mode": "redis" if use_redis else "csv",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/bulk_operations', methods=['POST'])
def bulk_operations():
    """Legacy bulk operations - redirects to enhanced pipeline"""
    try:
        data = request.get_json() or {}
        operation = data.get('operation', 'regenerate')

        # Map legacy operations to enhanced pipeline modes
        operation_mapping = {
            'fresh_start': {'operation_mode': 'full_pipeline', 'force_restart': True},
            'resume': {'operation_mode': 'smart_resume'},
            'regenerate_all': {'operation_mode': 'full_pipeline', 'force_restart': True}
        }

        if operation in operation_mapping:
            enhanced_data = operation_mapping[operation]
            enhanced_data.update(data)  # Merge with original data
            return enhanced_pipeline()
        else:
            return jsonify({"success": False, "error": f"Unknown operation: {operation}"})

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/failed_urls', methods=['GET'])
def get_failed_urls():
    """Get list of failed URLs with error details"""
    try:
        use_redis = request.args.get('use_redis', 'false').lower() == 'true'
        tracker = PipelineProgressTracker(pipeline_status.base_dir, use_redis=use_redis)

        failed_urls = []

        if use_redis and tracker.redis_client:
            try:
                # Get failed URLs from Redis
                failed_set = tracker.redis_client.smembers("pipeline:failed_urls")
                for url in failed_set:
                    error_msg = tracker.redis_client.hget("pipeline:failed_details", url) or "Unknown error"
                    failed_urls.append({"url": url, "error": error_msg})
            except Exception as e:
                return jsonify({"success": False, "error": f"Redis error: {e}"})
        else:
            # Get failed URLs from CSV
            failed_file = pipeline_status.base_dir / "data" / "failed_urls.csv"
            if failed_file.exists():
                import pandas as pd
                df_failed = pd.read_csv(failed_file)
                failed_urls = df_failed.to_dict('records')

        return jsonify({
            "success": True,
            "failed_urls": failed_urls,
            "total_failed": len(failed_urls),
            "storage_mode": "redis" if use_redis else "csv"
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/retry_failed', methods=['POST'])
def retry_failed_urls():
    """Retry processing failed URLs"""
    try:
        data = request.get_json() or {}
        use_redis = data.get('use_redis', False)
        operation_mode = data.get('operation_mode', 'full_pipeline')
        batch_size = data.get('batch_size', 3)

        tracker = PipelineProgressTracker(pipeline_status.base_dir, use_redis=use_redis)

        # Get failed URLs
        failed_urls = []
        if use_redis and tracker.redis_client:
            failed_urls = list(tracker.redis_client.smembers("pipeline:failed_urls"))
        else:
            failed_file = pipeline_status.base_dir / "data" / "failed_urls.csv"
            if failed_file.exists():
                import pandas as pd
                df_failed = pd.read_csv(failed_file)
                failed_urls = df_failed['url'].tolist()

        if not failed_urls:
            return jsonify({"success": False, "error": "No failed URLs to retry"})

        # Create retry operation
        retry_config = {
            "operation_mode": operation_mode,
            "batch_size": batch_size,
            "use_redis": use_redis,
            "retry_urls": failed_urls
        }

        # Clear failed URLs before retry
        if use_redis and tracker.redis_client:
            tracker.redis_client.delete("pipeline:failed_urls", "pipeline:failed_details")
        else:
            failed_file = pipeline_status.base_dir / "data" / "failed_urls.csv"
            if failed_file.exists():
                failed_file.unlink()

        # Start retry operation (similar to enhanced_pipeline but with specific URLs)
        commands = []
        if operation_mode in ['full_pipeline', 'scrape_only']:
            scrape_cmd = [
                'python', 'scrapper/scripts/main.py',
                '--scrape',
                '--max-repos', str(len(failed_urls))
            ]
            commands.append(('retry_scraping', scrape_cmd, None, None))

        if operation_mode in ['full_pipeline', 'generate_only']:
            generation_cmd = [
                'python', 'run_content_generation.py',
                '--filter-urls'
            ] + failed_urls + [
                '--batch-size', str(batch_size)
            ]
            commands.append(('retry_generation', generation_cmd, None, None))

        # Execute commands
        background_processes = []
        for step_name, cmd, backup_file, temp_file in commands:
            process = subprocess.Popen(cmd, cwd=pipeline_status.base_dir,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            background_processes.append({
                "step": step_name,
                "process_id": process.pid,
                "command": ' '.join(cmd)
            })

        return jsonify({
            "success": True,
            "message": f"Retrying {len(failed_urls)} failed URLs",
            "failed_urls_count": len(failed_urls),
            "operation_mode": operation_mode,
            "background_processes": background_processes
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/selective_processing', methods=['POST'])
def selective_processing():
    """Process specific URLs or URL ranges"""
    try:
        data = request.get_json() or {}
        urls = data.get('urls', [])
        url_pattern = data.get('url_pattern', '')
        operation_mode = data.get('operation_mode', 'full_pipeline')
        batch_size = data.get('batch_size', 3)

        if not urls and not url_pattern:
            return jsonify({"success": False, "error": "No URLs or URL pattern provided"})

        # If pattern is provided, find matching URLs
        if url_pattern and not urls:
            master_file = pipeline_status.base_dir / "data" / "master_data.csv"
            if master_file.exists():
                import pandas as pd
                df_master = pd.read_csv(master_file)
                # Simple pattern matching (can be enhanced with regex)
                matching_urls = df_master[df_master['url'].str.contains(url_pattern, case=False, na=False)]['url'].tolist()
                urls = matching_urls

        if not urls:
            return jsonify({"success": False, "error": "No matching URLs found"})

        # Process selected URLs
        commands = []
        if operation_mode in ['full_pipeline', 'scrape_only']:
            # Create temporary master file with selected URLs
            import tempfile, csv
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False,
                                                 dir=pipeline_status.base_dir / "data")
            with open(temp_file.name, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['url', 'stored_at', 'is_scrapped'])
                for url in urls:
                    writer.writerow([url, datetime.now().isoformat(), False])

            scrape_cmd = [
                'python', 'scrapper/scripts/main.py',
                '--scrape',
                '--max-repos', str(len(urls))
            ]
            commands.append(('selective_scraping', scrape_cmd, None, temp_file.name))

        if operation_mode in ['full_pipeline', 'generate_only']:
            generation_cmd = [
                'python', 'run_content_generation.py',
                '--filter-urls'
            ] + urls + [
                '--batch-size', str(batch_size)
            ]
            commands.append(('selective_generation', generation_cmd, None, None))

        # Execute commands
        background_processes = []
        for step_name, cmd, backup_file, temp_file in commands:
            process = subprocess.Popen(cmd, cwd=pipeline_status.base_dir,
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            background_processes.append({
                "step": step_name,
                "process_id": process.pid,
                "command": ' '.join(cmd)
            })

        return jsonify({
            "success": True,
            "message": f"Processing {len(urls)} selected URLs",
            "selected_urls": urls,
            "operation_mode": operation_mode,
            "background_processes": background_processes
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/data_consistency_check', methods=['GET'])
def data_consistency_check():
    """Check and optionally repair data consistency issues"""
    try:
        use_redis = request.args.get('use_redis', 'false').lower() == 'true'
        repair = request.args.get('repair', 'false').lower() == 'true'

        tracker = PipelineProgressTracker(pipeline_status.base_dir, use_redis=use_redis)
        state = tracker.get_pipeline_state()

        issues = []
        repairs_made = []

        # Check for negative pending values
        if state.get('pending_generation', 0) < 0:
            issues.append("Negative pending generation count detected")
            if repair:
                # This is handled in the progress tracker now
                repairs_made.append("Fixed negative pending generation count")

        # Check for data consistency warning
        if state.get('data_consistency_warning', False):
            issues.append("Generated content count exceeds scraped content count")
            if repair:
                repairs_made.append("Adjusted counts to maintain logical consistency")

        # Check for malformed CSV files
        try:
            import pandas as pd
            completed_file = pipeline_status.base_dir / "data" / "completed_data.csv"
            if completed_file.exists():
                try:
                    # Try reading with standard method first
                    df = pd.read_csv(completed_file, encoding='utf-8')
                except Exception as e:
                    issues.append(f"Malformed completed_data.csv: {str(e)}")
                    if repair:
                        # Try to repair the CSV with encoding fallback
                        try:
                            tracker = PipelineProgressTracker(pipeline_status.base_dir, use_redis=use_redis)
                            df_repaired = tracker._read_csv_with_encoding_fallback(completed_file)

                            if df_repaired is not None:
                                # Backup original file
                                backup_file = str(completed_file) + '.backup'
                                import shutil
                                shutil.copy(str(completed_file), backup_file)

                                # Save repaired version with UTF-8 encoding
                                df_repaired.to_csv(completed_file, index=False, encoding='utf-8')
                                repairs_made.append(f"Repaired completed_data.csv with encoding fix (backup saved as {backup_file})")
                            else:
                                # Try extracting URLs and creating a minimal CSV
                                urls = tracker._extract_urls_from_malformed_csv(completed_file)
                                if urls:
                                    # Create a minimal CSV with just URLs
                                    backup_file = str(completed_file) + '.backup'
                                    import shutil
                                    shutil.copy(str(completed_file), backup_file)

                                    # Create new CSV with extracted URLs
                                    import pandas as pd
                                    from datetime import datetime
                                    df_minimal = pd.DataFrame({
                                        'repo_url': list(urls),
                                        'stored_at': [datetime.now().isoformat()] * len(urls)
                                    })
                                    df_minimal.to_csv(completed_file, index=False, encoding='utf-8')
                                    repairs_made.append(f"Created minimal CSV with {len(urls)} extracted URLs (backup saved as {backup_file})")
                                else:
                                    repairs_made.append("Could not extract URLs from corrupted CSV")
                        except Exception as repair_error:
                            repairs_made.append(f"Failed to repair CSV: {str(repair_error)}")
        except ImportError:
            issues.append("Pandas not available for CSV validation")

        return jsonify({
            "success": True,
            "issues_found": len(issues),
            "issues": issues,
            "repairs_attempted": repair,
            "repairs_made": repairs_made,
            "current_state": state
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/system_info')
def system_info():
    """Get detailed system information"""
    try:
        info = {
            "python_version": subprocess.run(['python', '--version'], capture_output=True, text=True).stdout.strip(),
            "disk_usage": {},
            "process_count": 0,
            "memory_usage": "N/A"
        }

        # Get disk usage for data directory
        data_dir = pipeline_status.base_dir / "data"
        if data_dir.exists():
            total_size = sum(f.stat().st_size for f in data_dir.rglob('*') if f.is_file())
            info["disk_usage"]["data_dir_mb"] = round(total_size / 1024 / 1024, 2)

        # Check Redis availability
        try:
            import redis
            redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
            redis_client.ping()
            info["redis_available"] = True
            info["redis_status"] = "Connected"
        except Exception:
            info["redis_available"] = False
            info["redis_status"] = "Not available"

        return jsonify(info)
    except Exception as e:
        return jsonify({"error": str(e)})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    templates_dir = Path("templates")
    templates_dir.mkdir(exist_ok=True)
    
    print("🚀 Starting MCP Pipeline Admin Panel...")
    print("📊 Dashboard will be available at: http://localhost:9000")
    print("🔄 Auto-refresh every 30 seconds")

    app.run(debug=True, host='0.0.0.0', port=9000)
