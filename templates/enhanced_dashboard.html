<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Enhanced MCP Pipeline Admin Panel</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        color: white;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      }

      .card h2 {
        color: #4a5568;
        margin-bottom: 15px;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
      }

      .status-good {
        background-color: #48bb78;
      }
      .status-warning {
        background-color: #ed8936;
      }
      .status-error {
        background-color: #f56565;
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background-color: #e2e8f0;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4299e1, #3182ce);
        transition: width 0.5s ease;
        border-radius: 10px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .stat-item {
        text-align: center;
        padding: 15px;
        background: #f7fafc;
        border-radius: 10px;
      }

      .stat-number {
        font-size: 1.8rem;
        font-weight: bold;
        color: #2d3748;
      }

      .stat-label {
        font-size: 0.9rem;
        color: #718096;
        margin-top: 5px;
      }

      .operation-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
      }

      .btn {
        padding: 15px 25px;
        border: none;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
      }

      .btn-success {
        background: linear-gradient(135deg, #48bb78, #38a169);
        color: white;
      }

      .btn-warning {
        background: linear-gradient(135deg, #ed8936, #dd6b20);
        color: white;
      }

      .btn-danger {
        background: linear-gradient(135deg, #f56565, #e53e3e);
        color: white;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .settings-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-top: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #4a5568;
      }

      .form-group input,
      .form-group select {
        width: 100%;
        padding: 12px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
      }

      .form-group input:focus,
      .form-group select:focus {
        outline: none;
        border-color: #4299e1;
      }

      .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .loading {
        display: none;
        text-align: center;
        padding: 20px;
      }

      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .alert {
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        font-weight: 500;
      }

      .alert-success {
        background-color: #c6f6d5;
        color: #22543d;
        border: 1px solid #9ae6b4;
      }

      .alert-error {
        background-color: #fed7d7;
        color: #742a2a;
        border: 1px solid #fc8181;
      }

      .alert-info {
        background-color: #bee3f8;
        color: #2a4365;
        border: 1px solid #90cdf4;
      }

      .hidden {
        display: none;
      }

      .logs-container {
        background: #1a202c;
        color: #e2e8f0;
        border-radius: 10px;
        padding: 20px;
        font-family: "Courier New", monospace;
        font-size: 0.9rem;
        max-height: 300px;
        overflow-y: auto;
        margin-top: 15px;
      }

      .log-entry {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 3px;
      }

      .log-info {
        background-color: rgba(66, 153, 225, 0.1);
      }
      .log-warning {
        background-color: rgba(237, 137, 54, 0.1);
      }
      .log-error {
        background-color: rgba(245, 101, 101, 0.1);
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .dashboard-grid {
          grid-template-columns: 1fr;
        }

        .operation-buttons {
          grid-template-columns: 1fr;
        }

        .form-row {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🚀 Enhanced MCP Pipeline Admin Panel</h1>
        <p>
          Intelligent automation for scraping and content generation with smart
          resume capabilities
        </p>
      </div>

      <div class="dashboard-grid">
        <!-- Pipeline Status Card -->
        <div class="card">
          <h2>
            <span
              class="status-indicator status-good"
              id="pipeline-status-indicator"
            ></span>
            📊 Pipeline Status
          </h2>
          <div id="pipeline-status">
            <div class="loading">
              <div class="spinner"></div>
              <p>Loading pipeline status...</p>
            </div>
          </div>
        </div>

        <!-- Progress Overview Card -->
        <div class="card">
          <h2>📈 Progress Overview</h2>
          <div id="progress-overview">
            <div class="progress-bar">
              <div
                class="progress-fill"
                id="overall-progress"
                style="width: 0%"
              ></div>
            </div>
            <div class="stats-grid" id="progress-stats">
              <!-- Progress stats will be loaded here -->
            </div>
          </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="card">
          <h2>⚡ Quick Actions</h2>
          <div class="operation-buttons">
            <button class="btn btn-primary" onclick="startSmartResume()">
              🔄 Smart Resume
            </button>
            <button class="btn btn-success" onclick="startFullPipeline()">
              🚀 Full Pipeline
            </button>
            <button class="btn btn-warning" onclick="startScrapeOnly()">
              🔍 Scrape Only
            </button>
            <button class="btn btn-primary" onclick="startGenerateOnly()">
              📝 Generate Only
            </button>
          </div>
        </div>

        <!-- System Health Card -->
        <div class="card">
          <h2>
            <span
              class="status-indicator status-good"
              id="system-status-indicator"
            ></span>
            ⚙️ System Health
          </h2>
          <div id="system-health">
            <div class="loading">
              <div class="spinner"></div>
              <p>Checking system health...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Settings Panel -->
      <div class="settings-panel">
        <h2>🛠️ Pipeline Configuration</h2>
        <form id="pipeline-config-form">
          <div class="form-row">
            <div class="form-group">
              <label for="operation-mode">Operation Mode</label>
              <select id="operation-mode" name="operation_mode">
                <option value="smart_resume">Smart Resume</option>
                <option value="full_pipeline">Full Pipeline</option>
                <option value="scrape_only">Scrape Only</option>
                <option value="generate_only">Generate Only</option>
              </select>
            </div>

            <div class="form-group">
              <label for="batch-size">Batch Size</label>
              <input
                type="number"
                id="batch-size"
                name="batch_size"
                value="3"
                min="1"
                max="10"
              />
            </div>

            <div class="form-group">
              <label for="max-urls">Max URLs (Optional)</label>
              <input
                type="number"
                id="max-urls"
                name="max_urls"
                placeholder="Leave empty for all"
              />
            </div>

            <div class="form-group">
              <label for="storage-mode">Storage Mode</label>
              <select id="storage-mode" name="use_redis">
                <option value="false">CSV Files</option>
                <option value="true">Redis (if available)</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>
                <input
                  type="checkbox"
                  id="force-restart"
                  name="force_restart"
                />
                Force Fresh Start (ignores existing progress)
              </label>
            </div>
          </div>

          <div class="operation-buttons">
            <button
              type="button"
              class="btn btn-primary"
              onclick="startEnhancedPipeline()"
            >
              🚀 Start Enhanced Pipeline
            </button>
            <button
              type="button"
              class="btn btn-warning"
              onclick="refreshStatus()"
            >
              🔄 Refresh Status
            </button>
            <button
              type="button"
              class="btn btn-danger"
              onclick="showAdvancedOptions()"
            >
              ⚙️ Advanced Options
            </button>
            <button
              type="button"
              class="btn btn-warning"
              onclick="checkDataConsistency()"
            >
              🔧 Fix Data Issues
            </button>
          </div>
        </form>
      </div>

      <!-- Results and Logs -->
      <div class="card" style="margin-top: 20px">
        <h2>📋 Operation Logs & Results</h2>
        <div id="operation-results"></div>
        <div class="logs-container" id="logs-container">
          <div class="log-entry log-info">
            System ready. Waiting for operations...
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global variables
      let refreshInterval;
      let isOperationRunning = false;

      // Initialize dashboard
      document.addEventListener("DOMContentLoaded", function () {
        refreshStatus();
        startAutoRefresh();
      });

      // Auto-refresh status every 30 seconds
      function startAutoRefresh() {
        refreshInterval = setInterval(refreshStatus, 30000);
      }

      function stopAutoRefresh() {
        if (refreshInterval) {
          clearInterval(refreshInterval);
        }
      }

      // Refresh pipeline status
      async function refreshStatus() {
        try {
          const useRedis =
            document.getElementById("storage-mode").value === "true";
          const response = await fetch(
            `/api/pipeline_status?use_redis=${useRedis}`
          );
          const data = await response.json();

          if (data.success) {
            updatePipelineStatus(data.pipeline_state);
            updateProgressOverview(data.pipeline_state);
            updateSystemHealth(data);
            logMessage("Status refreshed successfully", "info");
          } else {
            logMessage(`Error refreshing status: ${data.error}`, "error");
          }
        } catch (error) {
          logMessage(`Network error: ${error.message}`, "error");
        }
      }

      // Update pipeline status display
      function updatePipelineStatus(state) {
        const statusDiv = document.getElementById("pipeline-status");
        const indicator = document.getElementById("pipeline-status-indicator");

        if (state.error) {
          statusDiv.innerHTML = `<div class="alert alert-error">Error: ${state.error}</div>`;
          indicator.className = "status-indicator status-error";
          return;
        }

        const completionRate = state.completion_percentage || 0;
        let statusClass = "status-good";
        if (completionRate < 50) statusClass = "status-warning";
        if (completionRate < 25) statusClass = "status-error";

        indicator.className = `status-indicator ${statusClass}`;

        statusDiv.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${state.total_urls || 0}</div>
                        <div class="stat-label">Total URLs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${state.scraped || 0}</div>
                        <div class="stat-label">Scraped</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${state.generated || 0}</div>
                        <div class="stat-label">Generated</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${completionRate}%</div>
                        <div class="stat-label">Complete</div>
                    </div>
                </div>
            `;
      }

      // Update progress overview
      function updateProgressOverview(state) {
        const progressFill = document.getElementById("overall-progress");
        const progressStats = document.getElementById("progress-stats");

        const completionRate = state.completion_percentage || 0;
        progressFill.style.width = `${completionRate}%`;

        // Ensure values are non-negative
        const pendingScrape = Math.max(0, state.pending_scrape || 0);
        const pendingGeneration = Math.max(0, state.pending_generation || 0);
        const failed = Math.max(0, state.failed || 0);

        progressStats.innerHTML = `
                <div class="stat-item">
                    <div class="stat-number">${pendingScrape}</div>
                    <div class="stat-label">Pending Scrape</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${pendingGeneration}</div>
                    <div class="stat-label">Pending Generation</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${failed}</div>
                    <div class="stat-label">Failed</div>
                </div>
            `;

        // Show warning if there are data consistency issues
        if (state.data_consistency_warning) {
          logMessage(
            "Data consistency warning detected. Consider running data repair.",
            "warning"
          );
        }
      }

      // Update system health
      function updateSystemHealth(data) {
        const healthDiv = document.getElementById("system-health");
        const indicator = document.getElementById("system-status-indicator");

        const storageMode = data.storage_mode || "csv";
        const isHealthy = !data.pipeline_state.error;

        indicator.className = `status-indicator ${
          isHealthy ? "status-good" : "status-error"
        }`;

        healthDiv.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${storageMode.toUpperCase()}</div>
                        <div class="stat-label">Storage Mode</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${
                          isHealthy ? "OK" : "ERROR"
                        }</div>
                        <div class="stat-label">Status</div>
                    </div>
                </div>
            `;
      }

      // Log message to console
      function logMessage(message, type = "info") {
        const logsContainer = document.getElementById("logs-container");
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement("div");
        logEntry.className = `log-entry log-${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;

        logsContainer.appendChild(logEntry);
        logsContainer.scrollTop = logsContainer.scrollHeight;

        // Keep only last 50 log entries
        const logEntries = logsContainer.querySelectorAll(".log-entry");
        if (logEntries.length > 50) {
          logEntries[0].remove();
        }
      }

      // Quick action functions
      async function startSmartResume() {
        await startOperation("smart_resume");
      }

      async function startFullPipeline() {
        await startOperation("full_pipeline");
      }

      async function startScrapeOnly() {
        await startOperation("scrape_only");
      }

      async function startGenerateOnly() {
        await startOperation("generate_only");
      }

      // Start enhanced pipeline with current form settings
      async function startEnhancedPipeline() {
        const form = document.getElementById("pipeline-config-form");
        const formData = new FormData(form);
        const config = Object.fromEntries(formData.entries());

        // Convert checkbox and numeric values
        config.force_restart = document.getElementById("force-restart").checked;
        config.use_redis = config.use_redis === "true";
        config.batch_size = parseInt(config.batch_size) || 3;
        if (config.max_urls) {
          config.max_urls = parseInt(config.max_urls);
        }

        await startOperation(config.operation_mode, config);
      }

      // Generic operation starter
      async function startOperation(operationMode, additionalConfig = {}) {
        if (isOperationRunning) {
          logMessage("Operation already running. Please wait...", "warning");
          return;
        }

        isOperationRunning = true;
        logMessage(`Starting ${operationMode} operation...`, "info");

        try {
          const config = {
            operation_mode: operationMode,
            batch_size: 3,
            use_redis: document.getElementById("storage-mode").value === "true",
            ...additionalConfig,
          };

          const response = await fetch("/api/enhanced_pipeline", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(config),
          });

          const data = await response.json();

          if (data.success) {
            logMessage(
              `Operation started successfully: ${data.message}`,
              "info"
            );
            displayOperationResults(data);

            // Start monitoring progress
            startProgressMonitoring();
          } else {
            logMessage(`Operation failed: ${data.error}`, "error");
          }
        } catch (error) {
          logMessage(`Network error: ${error.message}`, "error");
        } finally {
          isOperationRunning = false;
        }
      }

      // Display operation results
      function displayOperationResults(data) {
        const resultsDiv = document.getElementById("operation-results");

        resultsDiv.innerHTML = `
                <div class="alert alert-success">
                    <strong>Operation Started:</strong> ${data.message}<br>
                    <strong>Mode:</strong> ${data.operation_mode}<br>
                    <strong>Commands Executed:</strong> ${data.commands_executed}<br>
                    <strong>Background Processes:</strong> ${data.background_processes.length}
                </div>
            `;

        if (data.background_processes.length > 0) {
          const processList = data.background_processes
            .map(
              (proc) =>
                `<li><strong>${proc.step}</strong> (PID: ${proc.process_id})</li>`
            )
            .join("");

          resultsDiv.innerHTML += `
                    <div class="alert alert-info">
                        <strong>Running Processes:</strong>
                        <ul>${processList}</ul>
                    </div>
                `;
        }
      }

      // Start progress monitoring
      function startProgressMonitoring() {
        // Increase refresh frequency during operations
        stopAutoRefresh();
        refreshInterval = setInterval(refreshStatus, 10000); // Every 10 seconds

        // Reset to normal frequency after 5 minutes
        setTimeout(() => {
          stopAutoRefresh();
          startAutoRefresh();
        }, 300000);
      }

      // Show advanced options (placeholder)
      function showAdvancedOptions() {
        alert(
          "Advanced options panel coming soon!\n\nFeatures will include:\n- Custom URL filtering\n- Error recovery settings\n- Detailed logging configuration\n- Performance tuning options"
        );
      }

      // Check and fix data consistency issues
      async function checkDataConsistency() {
        try {
          const useRedis =
            document.getElementById("storage-mode").value === "true";

          // First check for issues
          const checkResponse = await fetch(
            `/api/data_consistency_check?use_redis=${useRedis}`
          );
          const checkData = await checkResponse.json();

          if (checkData.success) {
            if (checkData.issues_found === 0) {
              logMessage("No data consistency issues found! 🎉", "success");
              return;
            }

            const issuesList = checkData.issues.join("\n- ");
            const confirmRepair = confirm(
              `Found ${checkData.issues_found} data consistency issues:\n\n- ${issuesList}\n\nWould you like to attempt automatic repair?`
            );

            if (confirmRepair) {
              // Attempt repair
              const repairResponse = await fetch(
                `/api/data_consistency_check?use_redis=${useRedis}&repair=true`
              );
              const repairData = await repairResponse.json();

              if (repairData.success) {
                if (repairData.repairs_made.length > 0) {
                  const repairsList = repairData.repairs_made.join("\n- ");
                  logMessage(
                    `Data repair completed:\n- ${repairsList}`,
                    "success"
                  );

                  // Refresh status to show updated data
                  setTimeout(refreshStatus, 1000);
                } else {
                  logMessage("No repairs were needed or possible.", "info");
                }
              } else {
                logMessage(`Repair failed: ${repairData.error}`, "error");
              }
            }
          } else {
            logMessage(
              `Data consistency check failed: ${checkData.error}`,
              "error"
            );
          }
        } catch (error) {
          logMessage(
            `Error checking data consistency: ${error.message}`,
            "error"
          );
        }
      }

      // Handle page visibility changes
      document.addEventListener("visibilitychange", function () {
        if (document.hidden) {
          stopAutoRefresh();
        } else {
          refreshStatus();
          startAutoRefresh();
        }
      });
    </script>
  </body>
</html>
