<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pipeline Dashboard - MCP Admin Panel</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: #f5f7fa;
        color: #333;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .nav {
        background: white;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .nav a {
        color: #667eea;
        text-decoration: none;
        margin-right: 20px;
        font-weight: 600;
      }

      .nav a:hover {
        color: #764ba2;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
      }

      .card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .card h3 {
        margin-bottom: 15px;
        color: #4a5568;
      }

      .progress-section {
        margin-bottom: 20px;
      }

      .progress-bar {
        width: 100%;
        height: 25px;
        background-color: #e2e8f0;
        border-radius: 12px;
        overflow: hidden;
        margin: 10px 0;
        position: relative;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4299e1, #3182ce);
        transition: width 0.5s ease;
        border-radius: 12px;
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        color: white;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 15px;
        margin: 15px 0;
      }

      .stat-item {
        text-align: center;
        padding: 15px;
        background: #f7fafc;
        border-radius: 8px;
      }

      .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2d3748;
      }

      .stat-label {
        font-size: 0.8rem;
        color: #718096;
        margin-top: 5px;
      }

      .operation-panel {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .operation-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .btn {
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
      }

      .btn-primary {
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
      }

      .btn-success {
        background: linear-gradient(135deg, #48bb78, #38a169);
        color: white;
      }

      .btn-warning {
        background: linear-gradient(135deg, #ed8936, #dd6b20);
        color: white;
      }

      .btn-danger {
        background: linear-gradient(135deg, #f56565, #e53e3e);
        color: white;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #4a5568;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 2px solid #e2e8f0;
        border-radius: 6px;
        font-size: 0.9rem;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #4299e1;
      }

      .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .logs-container {
        background: #1a202c;
        color: #e2e8f0;
        border-radius: 8px;
        padding: 15px;
        font-family: "Courier New", monospace;
        font-size: 0.8rem;
        max-height: 400px;
        overflow-y: auto;
        margin-top: 15px;
      }

      .log-entry {
        margin-bottom: 3px;
        padding: 3px;
        border-radius: 2px;
      }

      .log-info {
        background-color: rgba(66, 153, 225, 0.1);
      }
      .log-warning {
        background-color: rgba(237, 137, 54, 0.1);
      }
      .log-error {
        background-color: rgba(245, 101, 101, 0.1);
      }
      .log-success {
        background-color: rgba(72, 187, 120, 0.1);
      }

      .alert {
        padding: 12px;
        border-radius: 6px;
        margin: 10px 0;
        font-weight: 500;
      }

      .alert-success {
        background-color: #c6f6d5;
        color: #22543d;
        border: 1px solid #9ae6b4;
      }

      .alert-error {
        background-color: #fed7d7;
        color: #742a2a;
        border: 1px solid #fc8181;
      }

      .alert-info {
        background-color: #bee3f8;
        color: #2a4365;
        border: 1px solid #90cdf4;
      }

      .alert-warning {
        background-color: #faf089;
        color: #744210;
        border: 1px solid #f6e05e;
      }

      .failed-urls-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .failed-url-item {
        background: #fed7d7;
        border: 1px solid #fc8181;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 10px;
      }

      .failed-url-item .url {
        font-weight: bold;
        color: #742a2a;
      }

      .failed-url-item .error {
        font-size: 0.8rem;
        color: #a0aec0;
        margin-top: 5px;
      }

      .loading {
        text-align: center;
        padding: 20px;
      }

      .spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .hidden {
        display: none;
      }

      @media (max-width: 768px) {
        .dashboard-grid {
          grid-template-columns: 1fr;
        }

        .operation-buttons {
          grid-template-columns: 1fr;
        }

        .form-row {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🔧 Pipeline Dashboard</h1>
      <p>Advanced monitoring and control for MCP automation pipeline</p>
    </div>

    <div class="nav">
      <a href="/">🏠 Main Dashboard</a>
      <a href="/pipeline">🔧 Pipeline Dashboard</a>
      <a href="/content">📝 Content Management</a>
      <a href="/config-generator">⚙️ Config Generator</a>
    </div>

    <div class="container">
      <!-- Progress Overview -->
      <div class="dashboard-grid">
        <div class="card">
          <h3>📊 Scraping Progress</h3>
          <div class="progress-section">
            <div class="progress-bar">
              <div
                class="progress-fill"
                id="scraping-progress"
                style="width: 0%"
              ></div>
              <div class="progress-text" id="scraping-progress-text">0%</div>
            </div>
            <div class="stats-grid" id="scraping-stats">
              <!-- Scraping stats will be loaded here -->
            </div>
          </div>
        </div>

        <div class="card">
          <h3>📝 Generation Progress</h3>
          <div class="progress-section">
            <div class="progress-bar">
              <div
                class="progress-fill"
                id="generation-progress"
                style="width: 0%"
              ></div>
              <div class="progress-text" id="generation-progress-text">0%</div>
            </div>
            <div class="stats-grid" id="generation-stats">
              <!-- Generation stats will be loaded here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Operation Controls -->
      <div class="operation-panel">
        <h3>🎮 Pipeline Operations</h3>
        <div class="form-row">
          <div class="form-group">
            <label for="operation-mode">Operation Mode</label>
            <select id="operation-mode">
              <option value="smart_resume">🔄 Smart Resume</option>
              <option value="full_pipeline">🚀 Full Pipeline</option>
              <option value="scrape_only">🔍 Scrape Only</option>
              <option value="generate_only">📝 Generate Only</option>
            </select>
          </div>

          <div class="form-group">
            <label for="batch-size">Batch Size</label>
            <input type="number" id="batch-size" value="3" min="1" max="10" />
          </div>

          <div class="form-group">
            <label for="max-urls">Max URLs</label>
            <input type="number" id="max-urls" placeholder="All URLs" />
          </div>

          <div class="form-group">
            <label for="storage-mode">Storage Mode</label>
            <select id="storage-mode">
              <option value="false">CSV Files</option>
              <option value="true">Redis</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>
            <input type="checkbox" id="force-restart" />
            Force Fresh Start (ignore existing progress)
          </label>
        </div>

        <div class="operation-buttons">
          <button class="btn btn-primary" onclick="startPipelineOperation()">
            🚀 Start Operation
          </button>
          <button class="btn btn-success" onclick="refreshStatus()">
            🔄 Refresh Status
          </button>
          <button class="btn btn-warning" onclick="retryFailedUrls()">
            🔁 Retry Failed
          </button>
          <button class="btn btn-danger" onclick="showSelectiveProcessing()">
            🎯 Selective Processing
          </button>
        </div>
      </div>

      <!-- Selective Processing Panel -->
      <div class="operation-panel hidden" id="selective-panel">
        <h3>🎯 Selective Processing</h3>
        <div class="form-group">
          <label for="url-pattern">URL Pattern (optional)</label>
          <input
            type="text"
            id="url-pattern"
            placeholder="e.g., github.com/user"
          />
        </div>

        <div class="form-group">
          <label for="specific-urls">Specific URLs (one per line)</label>
          <textarea
            id="specific-urls"
            rows="5"
            placeholder="https://github.com/user/repo1&#10;https://github.com/user/repo2"
          ></textarea>
        </div>

        <div class="operation-buttons">
          <button class="btn btn-primary" onclick="startSelectiveProcessing()">
            🎯 Process Selected URLs
          </button>
          <button class="btn btn-warning" onclick="hideSelectiveProcessing()">
            ❌ Cancel
          </button>
        </div>
      </div>

      <!-- Results and Status -->
      <div class="card">
        <h3>📋 Operation Results</h3>
        <div id="operation-results">
          <div class="alert alert-info">
            No operations running. Ready to start.
          </div>
        </div>
      </div>

      <!-- Failed URLs Section -->
      <div class="failed-urls-section">
        <h3>❌ Failed URLs</h3>
        <div id="failed-urls-container">
          <div class="loading">
            <div class="spinner"></div>
            <p>Loading failed URLs...</p>
          </div>
        </div>
      </div>

      <!-- Live Logs -->
      <div class="card">
        <h3>📝 Live Logs</h3>
        <div class="logs-container" id="logs-container">
          <div class="log-entry log-info">
            Pipeline dashboard initialized. Ready for operations.
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global variables
      let refreshInterval;
      let isOperationRunning = false;

      // Initialize dashboard
      document.addEventListener("DOMContentLoaded", function () {
        refreshStatus();
        loadFailedUrls();
        startAutoRefresh();
      });

      // Auto-refresh every 15 seconds
      function startAutoRefresh() {
        refreshInterval = setInterval(() => {
          refreshStatus();
          loadFailedUrls();
        }, 15000);
      }

      function stopAutoRefresh() {
        if (refreshInterval) {
          clearInterval(refreshInterval);
        }
      }

      // Refresh pipeline status
      async function refreshStatus() {
        try {
          const useRedis =
            document.getElementById("storage-mode").value === "true";
          const response = await fetch(
            `/api/pipeline_status?use_redis=${useRedis}`
          );
          const data = await response.json();

          if (data.success) {
            updateProgressBars(data.pipeline_state);
            logMessage("Status refreshed successfully", "success");
          } else {
            logMessage(`Error refreshing status: ${data.error}`, "error");
          }
        } catch (error) {
          logMessage(`Network error: ${error.message}`, "error");
        }
      }

      // Update progress bars and stats
      function updateProgressBars(state) {
        // Scraping progress
        const scrapingProgress =
          state.total_urls > 0 ? (state.scraped / state.total_urls) * 100 : 0;
        document.getElementById(
          "scraping-progress"
        ).style.width = `${scrapingProgress}%`;
        document.getElementById(
          "scraping-progress-text"
        ).textContent = `${Math.round(scrapingProgress)}%`;

        document.getElementById("scraping-stats").innerHTML = `
                <div class="stat-item">
                    <div class="stat-number">${state.scraped || 0}</div>
                    <div class="stat-label">Scraped</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${state.pending_scrape || 0}</div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${state.total_urls || 0}</div>
                    <div class="stat-label">Total</div>
                </div>
            `;

        // Generation progress
        const generationProgress =
          state.scraped > 0 ? (state.generated / state.scraped) * 100 : 0;
        document.getElementById(
          "generation-progress"
        ).style.width = `${generationProgress}%`;
        document.getElementById(
          "generation-progress-text"
        ).textContent = `${Math.round(generationProgress)}%`;

        document.getElementById("generation-stats").innerHTML = `
                <div class="stat-item">
                    <div class="stat-number">${state.generated || 0}</div>
                    <div class="stat-label">Generated</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${
                      state.pending_generation || 0
                    }</div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${state.scraped || 0}</div>
                    <div class="stat-label">Available</div>
                </div>
            `;
      }

      // Load failed URLs
      async function loadFailedUrls() {
        try {
          const useRedis =
            document.getElementById("storage-mode").value === "true";
          const response = await fetch(
            `/api/failed_urls?use_redis=${useRedis}`
          );
          const data = await response.json();

          const container = document.getElementById("failed-urls-container");

          if (data.success) {
            if (data.failed_urls.length === 0) {
              container.innerHTML =
                '<div class="alert alert-success">No failed URLs! 🎉</div>';
            } else {
              const failedHtml = data.failed_urls
                .map(
                  (item) => `
                            <div class="failed-url-item">
                                <div class="url">${item.url}</div>
                                <div class="error">${
                                  item.error ||
                                  item.error_message ||
                                  "Unknown error"
                                }</div>
                            </div>
                        `
                )
                .join("");

              container.innerHTML = `
                            <div class="alert alert-warning">Found ${data.total_failed} failed URLs</div>
                            ${failedHtml}
                        `;
            }
          } else {
            container.innerHTML = `<div class="alert alert-error">Error loading failed URLs: ${data.error}</div>`;
          }
        } catch (error) {
          document.getElementById(
            "failed-urls-container"
          ).innerHTML = `<div class="alert alert-error">Network error: ${error.message}</div>`;
        }
      }

      // Start pipeline operation
      async function startPipelineOperation() {
        if (isOperationRunning) {
          logMessage("Operation already running. Please wait...", "warning");
          return;
        }

        const config = {
          operation_mode: document.getElementById("operation-mode").value,
          batch_size:
            parseInt(document.getElementById("batch-size").value) || 3,
          use_redis: document.getElementById("storage-mode").value === "true",
          force_restart: document.getElementById("force-restart").checked,
        };

        const maxUrls = document.getElementById("max-urls").value;
        if (maxUrls) {
          config.max_urls = parseInt(maxUrls);
        }

        await executeOperation(
          "/api/enhanced_pipeline",
          config,
          "Pipeline operation"
        );
      }

      // Retry failed URLs
      async function retryFailedUrls() {
        const config = {
          operation_mode: document.getElementById("operation-mode").value,
          batch_size:
            parseInt(document.getElementById("batch-size").value) || 3,
          use_redis: document.getElementById("storage-mode").value === "true",
        };

        await executeOperation(
          "/api/retry_failed",
          config,
          "Retry failed URLs"
        );
      }

      // Show selective processing panel
      function showSelectiveProcessing() {
        document.getElementById("selective-panel").classList.remove("hidden");
      }

      // Hide selective processing panel
      function hideSelectiveProcessing() {
        document.getElementById("selective-panel").classList.add("hidden");
      }

      // Start selective processing
      async function startSelectiveProcessing() {
        const urlPattern = document.getElementById("url-pattern").value.trim();
        const specificUrls = document
          .getElementById("specific-urls")
          .value.split("\n")
          .map((url) => url.trim())
          .filter((url) => url.length > 0);

        if (!urlPattern && specificUrls.length === 0) {
          logMessage(
            "Please provide either a URL pattern or specific URLs",
            "warning"
          );
          return;
        }

        const config = {
          operation_mode: document.getElementById("operation-mode").value,
          batch_size:
            parseInt(document.getElementById("batch-size").value) || 3,
          url_pattern: urlPattern,
          urls: specificUrls,
        };

        await executeOperation(
          "/api/selective_processing",
          config,
          "Selective processing"
        );
        hideSelectiveProcessing();
      }

      // Generic operation executor
      async function executeOperation(endpoint, config, operationName) {
        if (isOperationRunning) {
          logMessage("Operation already running. Please wait...", "warning");
          return;
        }

        isOperationRunning = true;
        logMessage(`Starting ${operationName}...`, "info");

        try {
          const response = await fetch(endpoint, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(config),
          });

          const data = await response.json();

          if (data.success) {
            logMessage(
              `${operationName} started successfully: ${data.message}`,
              "success"
            );
            displayOperationResults(data);

            // Increase refresh frequency during operations
            stopAutoRefresh();
            refreshInterval = setInterval(() => {
              refreshStatus();
              loadFailedUrls();
            }, 5000);

            // Reset to normal frequency after 5 minutes
            setTimeout(() => {
              stopAutoRefresh();
              startAutoRefresh();
            }, 300000);
          } else {
            logMessage(`${operationName} failed: ${data.error}`, "error");
          }
        } catch (error) {
          logMessage(`Network error: ${error.message}`, "error");
        } finally {
          isOperationRunning = false;
        }
      }

      // Display operation results
      function displayOperationResults(data) {
        const resultsDiv = document.getElementById("operation-results");

        let resultHtml = `
                <div class="alert alert-success">
                    <strong>Operation Started:</strong> ${data.message}<br>
                    <strong>Mode:</strong> ${data.operation_mode || "N/A"}
                </div>
            `;

        if (data.background_processes && data.background_processes.length > 0) {
          const processList = data.background_processes
            .map(
              (proc) =>
                `<li><strong>${proc.step}</strong> (PID: ${proc.process_id})</li>`
            )
            .join("");

          resultHtml += `
                    <div class="alert alert-info">
                        <strong>Running Processes:</strong>
                        <ul>${processList}</ul>
                    </div>
                `;
        }

        if (data.failed_urls_count) {
          resultHtml += `
                    <div class="alert alert-warning">
                        <strong>Processing:</strong> ${data.failed_urls_count} failed URLs
                    </div>
                `;
        }

        if (data.selected_urls) {
          resultHtml += `
                    <div class="alert alert-info">
                        <strong>Selected URLs:</strong> ${data.selected_urls.length} URLs
                    </div>
                `;
        }

        resultsDiv.innerHTML = resultHtml;
      }

      // Log message to console
      function logMessage(message, type = "info") {
        const logsContainer = document.getElementById("logs-container");
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement("div");
        logEntry.className = `log-entry log-${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;

        logsContainer.appendChild(logEntry);
        logsContainer.scrollTop = logsContainer.scrollHeight;

        // Keep only last 100 log entries
        const logEntries = logsContainer.querySelectorAll(".log-entry");
        if (logEntries.length > 100) {
          logEntries[0].remove();
        }
      }

      // Handle page visibility changes
      document.addEventListener("visibilitychange", function () {
        if (document.hidden) {
          stopAutoRefresh();
        } else {
          refreshStatus();
          loadFailedUrls();
          startAutoRefresh();
        }
      });
    </script>
  </body>
</html>
