#!/usr/bin/env python3
"""
CSV Encoding Repair Script
Fixes encoding issues in completed_data.csv file
"""

import os
import shutil
from datetime import datetime
from pathlib import Path

def fix_csv_encoding(csv_file_path):
    """Fix encoding issues in CSV file"""
    
    print(f"🔧 Fixing encoding issues in {csv_file_path}")
    
    # Create backup
    backup_file = str(csv_file_path) + f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    shutil.copy(str(csv_file_path), backup_file)
    print(f"✅ Backup created: {backup_file}")
    
    # Try different encodings to read the file
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    
    urls_found = set()
    lines_processed = 0
    
    for encoding in encodings:
        try:
            print(f"🔍 Trying {encoding} encoding...")
            
            with open(csv_file_path, 'r', encoding=encoding, errors='ignore') as f:
                lines = f.readlines()
            
            # Extract URLs from each line
            for line_num, line in enumerate(lines):
                try:
                    if line.strip() and not line.startswith('repo_url'):  # Skip header
                        # Try to extract the first field (URL)
                        parts = line.split(',')
                        if parts and parts[0].strip():
                            url = parts[0].strip().strip('"')
                            if url.startswith('https://github.com/'):
                                urls_found.add(url)
                                lines_processed += 1
                except Exception:
                    continue
            
            if urls_found:
                print(f"✅ Successfully extracted {len(urls_found)} URLs with {encoding}")
                break
                
        except Exception as e:
            print(f"❌ Failed with {encoding}: {e}")
            continue
    
    if not urls_found:
        print("❌ Could not extract any URLs from the file")
        return False
    
    # Create a clean CSV file
    print(f"📝 Creating clean CSV with {len(urls_found)} URLs...")
    
    try:
        # Write clean CSV
        with open(csv_file_path, 'w', encoding='utf-8', newline='') as f:
            # Write header
            f.write('repo_url,slug,readme,stars,stored_at,seo_title,seo_meta_description,seo_keywords,seo_structured_data,language,category,subcategory,installation_command,tools_list,config_example,dependencies,license,last_updated,author,description_short\n')
            
            # Write URLs with minimal data
            timestamp = datetime.now().isoformat()
            for url in sorted(urls_found):
                # Extract repo info from URL
                parts = url.replace('https://github.com/', '').split('/')
                if len(parts) >= 2:
                    owner, repo = parts[0], parts[1]
                    slug = f"{owner}/{repo}"
                else:
                    slug = url.replace('https://github.com/', '')
                
                # Write minimal row
                row = [
                    url,  # repo_url
                    slug,  # slug
                    '',   # readme (empty)
                    '0',  # stars
                    timestamp,  # stored_at
                    '',   # seo_title
                    '',   # seo_meta_description
                    '',   # seo_keywords
                    '',   # seo_structured_data
                    '',   # language
                    '',   # category
                    '',   # subcategory
                    '',   # installation_command
                    '',   # tools_list
                    '',   # config_example
                    '',   # dependencies
                    '',   # license
                    '',   # last_updated
                    owner,  # author
                    ''    # description_short
                ]
                
                # Escape commas in fields and write
                escaped_row = [f'"{field}"' if ',' in field else field for field in row]
                f.write(','.join(escaped_row) + '\n')
        
        print(f"✅ Successfully created clean CSV file")
        print(f"📊 Processed {len(urls_found)} unique GitHub URLs")
        print(f"💾 Original file backed up to: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating clean CSV: {e}")
        # Restore backup
        shutil.copy(backup_file, str(csv_file_path))
        print(f"🔄 Restored original file from backup")
        return False

def main():
    """Main function"""
    
    # Find the CSV file
    csv_file = Path("data/completed_data.csv")
    
    if not csv_file.exists():
        print(f"❌ File not found: {csv_file}")
        print("Make sure you're running this script from the project root directory")
        return
    
    print("🚀 Starting CSV encoding repair...")
    print(f"📁 File: {csv_file}")
    print(f"📏 File size: {csv_file.stat().st_size:,} bytes")
    
    success = fix_csv_encoding(csv_file)
    
    if success:
        print("\n🎉 CSV repair completed successfully!")
        print("✅ Your admin panel should now work without encoding errors")
        print("✅ Negative values should be resolved")
        print("\n💡 Next steps:")
        print("1. Restart your admin panel")
        print("2. Check the progress overview")
        print("3. Use the '🔧 Fix Data Issues' button if needed")
    else:
        print("\n❌ CSV repair failed")
        print("💡 You may need to manually clean the CSV file")

if __name__ == "__main__":
    main()
