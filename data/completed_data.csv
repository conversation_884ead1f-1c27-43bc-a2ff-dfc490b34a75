repo_url,slug,readme,stars,stored_at,seo_title,seo_meta_description,seo_keywords,seo_structured_data,language,category,subcategory,installation_command,tools_list,config_example,dependencies,license,last_updated,author,description_short
https://github.com/hbg/mcp-paperswithcode,hbg/mcp-paperswithcode,"# mcp-paperswithcode

[![smithery badge](https://smithery.ai/badge/@hbg/mcp-paperswithcode)](https://smithery.ai/server/@hbg/mcp-paperswithcode)

# 🦾 Features

> Allows AI assistants to find and read papers, as well as view related code repositories for further context.

This MCP server provides a Model Context Protocol (MCP) client that interfaces with the PapersWithCode API.

It includes tools for searching, retrieving, and parsing information on research papers, authors, datasets, conferences, and more.

# 🚀 Getting Started

### Installing via Smithery

To install mcp-paperswithcode for Claude <PERSON> automatically via [<PERSON><PERSON>](https://smithery.ai/server/@hbg/mcp-paperswithcode):

```bash
npx -y @smithery/cli install @hbg/mcp-paperswithcode --client claude
```

# 🛠️ Tools

## 📚 Paper Tools

### `search_papers`
Search for papers using optional filters.

- `abstract` (str, optional): Filter by abstract text.
- `title` (str, optional): Filter by title text.
- `arxiv_id` (str, optional): Filter by ArXiv ID.

### `get_paper`
Get a paper's metadata by its ID.

- `paper_id` (str): The paper ID.

### `read_paper_from_url`
Extract readable text from a paper given its URL.

- `paper_url` (str): The direct PDF or HTML URL to a paper.

### `list_paper_results`
List benchmark results associated with a paper.

- `paper_id` (str): The paper ID.

### `list_paper_tasks`
List tasks associated with a paper.

- `paper_id` (str): The paper ID.

### `list_paper_methods`
List methods discussed in a paper.

- `paper_id` (str): The paper ID.

### `list_paper_repositories`
List code repositories linked to a paper.

- `paper_id` (str): The paper ID.

### `list_paper_datasets`
List datasets mentioned or used in a paper.

- `paper_id` (str): The paper ID.

## 🧠 Research Area Tools

### `search_research_areas`
Search research areas by name.

- `name` (str): Partial or full name of the research area.

### `get_research_area`
Get metadata for a specific research area.

- `area_id` (str): The area ID.

### `list_research_area_tasks`
List tasks associated with a research area.

- `area_id` (str): The area ID.

## 👨‍🔬 Author Tools

### `search_authors`
Search authors by full name.

- `full_name` (str): Full name of the author.

### `get_paper_author`
Get metadata for an author by ID.

- `author_id` (str): The author ID.

### `list_papers_by_author_id`
List all papers written by an author via ID.

- `author_id` (str): The author ID.

### `list_papers_by_author_name`
Search by name and return papers for the first matching author.

- `author_name` (str): Full name of the author.

## 🎓 Conference Tools

### `list_conferences`
List conferences, optionally filter by name.

- `conference_name` (str, optional): Full or partial name.

### `get_conference`
Get metadata for a specific conference.

- `conference_id` (str): The conference ID.

### `list_conference_proceedings`
List all proceedings under a conference.

- `conference_id` (str): The conference ID.

### `get_conference_proceeding`
Get details for a specific conference proceeding.

- `conference_id` (str): The conference ID.
- `proceeding_id` (str): The proceeding ID.

### `list_conference_papers`
List all papers for a specific conference proceeding.

- `conference_id` (str): The conference ID.
- `proceeding_id` (str): The proceeding ID.
","Star
 9",2025-06-24T08:43:26.213759,mcp-paperswithcode - MCP Server | Model Context Protocol Integration,"# mcp-paperswithcode

[![smithery badge](https://smithery.ai/badge/@hbg/mcp-paperswithcode)](https://smithery.ai/server/@hbg/mcp-paperswithcode)

# 🦾 Feature...","['mcp server', 'model context protocol', 'ai integration', 'mcp-paperswithcode']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-paperswithcode', 'description': '# mcp-paperswithcode\n\n[![smithery badge](https://smithery.ai/badge/@hbg/mcp-paperswithcode)](https://smithery.ai/server/@hbg/mcp-paperswithcode)\n\n# 🦾 Feature...', 'url': 'https://github.com/hbg/mcp-paperswithcode', 'codeRepository': 'https://github.com/hbg/mcp-paperswithcode', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",C#,Security,Search Engine,,,,,MIT License,2025-06-07,hbg,"> Allows AI assistants to find and read papers, as well as view related code repositories for further context."
https://github.com/Flux159/mcp-server-kubernetes,Flux159/mcp-server-kubernetes,"# MCP Server Kubernetes

[![CI](https://github.com/Flux159/mcp-server-kubernetes/actions/workflows/ci.yml/badge.svg)](https://github.com/yourusername/mcp-server-kubernetes/actions/workflows/ci.yml)
[![Language](https://img.shields.io/github/languages/top/Flux159/mcp-server-kubernetes)](https://github.com/yourusername/mcp-server-kubernetes)
[![Bun](https://img.shields.io/badge/runtime-bun-orange)](https://bun.sh)
[![Kubernetes](https://img.shields.io/badge/kubernetes-%23326ce5.svg?style=flat&logo=kubernetes&logoColor=white)](https://kubernetes.io/)
[![Docker](https://img.shields.io/badge/docker-%230db7ed.svg?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)
[![Stars](https://img.shields.io/github/stars/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/stargazers)
[![Issues](https://img.shields.io/github/issues/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/issues)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/Flux159/mcp-server-kubernetes/pulls)
[![Last Commit](https://img.shields.io/github/last-commit/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/commits/main)
[![smithery badge](https://smithery.ai/badge/mcp-server-kubernetes)](https://smithery.ai/protocol/mcp-server-kubernetes)

MCP Server that can connect to a Kubernetes cluster and manage it. Supports loading kubeconfig from multiple sources in priority order.

https://github.com/user-attachments/assets/f25f8f4e-4d04-479b-9ae0-5dac452dd2ed

<a href=""https://glama.ai/mcp/servers/w71ieamqrt""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/w71ieamqrt/badge"" /></a>

## Usage with Claude Desktop

```json
{
  ""mcpServers"": {
    ""kubernetes"": {
      ""command"": ""npx"",
      ""args"": [""mcp-server-kubernetes""]
    }
  }
}
```

By default, the server loads kubeconfig from `~/.kube/config`. For additional authentication options (environment variables, custom paths, etc.), see [ADVANCED_README.md](ADVANCED_README.md).

The server will automatically connect to your current kubectl context. Make sure you have:

1. kubectl installed and in your PATH
2. A valid kubeconfig file with contexts configured
3. Access to a Kubernetes cluster configured for kubectl (e.g. minikube, Rancher Desktop, GKE, etc.)
4. Helm v3 installed and in your PATH (no Tiller required). Optional if you don't plan to use Helm.

You can verify your connection by asking Claude to list your pods or create a test deployment.

If you have errors open up a standard terminal and run `kubectl get pods` to see if you can connect to your cluster without credentials issues.

## Usage with mcp-chat

[mcp-chat](https://github.com/Flux159/mcp-chat) is a CLI chat client for MCP servers. You can use it to interact with the Kubernetes server.

```shell
npx mcp-chat --server ""npx mcp-server-kubernetes""
```

Alternatively, pass it your existing Claude Desktop configuration file from above (Linux should pass the correct path to config):

Mac:

```shell
npx mcp-chat --config ""~/Library/Application Support/Claude/claude_desktop_config.json""
```

Windows:

```shell
npx mcp-chat --config ""%APPDATA%\Claude\claude_desktop_config.json""
```

## Features

- [x] Connect to a Kubernetes cluster
- [x] Unified kubectl API for managing resources
  - Get or list resources with `kubectl_get`
  - Describe resources with `kubectl_describe`
  - List resources with `kubectl_list`
  - Create resources with `kubectl_create`
  - Apply YAML manifests with `kubectl_apply`
  - Delete resources with `kubectl_delete`
  - Get logs with `kubectl_logs`
  - Manage kubectl contexts with `kubectl_context`
  - Explain Kubernetes resources with `explain_resource`
  - List API resources with `list_api_resources`
  - Scale resources with `kubectl_scale`
  - Update field(s) of a resource with `kubectl_patch`
  - Manage deployment rollouts with `kubectl_rollout`
  - Execute any kubectl command with `kubectl_generic`
- [x] Advanced operations
  - Scale deployments with `kubectl_scale` (replaces legacy `scale_deployment`)
  - Port forward to pods and services with `port_forward`
  - Run Helm operations
    - Install, upgrade, and uninstall charts
    - Support for custom values, repositories, and versions
- [x] Troubleshooting Prompt (`k8s-troubleshoot`)
  - Guides through a systematic Kubernetes troubleshooting flow for pods based on a keyword and optional namespace.
- [x] Non-destructive mode for read and create/update-only access to clusters

## Prompts

The MCP Kubernetes server includes specialized prompts to assist with common operations.

### k8s-troubleshoot Prompt

This prompt provides a systematic troubleshooting flow for Kubernetes pods. It accepts a `keyword` to identify relevant pods and an optional `namespace` to narrow the search.
The prompt's output will guide you through an autonomous troubleshooting flow, providing instructions for identifying issues, collecting evidence, and suggesting remediation steps.

## Local Development

Make sure that you have [bun installed](https://bun.sh/docs/installation). Clone the repo & install dependencies:

```bash
git clone https://github.com/Flux159/mcp-server-kubernetes.git
cd mcp-server-kubernetes
bun install
```

### Development Workflow

1. Start the server in development mode (watches for file changes):

```bash
bun run dev
```

2. Run unit tests:

```bash
bun run test
```

3. Build the project:

```bash
bun run build
```

4. Local Testing with [Inspector](https://github.com/modelcontextprotocol/inspector)

```bash
npx @modelcontextprotocol/inspector node dist/index.js
# Follow further instructions on terminal for Inspector link
```

5. Local testing with Claude Desktop

```json
{
  ""mcpServers"": {
    ""mcp-server-kubernetes"": {
      ""command"": ""node"",
      ""args"": [""/path/to/your/mcp-server-kubernetes/dist/index.js""]
    }
  }
}
```

6. Local testing with [mcp-chat](https://github.com/Flux159/mcp-chat)

```bash
bun run chat
```

## Contributing

See the [CONTRIBUTING.md](CONTRIBUTING.md) file for details.

## Advanced

### Non-Destructive Mode

You can run the server in a non-destructive mode that disables all destructive operations (delete pods, delete deployments, delete namespaces, etc.):

```shell
ALLOW_ONLY_NON_DESTRUCTIVE_TOOLS=true npx mcp-server-kubernetes
```

For Claude Desktop configuration with non-destructive mode:

```json
{
  ""mcpServers"": {
    ""kubernetes-readonly"": {
      ""command"": ""npx"",
      ""args"": [""mcp-server-kubernetes""],
      ""env"": {
        ""ALLOW_ONLY_NON_DESTRUCTIVE_TOOLS"": ""true""
      }
    }
  }
}
```

### Commands Available in Non-Destructive Mode

All read-only and resource creation/update operations remain available:

- Resource Information: `kubectl_get`, `kubectl_describe`, `kubectl_list`, `kubectl_logs`, `explain_resource`, `list_api_resources`
- Resource Creation/Modification: `kubectl_apply`, `kubectl_create`, `kubectl_scale`, `kubectl_patch`, `kubectl_rollout`
- Helm Operations: `install_helm_chart`, `upgrade_helm_chart`
- Connectivity: `port_forward`, `stop_port_forward`
- Context Management: `kubectl_context`

### Commands Disabled in Non-Destructive Mode

The following destructive ophttps://github.com/zcaceres/gtasks-mcp,zcaceres/gtasks-mcp,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcaceres/gtasks)

This MCP server integrates with Google Tasks to allow listing, reading, searching, creating, updating, and deleting tasks.

## Components

### Tools

- **search**
  - Search for tasks in Google Tasks
  - Input: `query` (string): Search query
  - Returns matching tasks with details

- **list**
  - List all tasks in Google Tasks
  - Optional input: `cursor` (string): Cursor for pagination
  - Returns a list of all tasks

- **create**
  - Create a new task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `title` (string, required): Task title
    - `notes` (string, optional): Task notes
    - `due` (string, optional): Due date
  - Returns confirmation of task creation

- **update**
  - Update an existing task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `id` (string, required): Task ID
    - `uri` (string, required): Task URI
    - `title` (string, optional): New task title
    - `notes` (string, optional): New task notes
    - `status` (string, optional): New task status (""needsAction"" or ""completed"")
    - `due` (string, optional): New due date
  - Returns confirmation of task update

- **delete**
  - Delete a task in Google Tasks
  - Input:
    - `taskListId` (string, required): Task list ID
    - `id` (string, required): Task ID
  - Returns confirmation of task deletion

- **clear**
  - Clear completed tasks from a Google Tasks task list
  - Input: `taskListId` (string, required): Task list ID
  - Returns confirmation of cleared tasks

### Resources

The server provides access to Google Tasks resources:

- **Tasks** (`gtasks:///<task_id>`)
  - Represents individual tasks in Google Tasks
  - Supports reading task details including title, status, due date, notes, and other metadata
  - Can be listed, read, created, updated, and deleted using the provided tools

## Getting started

1. [Create a new Google Cloud project](https://console.cloud.google.com/projectcreate)
2. [Enable the Google Tasks API](https://console.cloud.google.com/workspace-api/products)
3. [Configure an OAuth consent screen](https://console.cloud.google.com/apis/credentials/consent) (""internal"" is fine for testing)
4. Add scopes `https://www.googleapis.com/auth/tasks`
5. [Create an OAuth Client ID](https://console.cloud.google.com/apis/credentials/oauthclient) for application type ""Desktop App""
6. Download the JSON file of your client's OAuth keys
7. Rename the key file to `gcp-oauth.keys.json` and place into the root of this repo (i.e. `gcp-oauth.keys.json`)

Make sure to build the server with either `npm run build` or `npm run watch`.

### Installing via Smithery

To install Google Tasks Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@zcaceres/gtasks):

```bash
npx -y @smithery/cli install @zcaceres/gtasks --client claude
```

### Authentication

To authenticate and save credentials:

1. Run the server with the `auth` argument: `npm run start auth`
2. This will open an authentication flow in your system browser
3. Complete the authentication process
4. Credentials will be saved in the root of this repo (i.e. `.gdrive-server-credentials.json`)

### Usage with Desktop App

To integrate this server with the desktop app, add the following to your app's server configuration:

```json
{
  ""mcpServers"": {
    ""gtasks"": {
      ""command"": ""/opt/homebrew/bin/node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}
```
","Star
 63",2025-06-24T08:43:26.213755,gtasks-mcp - MCP Server | Model Context Protocol Integration,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcacere...","['mcp server', 'model context protocol', 'ai integration', 'gtasks-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'gtasks-mcp', 'description': '# Google Tasks MCP Server\n\n![gtasks mcp logo](./logo.jpg)\n[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcacere...', 'url': 'https://github.com/zcaceres/gtasks-mcp', 'codeRepositoryhttps://github.com/zcaceres/gtasks-mcp,zcaceres/gtasks-mcp,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcaceres/gtasks)

This MCP server integrates with Google Tasks to allow listing, reading, searching, creating, updating, and deleting tasks.

## Components

### Tools

- **search**
  - Search for tasks in Google Tasks
  - Input: `query` (string): Search query
  - Returns matching tasks with details

- **list**
  - List all tasks in Google Tasks
  - Optional input: `cursor` (string): Cursor for pagination
  - Returns a list of all tasks

- **create**
  - Create a new task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `title` (string, required): Task title
    - `notes` (string, optional): Task notes
    - `due` (string, optional): Due date
  - Returns confirmation of task creation

- **update**
  - Update an existing task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `id` (string, required): Task ID
    - `uri` (string, required): Task URI
    - `title` (string, optional): New task title
    - `notes` (string, optional): New task notes
    - `status` (string, optional): New task status (""needsAction"" or ""completed"")
    - `due` (string, optional): New due date
  - Returns confirmation of task update

- **delete**
  - Delete a task in Google Tasks
  - Input:
    - `taskListId` (string, required): Task list ID
    - `id` (string, required): Task ID
  - Returns confirmation of task deletion

- **clear**
  - Clear completed tasks from a Google Tasks task list
  - Input: `taskListId` (string, required): Task list ID
  - Returns confirmation of cleared tasks

### Resources

The server provides access to Google Tasks resources:

- **Tasks** (`gtasks:///<task_id>`)
  - Represents individual tasks in Google Tasks
  - Supports reading task details including title, status, due date, notes, and other metadata
  - Can be listed, read, created, updated, and deleted using the provided tools

## Getting started

1. [Create a new Google Cloud project](https://console.cloud.google.com/projectcreate)
2. [Enable the Google Tasks API](https://console.cloud.google.com/workspace-api/products)
3. [Configure an OAuth consent screen](https://console.cloud.google.com/apis/credentials/consent) (""internal"" is fine for testing)
4. Add scopes `https://www.googleapis.com/auth/tasks`
5. [Create an OAuth Client ID](https://console.cloud.google.com/apis/credentials/oauthclient) for application type ""Desktop App""
6. Download the JSON file of your client's OAuth keys
7. Rename the key file to `gcp-oauth.keys.json` and place into the root of this repo (i.e. `gcp-oauth.keys.json`)

Make sure to build the server with either `npm run build` or `npm run watch`.

### Installing via Smithery

To install Google Tasks Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@zcaceres/gtasks):

```bash
npx -y @smithery/cli install @zcaceres/gtasks --client claude
```

### Authentication

To authenticate and save credentials:

1. Run the server with the `auth` argument: `npm run start auth`
2. This will open an authentication flow in your system browser
3. Complete the authentication process
4. Credentials will be saved in the root of this repo (i.e. `.gdrive-server-credentials.json`)

### Usage with Desktop App

To integrate this server with the desktop app, add the following to your app's server configuration:

```json
{
  ""mcpServers"": {
    ""gtasks"": {
      ""command"": ""/opt/homebrew/bin/node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}
```
","Star
 63",2025-06-24T08:43:26.213755,gtasks-mcp - MCP Server | Model Context Protocol Integration,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcacere...","['mcp server', 'model context protocol', 'ai integration', 'gtasks-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'gtasks-mcp', 'description': '# Google Tasks MCP Server\n\n![gtasks mcp logo](./logo.jpg)\n[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcacere...', 'url': 'https://github.com/zcaceres/gtasks-mcp', 'codeRepository': 'https://github.com/zcaceres/gtasks-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,Automation,Search Engine,,"Authentication, Resources, Tools","{
  ""mcpServers"": {
    ""gtasks"": {
      ""command"": ""/opt/homebrew/bin/node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}",,MIT License,2025-06-21,zcaceres,"This MCP server integrates with Google Tasks to allow listing, reading, searching, creating, updating, and deleting tasks."
https://github.com/olalonde/mcp-human,olalonde/mcp-human,"# MCP-Human: Human Assistance for AI Assistants

A Model Context Protocol (MCP) server that enables AI assistants to get human input when needed. This tool creates tasks on Amazon Mechanical Turk that let real humans answer questions from AI systems. While primarily a proof-of-concept, it demonstrates how to build human-in-the-loop AI systems using the MCP standard. See [limitations](#limitations) for current constraints.

![we need to go deeper](./deeper.gif)

## Setup

### Prerequisites

- Node.js 16+
- AWS credentials with MTurk permissions. See [instructions below](#setting-up-aws-user-with-mechanical-turk-access).
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html) (recommended for setting aws credentials)

### Configuring AWS credentials

```sh
# Configure AWS credentials for profile mcp-human
export AWS_ACCESS_KEY_ID=""your_access_key""
export AWS_SECRET_ACCESS_KEY=""your_secret_key""
aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID} --profile mcp-human
aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY} --profile mcp-human
```

### Configuring MCP server with your MCP client

### Claude code

Sandbox mode:

```sh
claude mcp add human -- npx -y mcp-human@latest
```

The server defaults to [sandbox mode](https://workersandbox.mturk.com/) (for testing). If you want to submit real requests, use `MTURK_SANDBOX=false`.

```sh
claude mcp add human -e MTURK_SANDBOX=false -- npx -y mcp-human@latest
```

### Generic

Update the configuration of your MCP client to the following:

```json
{
  ""mcpServers"": {
    ""human"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-human@latest""]
    }
  }
}
```

e.g.: Claude Desktop (MacOS): `~/Library/Application\ Support/Claude/claude_desktop_config.json`

## Configuration

The server can be configured with the following environment variables:

| Variable         | Description                                        | Default                          |
| ---------------- | -------------------------------------------------- | -------------------------------- |
| `MTURK_SANDBOX`  | Use MTurk sandbox (`true`) or production (`false`) | `true`                           |
| `AWS_REGION`     | AWS region for MTurk                               | `us-east-1`                      |
| `AWS_PROFILE`    | AWS profile to use for credentials                 | `mcp-human`                      |
| `DEFAULT_REWARD` | The reward amount in USD.                          | `0.05`                           |
| `FORM_URL`       | URL where the form is hosted. Needs to be https.   | `https://syskall.com/mcp-human/` |

## Setting Up AWS User with Mechanical Turk Access

To create an AWS user with appropriate permissions for Mechanical Turk:

1. **Log in to the AWS Management Console**:

   - Go to https://aws.amazon.com/console/
   - Sign in as a root user or an administrator

2. **Create a new IAM User**:

   - Navigate to IAM (Identity and Access Management)
   - Click ""Users"" > ""Create user""
   - Enter a username (e.g., `mturk-api-user`)
   - Click ""Next"" to proceed to permissions

3. **Set Permissions**:

   - Choose ""Attach existing policies directly""
   - Search for and select `AmazonMechanicalTurkFullAccess`
   - If you need more granular control, you can create a custom policy with specific MTurk permissions
   - Click ""Next"" and then ""Create user""

4. **Create Access Keys**:

   - After user creation, click on the username to go to their detail page
   - Go to the ""Security credentials"" tab
   - In the ""Access keys"" section, click ""Create access key""
   - Choose ""Application running outside AWS"" or appropriate option
   - Click through the wizard and finally ""Crehttps://github.com/JoshuaRileyDev/simulator-mcp-server,JoshuaRileyDev/simulator-mcp-server,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized interface.

## Features

- List available iOS simulators
- Boot and shutdown simulators
- Install .app bundles on simulators
- Launch installed apps by bundle ID

## Installation
Add the following to your Claude Config JSON file
```
{
  ""mcpServers"": {
    ""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]
    }
  }
}
```","Star
 43",2025-06-24T08:43:26.213751,simulator-mcp-server - MCP Server | Model Context Protocol Integration,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...","['mcp server', 'model context protocol', 'ai integration', 'simulator-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'simulator-mcp-server', 'description': '# iOS Simulator MCP Server\n\nA Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...', 'url': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'codeRepository': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,AI Tools,Text Processing,,,"""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]",,,,JoshuaRileyDev,A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized inte...
https://github.com/kiwamizamurai/mcp-kibela-server,kiwamizamurai/mcp-kibela-server,"# Kibela MCP Server
![NPM Version](https://img.shields.io/npm/v/%40kiwamizamurai%2Fmcp-kibela-server)
![NPM Downloads](https://img.shields.io/npm/dm/%40kiwamizamurai%2Fmcp-kibela-server)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![smithery badge](https://smithery.ai/badge/@kiwamizamurai/mcp-kibela-server)](https://smithery.ai/server/@kiwamizamurai/mcp-kibela-server)
[![Build and Push Docker Image](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/docker.yml/badge.svg?branch=main)](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/docker.yml)
[![Lint](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/lint.yml/badge.svg?branch=main)](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/lint.yml)

MCP server implementation for Kibela API integration, enabling LLMs to interact with Kibela content.

<img width=""320"" alt=""Example"" src=""https://github.com/user-attachments/assets/eeed8f45-eb24-456d-bb70-9e738aa1bfb3"" />

<a href=""https://glama.ai/mcp/servers/m21nkeig1p""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/m21nkeig1p/badge"" alt=""Kibela Server MCP server"" /></a>

## Features

- Search notes with advanced filters
- Get your latest notes
- Get note content and comments
- Manage groups and folders
- Like/unlike notes
- List users
- View note attachments
- View recently viewed notes
- Get notes by path

## Configuration

### Environment Variables

- `KIBELA_TEAM`: Your Kibela team name (required)
- `KIBELA_TOKEN`: Your Kibela API token (required)

## Cursor Integration

Add to your `~/.cursor/mcp.json`:

```json
{
    ""mcpServers"": {
        ""kibela"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@kiwamizamurai/mcp-kibela-server""],
            ""env"": {
                ""KIBELA_TEAM"": ""YOUR_TEAM_NAME"",
                ""KIBELA_TOKEN"": ""YOUR_TOKEN""
            }
        }
    }
}
```

If you want to use docker instead

```json
{
    ""mcpServers"": {
        ""kibela"": {
            ""command"": ""docker"",
            ""args"": [
                ""run"",
                ""-i"",
                ""--rm"",
                ""-e"",
                ""KIBELA_TEAM"",
                ""-e"",
                ""KIBELA_https://github.com/JoshuaRileyDev/simulator-mcp-server,JoshuaRileyDev/simulator-mcp-server,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized interface.

## Features

- List available iOS simulators
- Boot and shutdown simulators
- Install .app bundles on simulators
- Launch installed apps by bundle ID

## Installation
Add the following to your Claude Config JSON file
```
{
  ""mcpServers"": {
    ""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]
    }
  }
}
```","Star
 43",2025-06-24T08:43:26.213751,simulator-mcp-server - MCP Server | Model Context Protocol Integration,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...","['mcp server', 'model context protocol', 'ai integration', 'simulator-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'simulator-mcp-server', 'description': '# iOS Simulator MCP Server\n\nA Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...', 'url': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'codeRepository': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,AI Tools,Text Processing,,,"""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]",,,,JoshuaRileyDev,A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized inte...
https://github.com/kiwamizamurai/mcp-kibela-server,kiwamizamurai/mcp-kibela-server,"# Kibela MCP Server
![NPM Version](https://img.shields.io/npm/v/%40kiwamizamurai%2Fmcp-kibela-server)
![NPM Downloads](https://img.shields.io/npm/dm/%40kiwamizamurai%2Fmcp-kibela-server)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![smithery badge](https://smithery.ai/badge/@kiwamizamurai/mcp-kibela-server)](https://smithery.ai/server/@kiwamizamurai/mcp-kibela-server)
[![Build and Push Docker Image](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/docker.yml/badge.svg?branch=main)](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/docker.yml)
[![Lint](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/lint.yml/badge.svg?branch=main)](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/lint.yml)

MCP server implementation for Kibela API integration, enabling LLMs to interact with Kibela content.

<img width=""320"" alt=""Example"" src=""https://github.com/user-attachments/assets/eeed8f45-eb24-456d-bb70-9e738aa1bfb3"" />

<a href=""https://glama.ai/mcp/servers/m21nkeig1p""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/m21nkeig1p/badge"" alt=""Kibela Server MCP server"" /></a>

## Features

- Search notes with advanced filters
- Get your latest notes
- Get note content and comments
- Manage groups and folders
- Like/unlike notes
- List users
- View note attachments
- View recently viewed notes
- Get notes by path

## Configuration

### Environment Variables

- `KIBELA_TEAM`: Your Kibela team name (required)
- `KIBELA_TOKEN`: Your Kibela API token (required)

## Cursor Integration

Add to your `~/.cursor/mcp.json`:

```json
{
    ""mcpServers"": {
        ""kibela"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@kiwamizamurai/mcp-kibela-server""],
            ""env"": {
                ""KIBELA_TEAM"": ""YOUR_TEAM_NAME"",
                ""KIBELA_TOKEN"": ""YOUR_TOKEN""
            }
        }
    }
}
```

If you want to use docker instead

```json
{
    ""mcpServers"": {
        ""kibela"": {
            ""command"": ""docker"",
            ""args"": [
                ""run"",
                ""-i"",
                ""--rm"",
                ""-e"",
                ""KIBELA_TEAM"",
                ""-e"",
                ""KIBELA_TOKEN"",
                ""ghcr.io/kiwamizamurai/mcp-kibela-server:latest""
            ],
            ""env"": {
                ""KIBELA_TEAM"": ""YOUR_TEAM_NAME"",
                ""KIBELA_TOKEN"": ""YOUR_TOKEN""
            }
        }
    }
}
```

## Tools

### kibela_search_notes
Search Kibela notes with given query
- Input:
  - `query` (string): Search query
  - `coediting` (boolean, optional): Filter by co-editing status
  - `isArchived` (boolean, optional): Filter by archive status
  - `sortBy` (string, optional): Sort by (RELEVANT, CONTENT_UPDATED_AT)
  - `userIds` (string[], optional): Filter by user IDs
  - `folderIds` (string[], optional): Filter by folder IDs
- Returns: List of matching notes with ID, title, URL, author, groups and more

### kibela_get_my_notes
Get your latest notes from Kibela
- Input:
  - `limit` (number, optional): Number of notes to fetch (default: 15)
- Returns: List of your latest notes with author information

### kibela_get_note_content
Get content and comments of a specific note
- Input:
  - `id` (string): Note ID
  - `include_image_data` (boolean, optional): Whether to include image data URLs in the response (default: false)
- Returns: Full note content including HTML, comments, attachments, groups, folders and more

### kibela_get_groups
Get list of accessible groups
- Input: None
- Returns: List of groups with details like privacy settings and permissions

### kibela_get_group_folders
Get folders in a group
- Input:
  - `groupId` (string): Group ID
  - `parentFolderId` (string, optional): Parent folder ID for nested folders
- Returns: List of folders with their notes and metadata

### kibhttps://github.com/ananddtyagi/webpage-screenshot-mcp,ananddtyagi/webpage-screenshot-mcp,"# Webpage Screenshot MCP Server

An MCP (Model Context Protocol) server that captures screenshots of web pages using Puppeteer. This server allows AI agents to visually verify web applications and see their progress when generating web apps.

![Screen Recording May 27 2025 (2)](https://github.com/user-attachments/assets/9f186ec4-5a5c-449b-9a30-a5ec0cdba695)


## Features

- **Full page screenshots**: Capture entire web pages or just the viewport
- **Element screenshots**: Target specific elements using CSS selectors
- **Multiple formats**: Support for PNG, JPEG, and WebP formats
- **Customizable options**: Set viewport size, image quality, wait conditions, and delays
- **Base64 encoding**: Returns screenshots as base64 encoded images for easy integration
- **Authentication support**: Manual login and cookie persistence
- **Default browser integration**: Use your system's default browser for a more natural experience
- **Session persistence**: Keep browser sessions open for multi-step workflows

## Installation

To install and build the MCP:

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/ananddtyagi/webpage-screenshot-mcp.git
cd webpage-screenshot-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

The MCP server is built using TypeScript and compiled to JavaScript. The `dist` folder contains the compiled JavaScript files. 

### Adding to Claude or Cursor

To add this MCP to Claude Desktop or Cursor:

1. **Claude Desktop**:
   - Go to Settings > Developer
   - Click ""Edit Config""
   - Add the following:

   ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [
        ""~/path/to/webpage-screenshot-mcp/dist/index.js""
      ]
    }
   ```
   - Save and reload Claude

2. **Cursor**:
   - Open Cursor and go to Cursor Settings > MCP
   - Click ""Add new global MCP server""
   - Add the following:
  
  ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [""~/path/to/webpage-screenshot-mcp/dist/index.js""]
    }
   ```

   - Save and reload Cursor

## Usage

### Tools

This MCP server provides several tools:

#### 1. login-and-wait

Opens a webpage in a visible browser window for manual login, waits for user to complete login, then saves cookies.

```json
{
  ""url"": ""https://example.com/login"",
  ""waitMinutes"": 5,
  ""successIndicator"": "".dashboard-welcome"",
  ""useDefaultBrowser"": true
}
```

- `url` (required): The URL of the login page
- `waitMinutes` (optional): Maximum minutes to wait for login (default: 5)
- `successIndicator` (optional): CSS selector or URL pattern that indicates successful login
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: true)

#### 2. screenshot-page

Captures a screenshot of a given URL and returns it as base64 encoded image.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""fullPage"": true,
  ""width"": 1920,
  ""height"": 1080,
  ""format"": ""png"",
  ""quality"": 80,
  ""waitFor"": ""networkidle2"",
  ""delay"": 500,
  ""useSavedAuth"": true,
  ""reuseAuthPage"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage to screenshot
- `fullPage` (optional): Whether to capture the full page or just the viewport (default: true)
- `width` (optional): Viewport width in pixels (default: 1920)
- `height` (optional): Viewport height in pixels (default: 1080)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `waitFor` (optional): When to consider page loaded - ""load"", ""domcontentloaded"", ""networkidle0"", or ""networkidle2"" (default: ""networkidle2"")
- `delay` (optional): Additional delay in milliseconds after page load (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `reuseAuthPage` (optional): Whether to use the existing authenticated page (default: false)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 3. screenshot-element

Captures a screenshot ofhttps://github.com/ananddtyagi/webpage-screenshot-mcp,ananddtyagi/webpage-screenshot-mcp,"# Webpage Screenshot MCP Server

An MCP (Model Context Protocol) server that captures screenshots of web pages using Puppeteer. This server allows AI agents to visually verify web applications and see their progress when generating web apps.

![Screen Recording May 27 2025 (2)](https://github.com/user-attachments/assets/9f186ec4-5a5c-449b-9a30-a5ec0cdba695)


## Features

- **Full page screenshots**: Capture entire web pages or just the viewport
- **Element screenshots**: Target specific elements using CSS selectors
- **Multiple formats**: Support for PNG, JPEG, and WebP formats
- **Customizable options**: Set viewport size, image quality, wait conditions, and delays
- **Base64 encoding**: Returns screenshots as base64 encoded images for easy integration
- **Authentication support**: Manual login and cookie persistence
- **Default browser integration**: Use your system's default browser for a more natural experience
- **Session persistence**: Keep browser sessions open for multi-step workflows

## Installation

To install and build the MCP:

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/ananddtyagi/webpage-screenshot-mcp.git
cd webpage-screenshot-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

The MCP server is built using TypeScript and compiled to JavaScript. The `dist` folder contains the compiled JavaScript files. 

### Adding to Claude or Cursor

To add this MCP to Claude Desktop or Cursor:

1. **Claude Desktop**:
   - Go to Settings > Developer
   - Click ""Edit Config""
   - Add the following:

   ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [
        ""~/path/to/webpage-screenshot-mcp/dist/index.js""
      ]
    }
   ```
   - Save and reload Claude

2. **Cursor**:
   - Open Cursor and go to Cursor Settings > MCP
   - Click ""Add new global MCP server""
   - Add the following:
  
  ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [""~/path/to/webpage-screenshot-mcp/dist/index.js""]
    }
   ```

   - Save and reload Cursor

## Usage

### Tools

This MCP server provides several tools:

#### 1. login-and-wait

Opens a webpage in a visible browser window for manual login, waits for user to complete login, then saves cookies.

```json
{
  ""url"": ""https://example.com/login"",
  ""waitMinutes"": 5,
  ""successIndicator"": "".dashboard-welcome"",
  ""useDefaultBrowser"": true
}
```

- `url` (required): The URL of the login page
- `waitMinutes` (optional): Maximum minutes to wait for login (default: 5)
- `successIndicator` (optional): CSS selector or URL pattern that indicates successful login
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: true)

#### 2. screenshot-page

Captures a screenshot of a given URL and returns it as base64 encoded image.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""fullPage"": true,
  ""width"": 1920,
  ""height"": 1080,
  ""format"": ""png"",
  ""quality"": 80,
  ""waitFor"": ""networkidle2"",
  ""delay"": 500,
  ""useSavedAuth"": true,
  ""reuseAuthPage"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage to screenshot
- `fullPage` (optional): Whether to capture the full page or just the viewport (default: true)
- `width` (optional): Viewport width in pixels (default: 1920)
- `height` (optional): Viewport height in pixels (default: 1080)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `waitFor` (optional): When to consider page loaded - ""load"", ""domcontentloaded"", ""networkidle0"", or ""networkidle2"" (default: ""networkidle2"")
- `delay` (optional): Additional delay in milliseconds after page load (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `reuseAuthPage` (optional): Whether to use the existing authenticated page (default: false)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 3. screenshot-element

Captures a screenshot of a specific element on a webpage using a CSS selector.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""selector"": "".user-profile"",
  ""waitForSelector"": true,
  ""format"": ""png"",
  ""quality"": 80,
  ""padding"": 10,
  ""useSavedAuth"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage
- `selector` (required): CSS selector for the element to screenshot
- `waitForSelector` (optional): Whether to wait for the selector to appear (default: true)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `padding` (optional): Padding around the element in pixels (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 4. clear-auth-cookies

Clears saved authentication cookies for a specific domain or all domains.

```json
{
  ""url"": ""https://example.com""
}
```

- `url` (optional): URL of the domain to clear cookies for. If not provided, clears all cookies.

## Default Browser Mode

The default browser mode allows you to use your system's regular browser (Chrome, Edge, etc.) instead of Puppeteer's bundled Chromium. This is useful for:

1. Using your existing browser sessions and extensions
2. Manually logging in to websites with your saved credentials
3. Having a more natural browsing experience for multi-step workflows
4. Testing with the same browser environment as your users

To enable default browser mode, set `useDefaultBrowser: true` and `visibleBrowser: true` in your tool parameters.

### How Default Browser Mode Works

When you enable default browser mode:

1. The tool will attempt to locate your system's default browser (Chrome, Edge, etc.)
2. It launches your browser with remote debugging enabled on a random port
3. Puppeteer connects to this browser instance instead of launching its own
4. Your existing profiles, extensions, and cookies are available during the session
5. The browser window remains visible so you can interact with it manually

This mode is particularly useful for workflows that require authentication or complex user interactions.

## Browser Persistence

The MCP server can maintain a persistent browser session across multiple tool calls:

1. When you use `login-and-wait`, the browser session is kept open
2. Subsequent calls to `screenshot-page` or `screenshot-element` with `reuseAuthPage: true` will use the same page
3. This allows for multi-step workflows without having to re-authenticate

## Cookie Management

Cookies are automatically saved for each domain you visit:

1. After using `login-and-wait`, cookies are saved to the `.mcp-screenshot-cookies` directory in your home folder
2. These cookies are automatically loaded when visiting the same domain again with `useSavedAuth: true`
3. You can clear cookies using the `clear-auth-cookies` tool

## Example Workflow: Protected Page Screenshots

Here's an example workflow for taking screenshots of pahttps://github.com/Gaffx/volatility-mcp,Gaffx/volatility-mcp,"![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)
<p align=""center"">
<img src=""assets/logo.png"" height=""300"">
</p>
<h1 align=""center"">
Your AI Assistant in Memory Forensics
</h1>

## Overview
Volatility MCP seamlessly integrates Volatility 3's powerful memory analysis with FastAPI and the Model Context Protocol (MCP). Experience memory forensics without barriers as plugins like `pslist` and `netscan` become accessible through clean REST APIs, connecting memory artifacts directly to AI assistants and web applications

## Features
* **Volatility 3 Integration:** Leverages the Volatility 3 framework for memory image analysis.
* **FastAPI Backend:** Provides RESTful APIs to interact with Volatility plugins.
* **Web Front End Support (future feature):** Designed to connect with a web-based front end for interactive analysis.
* **Model Context Protocol (MCP):** Enables standardized communication with MCP clients like Claude Desktop.
* **Plugin Support:** Supports various Volatility plugins, including `pslist` for process listing and `netscan` for network connection analysis.


## Architecture

The project architecture consists of the following components:

* **MCP Client:** MCP client like Claude Desktop that interacts with the FastAPI backend.
* **FastAPI Server:** A Python-based server that exposes Volatility plugins as API endpoints.
* **Volatility 3:** The memory forensics framework performing the analysis.

This architecture allows users to analyze memory images through MCP clients like Claude Desktop. Users can use natural language prompts to perform memory forensics analysis such as
show me the list of the processes in memory image x, or show me all the external connections made

## Getting Started

### Prerequisites

* Python 3.7+ installed on your system
* Volatility 3 binary installed (see [Volatility 3 Installation Guide](https://github.com/volatilityfoundation/volatility3?tab=readme-ov-file#installing)) and added to your env path called **VOLATILITY_BIN**

### Installation

1. Clone the repository:

    ```
    git clone <repository_url>
    cd <repository_directory>
    ```

2. Install the required Python dependencies:

    ```
    pip install -r requirements.txt
    ```

3. Start the FastAPI server to expose Volatility 3 APIs:

    ```
    uvicorn volatility_fastapi_server:app 
    ```
4. Install Claude Desktop (see [Claude Desktop](https://claude.ai/download)
5. To configure Claude Desktop as a volatility MCP client, navigate to Claude → Settings → Developer → Edit Config, locate the claude_desktop_config.json file, and insert the following configuration details
6. Please note that the `-i` option in the config.json file specifies the directory path of your memory image file.

   ```
       {
        ""mcpServers"": {
          ""vol"": {
            ""command"": ""python"",
            ""args"": [
              ""/ABSOLUTE_PATH_TO_MCP-SERVER/vol_mcp_server.py"", ""-i"",     
              ""/ABSOLUTE_PATH_TO_MEMORY_IMAGE/<memory_image>""
            ]
          }
        }
    }
   ```
Alternatively, update this file directly:

`/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json`

### Usage

1. Start the FastAPI server as described above.
2. Connect an MCP client (e.g., Claude Desktop) to the FastAPI server.
3. Start the prompt by asking questions regarding the memory image in scope, such as showing me the running processes, creating a tree relationship graph for process x, or showing me all external RFC1918 connections.

![image](https://github.com/user-attachments/assets/23f6fd4f-76b4-4255-a0a6-534ed3459bb3)
![image](https://github.com/user-attachments/assets/e5cd74ae-72ff-4c5b-8bd8-fbeb13488a70)
![image](https://github.com/user-attachments/assets/779707ef-4910-4503-b6b0-43f6c37075ef)
![image](https://github.com/user-attachments/assets/668e9b91-463a-424f-a3ef-ee2baf44308d)

## Future Features and Enhancements

*  **Native Volatility Python Integration:** Incorporate Volatility Python SDK directly in the code base as opposed to subprocess volatility binary
*   **Yara Integration:** Implement functionality to dump a process from memory and scan it with Yara rules for malware analysis.
*   **Multi-Image Analysis:** Enable the analysis of multiple memory images simultaneously to cohttps://github.com/Gaffx/volatility-mcp,Gaffx/volatility-mcp,"![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)
<p align=""center"">
<img src=""assets/logo.png"" height=""300"">
</p>
<h1 align=""center"">
Your AI Assistant in Memory Forensics
</h1>

## Overview
Volatility MCP seamlessly integrates Volatility 3's powerful memory analysis with FastAPI and the Model Context Protocol (MCP). Experience memory forensics without barriers as plugins like `pslist` and `netscan` become accessible through clean REST APIs, connecting memory artifacts directly to AI assistants and web applications

## Features
* **Volatility 3 Integration:** Leverages the Volatility 3 framework for memory image analysis.
* **FastAPI Backend:** Provides RESTful APIs to interact with Volatility plugins.
* **Web Front End Support (future feature):** Designed to connect with a web-based front end for interactive analysis.
* **Model Context Protocol (MCP):** Enables standardized communication with MCP clients like Claude Desktop.
* **Plugin Support:** Supports various Volatility plugins, including `pslist` for process listing and `netscan` for network connection analysis.


## Architecture

The project architecture consists of the following components:

* **MCP Client:** MCP client like Claude Desktop that interacts with the FastAPI backend.
* **FastAPI Server:** A Python-based server that exposes Volatility plugins as API endpoints.
* **Volatility 3:** The memory forensics framework performing the analysis.

This architecture allows users to analyze memory images through MCP clients like Claude Desktop. Users can use natural language prompts to perform memory forensics analysis such as
show me the list of the processes in memory image x, or show me all the external connections made

## Getting Started

### Prerequisites

* Python 3.7+ installed on your system
* Volatility 3 binary installed (see [Volatility 3 Installation Guide](https://github.com/volatilityfoundation/volatility3?tab=readme-ov-file#installing)) and added to your env path called **VOLATILITY_BIN**

### Installation

1. Clone the repository:

    ```
    git clone <repository_url>
    cd <repository_directory>
    ```

2. Install the required Python dependencies:

    ```
    pip install -r requirements.txt
    ```

3. Start the FastAPI server to expose Volatility 3 APIs:

    ```
    uvicorn volatility_fastapi_server:app 
    ```
4. Install Claude Desktop (see [Claude Desktop](https://claude.ai/download)
5. To configure Claude Desktop as a volatility MCP client, navigate to Claude → Settings → Developer → Edit Config, locate the claude_desktop_config.json file, and insert the following configuration details
6. Please note that the `-i` option in the config.json file specifies the directory path of your memory image file.

   ```
       {
        ""mcpServers"": {
          ""vol"": {
            ""command"": ""python"",
            ""args"": [
              ""/ABSOLUTE_PATH_TO_MCP-SERVER/vol_mcp_server.py"", ""-i"",     
              ""/ABSOLUTE_PATH_TO_MEMORY_IMAGE/<memory_image>""
            ]
          }
        }
    }
   ```
Alternatively, update this file directly:

`/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json`

### Usage

1. Start the FastAPI server as described above.
2. Connect an MCP client (e.g., Claude Desktop) to the FastAPI server.
3. Start the prompt by asking questions regarding the memory image in scope, such as showing me the running processes, creating a tree relationship graph for process x, or showing me all external RFC1918 connections.

![image](https://github.com/user-attachments/assets/23f6fd4f-76b4-4255-a0a6-534ed3459bb3)
![image](https://github.com/user-attachments/assets/e5cd74ae-72ff-4c5b-8bd8-fbeb13488a70)
![image](https://github.com/user-attachments/assets/779707ef-4910-4503-b6b0-43f6c37075ef)
![image](https://github.com/user-attachments/assets/668e9b91-463a-424f-a3ef-ee2baf44308d)

## Future Features and Enhancements

*  **Native Volatility Python Integration:** Incorporate Volatility Python SDK directly in the code base as opposed to subprocess volatility binary
*   **Yara Integration:** Implement functionality to dump a process from memory and scan it with Yara rules for malware analysis.
*   **Multi-Image Analysis:** Enable the analysis of multiple memory images simultaneously to correlate events and identify patterns across different systems.
*   **Adding more Volatility Plugins:** add more volatility plugins to expand the scope of memory analysis
*   **GUI Enhancements:** Develop a user-friendly web interface for interactive memory analysis and visualization.
*   **Automated Report Generation:** Automate the generation of detailed reports summarizing the findings of memory analysis.
*   **Advanced Threat Detection:** Incorporate advanced techniques for detecting sophisticated threats and anomalies in memory.

## Contributing

Contributions are welcome! Please follow these steps to contribute:

1. Fork this repository.
2. Create a new branch (`git checkout -b feature/my-feature`).
3. Commit your changes (`git commit -m 'Add some feature'`).
4. Push to your branch (`git push origin feature/my-feature`).
5. Open a pull request.

[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/gaffx-volatility-mcp-badge.png)](https://mseep.ai/app/gaffx-volatility-mcp)


","Star
 19",2025-06-24T08:43:26.213745,volatility-mcp - MCP Server | Model Context Protocol Integration,"![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)
<p align=""center"">
<img src=""assets/logo.png"" height=""300"">
</p>
<h...","['mcp server', 'model context protocol', 'ai integration', 'volatility-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'volatility-mcp', 'description': '![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)\n<p align=""center"">\n<img src=""assets/logo.png"" height=""300"">\n</p>\n<h...', 'url': 'https://github.com/Gaffx/volatility-mcp', 'codeRepository': 'https://github.com/Gaffx/volatility-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,API Integration,Image Processing,pip install -r requirements.txt,"Installation, Prerequisites, Usage","""vol"": {
            ""command"": ""python"",
            ""args"": [
              ""/ABSOLUTE_PATH_TO_MCP-SERVER/vol_mcp_server.py"", ""-i"",     
              ""/ABSOLUTE_PATH_TO_MEMORY_IMAGE/<memory_image>""
            ]","-r, requirements.txt",Apache License 2.0,2025-06-23,Gaffx,"<img src=""assets/logo.png"" height=""300"">"
https://github.com/rad-security/mcp-server,rad-security/mcp-server,"# RAD Security MCP Server

A Model Context Protocol (MCP) server for RAD Security, providing AI-powered security insights for Kubernetes and cloud environments.

## Installation

```bash
npm install @rad-security/mcp-server
```

## Usage

### Prerequisites

- Node.js 20.x or higher

### Environment Variables

The following environment are required required to use the MCP server with Rad Security:

```bash
RAD_SECURITY_ACCESS_KEY_ID=""your_access_key""
RAD_SECURITY_SECRET_KEY=""your_secret_key""
RAD_SECURITY_ACCOUNT_ID=""your_account_id""
```

but you can also use few operations without authentication:

- List CVEs
- Get details of a specific CVE
- Get latest 30 CVEs
- List Kubernetes resource misconfigurathttps://github.com/mem0ai/mem0-mcp,mem0ai/mem0-mcp,"# MCP Server with Mem0 for Managing Coding Preferences

This demonstrates a structured approach for using an [MCP](https://modelcontextprotocol.io/introduction) server with [mem0](https://mem0.ai) to manage coding preferences efficiently. The server can be used with Cursor and provides essential tools for storing, retrieving, and searching coding preferences.

## Installation

1. Clone this repository
2. Initialize the `uv` environment:

```bash
uv venv
```

3. Activate the virtual environment:

```bash
source .venv/bin/activate
```

4. Install the dependencies using `uv`:

```bash
# Install in editable mode from pyproject.toml
uv pip install -e .
```

5. Update `.env` file in the root directory with your mem0 API key:

```bash
MEM0_API_KEY=your_api_key_here
```

## Usage

1. Start the MCP server:

```bash
uv run main.py
```

2. In Cursor, connect to the SSE endpoint, follow this [doc](https://docs.cursor.com/context/model-context-protocol) for reference:

```
http://0.0.0.0:8080/sse
```

3. Open the Composer in Cursor and switch to `Agent` mode.

## Demo with Cursor

https://github.com/user-attachments/assets/56670550-fb11-4850-9905-692d3496231c

## Features

The server provides three main tools for managing code preferences:

1. `add_coding_preference`: Store code snippets, implementation details, and coding patterns with comprehensive context including:
   - Complete code with dependencies
   - Language/framework versions
   - Setup instructions
   - Documentation and comments
   - Example usage
   - Best practices

2. `get_all_coding_preferences`: Retrieve all stored coding preferences to analyze patterns, review implementations, and ensure no relevant information is missed.

3. `search_coding_preferences`: Semantically search through stored coding preferences to find relevant:
   - Code implementations
   - Programming solutions
   - Best practices
   - Setup guides
   - Technical documentation

## Why?

This implementation allows for a persistent coding preferences system that can be accessed via MCP. The SSE-based server can run as a process that agents connect to, use, and disconnect from whenever needed. This pattern fits well with ""cloud-native"" use cases where the server and clients can be decoupled processes on different nodes.

### Server

By default, the server runs on 0.0.0.0:8080 but is configurable with command line arguments like:

```
uv run main.py --host <your host> --port <your port>
```

The server exposes an SSE endpoint at `/sse` that MCP clients can connect to for accessing the coding preferences management tools.

","Star
 354",2025-06-24T08:43:26.213741,mem0-mcp - MCP Server | Model Context Protocol Integration,"# MCP Server with Mem0 for Managing Coding Preferences

This demonstrates a structured approach for using an [MCP](https://modelcontextprotocol.io/introducti...","['mcp server', 'model context protocol', 'ai integration', 'mem0-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mem0-mcp', 'description': '# MCP Server with Mem0 for Managing Coding Preferences\n\nThis demonstrates a structured approach for using an [MCP](https://modelcontextprotocol.io/introducti...', 'url': 'https://github.com/mem0ai/mem0-mcp', 'codeRepository': 'https://github.com/mem0ai/mem0-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Development,Cursor IDE,pip install -e .,Server,,-e,,2025-06-23,mem0ai,"This demonstrates a structured approach for using an MCP server with mem0 to manage coding preferences efficiently. The server can be used with Cursor and provides essential tools for storing, retriev..."
https://github.com/benborla/mcp-server-mysql,benborla/mcp-server-mysql,"
# MCP Server for MySQL based on NodeJS
[![smithery badge](https://smithery.ai/badge/@benborla29/mcp-server-mysql)](https://smithery.ai/server/@benborla29/mcp-server-mysql)

![Demo](assets/demo.gif)
<a href=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql/badge"" />
</a>

A Model Context Protocol server that provides access to MySQL databases. This server enables LLMs to inspect database schemas and execute SQL queries.

## Table of Contents
- [Requirements](#requirements)
- [Installation](#installation)
  - [Smithery](#https://github.com/mem0ai/mem0-mcp,mem0ai/mem0-mcp,"# MCP Server with Mem0 for Managing Coding Preferences

This demonstrates a structured approach for using an [MCP](https://modelcontextprotocol.io/introduction) server with [mem0](https://mem0.ai) to manage coding preferences efficiently. The server can be used with Cursor and provides essential tools for storing, retrieving, and searching coding preferences.

## Installation

1. Clone this repository
2. Initialize the `uv` environment:

```bash
uv venv
```

3. Activate the virtual environment:

```bash
source .venv/bin/activate
```

4. Install the dependencies using `uv`:

```bash
# Install in editable mode from pyproject.toml
uv pip install -e .
```

5. Update `.env` file in the root directory with your mem0 API key:

```bash
MEM0_API_KEY=your_api_key_here
```

## Usage

1. Start the MCP server:

```bash
uv run main.py
```

2. In Cursor, connect to the SSE endpoint, follow this [doc](https://docs.cursor.com/context/model-context-protocol) for reference:

```
http://0.0.0.0:8080/sse
```

3. Open the Composer in Cursor and switch to `Agent` mode.

## Demo with Cursor

https://github.com/user-attachments/assets/56670550-fb11-4850-9905-692d3496231c

## Features

The server provides three main tools for managing code preferences:

1. `add_coding_preference`: Store code snippets, implementation details, and coding patterns with comprehensive context including:
   - Complete code with dependencies
   - Language/framework versions
   - Setup instructions
   - Documentation and comments
   - Example usage
   - Best practices

2. `get_all_coding_preferences`: Retrieve all stored coding preferences to analyze patterns, review implementations, and ensure no relevant information is missed.

3. `search_coding_preferences`: Semantically search through stored coding preferences to find relevant:
   - Code implementations
   - Programming solutions
   - Best practices
   - Setup guides
   - Technical documentation

## Why?

This implementation allows for a persistent coding preferences system that can be accessed via MCP. The SSE-based server can run as a process that agents connect to, use, and disconnect from whenever needed. This pattern fits well with ""cloud-native"" use cases where the server and clients can be decoupled processes on different nodes.

### Server

By default, the server runs on 0.0.0.0:8080 but is configurable with command line arguments like:

```
uv run main.py --host <your host> --port <your port>
```

The server exposes an SSE endpoint at `/sse` that MCP clients can connect to for accessing the coding preferences management tools.

","Star
 354",2025-06-24T08:43:26.213741,mem0-mcp - MCP Server | Model Context Protocol Integration,"# MCP Server with Mem0 for Managing Coding Preferences

This demonstrates a structured approach for using an [MCP](https://modelcontextprotocol.io/introducti...","['mcp server', 'model context protocol', 'ai integration', 'mem0-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mem0-mcp', 'description': '# MCP Server with Mem0 for Managing Coding Preferences\n\nThis demonstrates a structured approach for using an [MCP](https://modelcontextprotocol.io/introducti...', 'url': 'https://github.com/mem0ai/mem0-mcp', 'codeRepository': 'https://github.com/mem0ai/mem0-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Development,Cursor IDE,pip install -e .,Server,,-e,,2025-06-23,mem0ai,"This demonstrates a structured approach for using an MCP server with mem0 to manage coding preferences efficiently. The server can be used with Cursor and provides essential tools for storing, retriev..."
https://github.com/benborla/mcp-server-mysql,benborla/mcp-server-mysql,"
# MCP Server for MySQL based on NodeJS
[![smithery badge](https://smithery.ai/badge/@benborla29/mcp-server-mysql)](https://smithery.ai/server/@benborla29/mcp-server-mysql)

![Demo](assets/demo.gif)
<a href=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql/badge"" />
</a>

A Model Context Protocol server that provides access to MySQL databases. This server enables LLMs to inspect database schemas and execute SQL queries.

## Table of Contents
- [Requirements](#requirements)
- [Installation](#installation)
  - [Smithery](#using-smithery)
  - [Clone to Local Repository](#running-from-local-repository)
- [Components](#components)
- [Configuration](#configuration)
- [Environment Variables](#environment-variables)
- [Multi-DB Mode](#multi-db-mode)
- [Schema-Specific Permissions](#schema-specific-permissions)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)

## Requirements

- Node.js v18 or higher
- MySQL 5.7 or higher (MySQL 8.0+ recommended)
- MySQL user with appropriate permissions for the operations you need
- For write operations: MySQL user with INSERT, UPDATE, and/or DELETE privileges

## Installation

There are several ways to install and configure the MCP server but the most common would be checking this website https://smithery.ai/server/@benborla29/mcp-server-mysql

### Cursor

For Cursor IDE, you can install this MCP server with the following command in your project:

1. Visit https://smithery.ai/server/@benborla29/mcp-server-mysql
2. Follow the instruction for Cursor


MCP Get provides a centralized registry of MCP servers and simplifies the installation process.

### Using NPM/PNPM

For manual installation:

```bash
# Using npm
npm install -g @benborla29/mcp-server-mysql

# Using pnpm
pnpm add -g @benborla29/mcp-server-mysql
```

After manual installation, you'll need to configure your LLM application to use the MCP server (see Configuration section below).

### Running from Local Repository

If you want to clone and run this MCP server directly from the source code, follow these steps:

1. **Clone the repository**
   ```bash
   git clone https://github.com/benborla/mcp-server-mysql.git
   cd mcp-server-mysql
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Build the project**
   ```bash
   npm run build
   # or
   pnpm run build
   ```

4. **Configure Claude Desktop**

   Add the following to your Claude Desktop configuration file (`claude_desktop_config.json`):

   ```json
   {
     ""mcpServers"": {
       ""mcp_server_mysql"": {
         ""command"": ""/path/to/node"",
         ""args"": [
           ""/full/path/to/mcp-server-mysql/dist/index.js""
         ],
         ""env"": {
           ""MYSQL_HOST"": ""127.0.0.1"",
           ""MYSQL_PORT"": ""3306"",
           ""MYSQL_USER"": ""root"",
           ""MYSQL_PASS"": ""your_password"",
           ""MYSQL_DB"": ""your_database"",
           ""ALLOW_INSERT_OPERATION"": ""false"",
           ""ALLOW_UPDATE_OPERATION"": ""false"",
           ""ALLOW_DELETE_OPERATION"": ""false"",
           ""PATH"": ""/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/bin:/usr/bin:/bin"", // <--- Important to add the following, run in your terminal `echo ""$(which node)/../""` to get the path
           ""NODE_PATH"": ""/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/lib/node_modules"" // <--- Important to add the following, run in your terminal `echo ""$(which node)/../../lib/node_modules""`
         }
       }
     }
   }
   ```

   Replace:
   - `/path/to/node` with the full path to your Node.js binary (find it with `which node`)
   - `/full/path/to/mcp-server-mysql` with the full path to where you cloned the repository
   - Set the MySQL credentials to match your environment

5. **Test the server**
   ```bash
   # Run the server directly to test
   node dist/index.js
   ```

   If it connects to MySQL successfully, you're ready to use it with Claude Desktop.

## Components

### Tools

- **mysql_query**
  - Execute SQL queries against the connected database
  - Input: `sql` (string): The SQL query to execute
  - By default, limited to READ ONLY operations
  - Optional write operations (when enabled via configuration):
    - INSERT: Add new data to tables (requires `ALLOW_INSERT_OPERATION=true`)
    - UPDATE: Modify existing data (requires `ALLOW_UPDATE_OPERATION=true`)
    - DELETE: Remove data (requires `ALLOW_DELETE_OPERATION=true`)
  - All operations are executed within a transaction with proper commit/rollback handling
  - Supports prepared statements for secure parameter handling
  - Configurable query timeouts and result pagination
  - Built-in query execution statistics

### Resources

The server provides comprehensive database information:

- **Table Schemas**
  - JSON schema information for each table
  - Column names and data types
  - Index information and constraints
  - Foreign key relationships
  - Table statistics and metrics
  - Automatically discovered from database metadata

### Security Features

- SQL injection prevention through prepared statements
- Query whitelisting/blacklisting capabilities
- Rate limiting for query execution
- Query complexity analysis
- Configurable connection encryption
- Read-only transaction enforcement

### Performance Optimizations

- Optimized connection pooling
- Query result caching
- Large result set streaming
- Query execution plan analysis
- Configurable query timeouts

### Monitoring and Debugging

- Comprehensive query logging
- Performance metrics collection
- Error tracking and reporting
- Health check endpoints
- Query execution statistics

## Configuration

### Automatic Configuration with Smithery
If you installed using Smithery, your configuration is already set up. You can view or modify it with:

```bash
smithery configure @benborla29/mcp-server-mysql
```

When reconfiguring, you can update any of the MySQL connection details as well as the write operation settings:

- **Basic connection settings**:
  - MySQL Host, Port, User, Password, Database
  - SSL/TLS configuration (if your database requires secure connections)

- **Write operation permissions**:
  - Allow INSERT Operations: Set to true if you want to allow adding new data
  - Allow UPDATE Operations: Set to true if you want to allow updating existing data
  - Allow DELETE Operations: Set to true if you want to allow deleting data

For security reasons, all write operations are disabled by default. Only enable these settings if you specifically need Claude to modify your database data.

### Advanced Configuration Options
For more control over the MCP server's behavior, you can use these advanced configuration options:

```json
{
  ""mcpServers"": {
    ""mcp_server_mysql"": {
      ""command"": ""/path/to/npx/binary/npx"",
      ""args"": [
        ""-y"",
        ""@benborla29/mcp-server-mysql""
      ],
      ""env"": {
        // Basic connection settings
        ""MYSQL_HOST"": ""127.0.0.1"",
        ""MYSQL_PORT"": ""3306"",
        ""MYSQL_USER"": ""root"",
        ""MYSQL_PASS"": """",
        ""MYSQL_DB"": ""db_name"",
        ""PATH"": ""/path/to/node/bin:/usr/bin:/bin"",
        
        // Performance settings
        ""MYSQL_POOL_SIZE"": ""10"",
        ""MYSQL_QUERY_TIMEOUT"": ""30000"",
        ""MYSQL_CACHE_TTL"": ""60000"",
        
        // Security settings
        ""MYSQL_RATE_LIMIT"": ""100"",
        ""MYSQL_MAX_QUERY_COMPLEXITY"": ""1000"",
        ""MYSQL_SSL"": ""true"",
        
        // Monitoring settings
        ""ENABLE_LOGGING"": ""true"",
        ""MYSQL_LOG_LEVEL"": ""info"",
        ""MYSQL_METRICS_ENABLED"": ""true"",
        
        // Write operation flags
        ""ALLOW_INSERT_OPERATION"": ""false"",
        ""ALLOW_UPDATE_OPERATION"": ""false"",
        ""ALLOW_DELETE_OPERATION"": ""false""
      }
    }
  }
}
```

## Environment Variables

### Basic Connection
- `MYSQL_SOCKET_PATH`: Unix socket path for local connections (e.g., ""/tmp/mysql.sock"")
- `MYSQL_HOST`: MySQL server host (default: ""127.0.0.1"") - ignored if MYSQL_SOCKET_PATH is set
- `MYSQL_PORT`: MySQL server port (default: ""3306"") - ignored if MYSQL_SOCKET_PATH is set
- `MYSQL_USER`: MySQL username (default: ""root"")
- `MYSQL_PASS`: MySQL password
- `MYSQL_DB`: Target database name (leave empty for multi-DB mode)

### Performance Configuration
- `MYSQL_POOL_SIZE`: Connection pool size (default: ""10"")
- `MYSQL_QUERY_TIMEOUT`: Query timeout in milliseconds (default: ""30000"")
- `MYSQL_CACHE_TTL`: Cache time-to-live in milliseconds (default: ""60000"")

### Security Configuration
- `MYSQL_RATE_LIMIT`: Maximum queries per minute (default: ""100"")
- `MYSQL_MAX_QUERY_COMPLEXITY`: Maximum query complexity score (default: ""1000"")
- `MYSQL_SSL`: Enable SSL/TLS encryption (default: ""false"")
- `ALLOW_INSERT_OPERATION`: Enable INSERT operations (default: ""false"")
- `ALLOW_UPDATE_OPERATION`: Enable UPDATE operations (default: ""false"")
- `ALLOW_DELETE_OPERATION`: Enable DELETE operations (default: ""false"")
- `ALLOW_DDL_OPERATION`: Enable DDL operations (default: ""false"")
- `SCHEMA_INSERT_PERMISSIONS`: Schema-specific INSERT permissions
- `SCHEMA_UPDATE_PERMISSIONS`: Schema-specific UPDATE permissions
- `SCHEMA_DELETE_PERMISSIONS`: Schema-specific DELETE permissions
- `SCHEMA_DDL_PERMISSIONS`: Schema-specific DDL permissions
- `MULTI_DB_WRITE_MODE`: Enable write operations in multi-DB mode (default: ""false"")

### Monitoring Configuration
- `MYSQL_ENABLE_LOGGING`: Enable query logging (default: ""false"")
- `MYSQL_LOG_LEVEL`: Logging level (default: ""info"")
- `MYSQL_METRICS_ENABLED`: Enable performance metrics (default: ""false"")

## Multi-DB Mode

MCP-Server-MySQL supports connecting to multiple databases when no specific database is set. This allows the LLM to query any database the MySQL user has access to. For full details, see [README-MULTI-DB.md](./README-MULTI-DB.md).

### Enabling Multi-DB Mode

To enable multi-DB mode, simply leave the `MYSQL_DB` environment variable empty. In multi-DB mode, queries require schema qualification:

```sql
-- Use fully qualified table names
SELECT * FROM database_name.table_name;

-- Or use USE statements to switch between databases
USE database_name;
SELECT * FROM table_name;
```

## Schema-Specific Permissions

For fine-grained control over database operations, MCP-Server-MySQL now supports schema-specific permissions. This allows different databases to have different levels of access (read-only, read-write, etc.).

### Configuration Example

```
SCHEMA_INSERT_PERMISSIONS=development:true,test:true,production:false
SCHEMA_UPDATE_PERMISSIONS=development:true,test:true,production:false
SCHEMA_DELETE_PERMISSIONS=development:false,test:true,production:false
SCHEMA_DDL_PERMISSIONS=development:false,test:true,production:false
```

For complete details and security recommendations, see [README-MULTI-DB.md](./README-MULTI-DB.md).

## Testing

### Database Setup

Before running tests, you need to set up the test database and seed it with test data:

1. **Create Test Database and User**
   ```sql
   -- Connect as root and create test database
   CREATE DATABASE IF NOT EXISTS mcp_test;
   
   -- Create test user with appropriate permissions
   CREATE USER IF NOT EXISTS 'mcp_test'@'localhost' IDENTIFIED BY 'mcp_test_password';
   GRANT ALL PRIVILEGES ON mcp_test.* TO 'mcp_test'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Run Database Setup Script**
   ```bash
   # Run the database setup script
   pnpm run setup:test:db
   ```

   This will create the necessary tables and seed data. The script is located in `scripts/setup-test-db.ts`

3. **Configure Test Environment**
   Create a `.env.test` file in the project root (if not existing):
   ```env
   MYSQL_HOST=127.0.0.1
   MYSQL_PORT=3306
   MYSQL_USER=mcp_test
   MYSQL_PASS=mcp_test_password
   MYSQL_DB=mcp_test
   ```

4. **Update package.json Scripts**
   Add these scripts to your package.json:
   ```json
   {
     ""scripts"": {
       ""setup:test:db"": ""ts-node scripts/setup-test-db.ts"",
       ""pretest"": ""pnpm run setup:test:db"",
       ""test"": ""vitest run"",
       ""test:watch"": ""vitest"",
       ""test:coverage"": ""vitest run --coverage""
     }
   }
   ```

### Running Tests

The project includes a comprehensive test suite to ensure functionality and reliability:

```bash
# First-time setup
pnpm run setup:test:db

# Run all tests
pnpm test
```



## Running evals

The evals package loads an mcp client that then runs the index.ts file, so there is no need to rebuild between tests. You can load environment variables by prefixing the npx command. Full documentation can be found [here](https://www.mcpevals.io/docs).

```bash
OPENAI_API_KEY=your-key  npx mcp-eval evals.ts index.ts
```
## Troubleshooting

### Common Issues

1. **Connection Issues**
   - Verify MySQL server is running and accessible
   - Check credentials and permissions
   - Ensure SSL/TLS configuration is correct if enabled
   - Try connecting with a MySQL client to confirm access

2. **Performance Issues**
   - Adjust connection pool size
   - Configure query timeout values
   - Enable query caching if needed
   - Check query complexity settings
   - Monitor server resource usage

3. **Security Restrictions**
   - Review rate limiting configuration
   - Check query whitelist/blacklist settings
   - Verify SSL/TLS settings
   - Ensure the user has appropriate MySQL permissions

4. **Path Resolution**
If you encounter an error ""Could not connect to MCP server mcp-server-mysql"", explicitly set the path of all required binaries:
```json
{
  ""env"": {
    ""PATH"": ""/path/to/node/bin:/usr/bin:/bin""
  }
}
```

*Where can I find my `node` bin path*
Run the following command to get it:

For **PATH**
```bash
echo ""$(which node)/../""    
```

For **NODE_PATH**
```bash
echo ""$(which node)/../../lib/node_modules""    
```

5. **Claude Desktop Specific Issues**
   - If you see ""Server disconnected"" logs in Claude Desktop, check the logs at `~/Library/Logs/Claude/mcp-server-mcp_server_mysql.log`
   - Ensure you're using the absolute path to both the Node binary and the server script
   - Check if your `.env` file is being properly loaded; use explicit environment variables in the configuration
   - Try running the server directly from the command line to see if there are connection issues
   - If you need write operations (INSERT, UPDATE, DELETE), set the appropriate flags to ""true"" in your configuration:
     ```json
     ""env"": {
       ""ALLOW_INSERT_OPERATION"": ""true"",  // Enable INSERT operations
       ""ALLOW_UPDATE_Ohttps://github.com/MarkusPfundstein/mcp-obsidian,MarkusPfundstein/mcp-obsidian,"# MCP server for Obsidian

MCP server to interact with Obsidian via the Local REST API community plugin.

<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/3wko1bhuek/badge"" alt=""server for Obsidian MCP server"" /></a>

## Components

### Tools

The server implements multiple tools to interact with Obsidian:

- list_files_in_vault: Lists all files and directories in the root directory of your Obsidian vault
- list_files_in_dir: Lists all files and directories in a specific Obsidian directory
- get_file_contents: Return the content of a single file in your vault.
- search: Search for documents matching a specified text query across all files in the vault
- patch_content: Insert content into an existing note relative to a heading, block reference, or frontmatter field.
- append_content: Append content to a new or existing file in the vault.
- delete_file: Delete a file or directory from your vault.

### Example prompts

Its good to first instruct Claude to use Obsidian. Then it will always call the tool.

The use prompts like this:
- Get the contents of the last architecture call note and summarize them
- Search for all files where Azure CosmosDb is mentioned and quickly explain to me the context in which it is mentioned
- Summarize the last meeting notes and put them into a new note 'summary meeting.md'. Add an introduction so that I can send it via email.

## Configuration

### Obsidian REST API Key

There are two ways to configure the environment with the Obsidian REST API Key. 

1. Add to server config (preferred)

```json
{
  ""mcp-obsidian"": {
    ""command"": ""uvx"",
    ""args"": [
      ""mcp-obsidian""
    ],
    ""env"": {
      ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
      ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
      ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
    }
  }
}
```

2. Create a `.env` file in the working directory with the following required variables:

```
OBSIDIAN_API_KEY=your_api_key_here
OBSIDIAN_HOST=your_obsidian_host
OBSIDIAN_PORT=your_obsidian_port
```

Note:
- You can find the API key in the Obsidian plugin config
- Default port is 27124 if not specified
- Default host is 127.0.0.1 if not specified

## Quickstart

### Install

#### Obsidian REST API

You need the Obsidian REST API community plugin running: https://github.com/coddingtonbear/obsidian-local-rest-api

Install and enable it in the settings and copy the api key.

#### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`

On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<dir_to>/mcp-obsidian"",
        ""run"",
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uvx"",
      ""args"": [
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<YOUR_OBSIDIAN_API_KEY>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

## Development

### Building

To prepare the package for distribution:

1. Sync dependencies and update lockfile:
```bash
uv sync
```

### Debugging

Since MCP servers run over stdio, debugging can be challenging. For the best debugging
experience, we strongly recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector).

You can launch the MCP Inspector via [`npm`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm) with this command:

```bash
npx @modelcontextprotocol/inspector uv --directory /path/to/mcp-obsidian run mcp-obsidian
```

Upon launching, the Inspector will display a URL that you can access in your browser to begin debugging.

You can also watch the server logs with this command:

```bash
tail -n 20 -f ~/https://github.com/MarkusPfundstein/mcp-obsidian,MarkusPfundstein/mcp-obsidian,"# MCP server for Obsidian

MCP server to interact with Obsidian via the Local REST API community plugin.

<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/3wko1bhuek/badge"" alt=""server for Obsidian MCP server"" /></a>

## Components

### Tools

The server implements multiple tools to interact with Obsidian:

- list_files_in_vault: Lists all files and directories in the root directory of your Obsidian vault
- list_files_in_dir: Lists all files and directories in a specific Obsidian directory
- get_file_contents: Return the content of a single file in your vault.
- search: Search for documents matching a specified text query across all files in the vault
- patch_content: Insert content into an existing note relative to a heading, block reference, or frontmatter field.
- append_content: Append content to a new or existing file in the vault.
- delete_file: Delete a file or directory from your vault.

### Example prompts

Its good to first instruct Claude to use Obsidian. Then it will always call the tool.

The use prompts like this:
- Get the contents of the last architecture call note and summarize them
- Search for all files where Azure CosmosDb is mentioned and quickly explain to me the context in which it is mentioned
- Summarize the last meeting notes and put them into a new note 'summary meeting.md'. Add an introduction so that I can send it via email.

## Configuration

### Obsidian REST API Key

There are two ways to configure the environment with the Obsidian REST API Key. 

1. Add to server config (preferred)

```json
{
  ""mcp-obsidian"": {
    ""command"": ""uvx"",
    ""args"": [
      ""mcp-obsidian""
    ],
    ""env"": {
      ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
      ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
      ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
    }
  }
}
```

2. Create a `.env` file in the working directory with the following required variables:

```
OBSIDIAN_API_KEY=your_api_key_here
OBSIDIAN_HOST=your_obsidian_host
OBSIDIAN_PORT=your_obsidian_port
```

Note:
- You can find the API key in the Obsidian plugin config
- Default port is 27124 if not specified
- Default host is 127.0.0.1 if not specified

## Quickstart

### Install

#### Obsidian REST API

You need the Obsidian REST API community plugin running: https://github.com/coddingtonbear/obsidian-local-rest-api

Install and enable it in the settings and copy the api key.

#### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`

On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<dir_to>/mcp-obsidian"",
        ""run"",
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uvx"",
      ""args"": [
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<YOUR_OBSIDIAN_API_KEY>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

## Development

### Building

To prepare the package for distribution:

1. Sync dependencies and update lockfile:
```bash
uv sync
```

### Debugging

Since MCP servers run over stdio, debugging can be challenging. For the best debugging
experience, we strongly recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector).

You can launch the MCP Inspector via [`npm`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm) with this command:

```bash
npx @modelcontextprotocol/inspector uv --directory /path/to/mcp-obsidian run mcp-obsidian
```

Upon launching, the Inspector will display a URL that you can access in your browser to begin debugging.

You can also watch the server logs with this command:

```bash
tail -n 20 -f ~/Library/Logs/Claude/mcp-server-mcp-obsidian.log
```
","Star
 1.2k",2025-06-24T08:43:26.213737,mcp-obsidian - MCP Server | Model Context Protocol Integration,"# MCP server for Obsidian

MCP server to interact with Obsidian via the Local REST API community plugin.

<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><...","['mcp server', 'model context protocol', 'ai integration', 'mcp-obsidian']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-obsidian', 'description': '# MCP server for Obsidian\n\nMCP server to interact with Obsidian via the Local REST API community plugin.\n\n<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><...', 'url': 'https://github.com/MarkusPfundstein/mcp-obsidian', 'codeRepository': 'https://github.com/MarkusPfundstein/mcp-obsidian', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,API Integration,Text Processing,,"Building, Debugging, Install, Tools","{
  ""mcp-obsidian"": {
    ""command"": ""uvx"",
    ""args"": [
      ""mcp-obsidian""
    ],
    ""env"": {
      ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
      ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
      ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
    }
  }
}",,MIT License,2025-06-24,MarkusPfundstein,MCP server to interact with Obsidian via the Local REST API community plugin.
https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git,SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git,,"Star
 8",2025-06-24T08:43:26.213735,Crypto-Trader-MCP-ClaudeDesktop.git - MCP Server | Model Context Protocol Integration,,"['mcp server', 'model context protocol', 'ai integration', 'Crypto-Trader-MCP-ClaudeDesktop.git']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'Crypto-Trader-MCP-ClaudeDesktop.git', 'description': '', 'url': 'https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git', 'codeRepository': 'https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Unknown,Other,,,,,,,,SaintDoresh,
https://github.com/micl2e2/code-to-tree,micl2e2/code-to-tree,"
# Table of Contents

-   [MCP Server: code-to-tree](#orgf542482)
-   [Using code-to-tree](#org862e7dc)
-   [Configure MCP Clients](#orge54fa87)
-   [Building (Windows)](#org48a8180)
-   [Building (macOS)](#orgbaa740e)



<a id=""orgf542482""></a>

# MCP Server: code-to-tree

The code-to-tree server's goals are:

1.  Give LLMs the capability of **accurately** converting source code into
    AST(Abstract Syntax Tree), regardless of language.
2.  One **standalone** binary should be everything the MCP client needs.

These goals imply:

1.  The underlying syntax parser should be **versatile** enough. Here we
    choose [tree-sitter](https://github.com/tree-sitter/tree-sitter), and languages are: C, C++, Rust, Ruby, Go, Java, Python.
2.  The server should be able to carry all capabilities within
    itself, imposing **minimum** software dependencies on the end user's
    machine. Here we choose [mcpc](https://github.com/micl2e2/mcpc).

**Screenshots:**

<img src=""./chathistory.png"" width=""450px"" /><img src=""./wholeast.png"" width=""200px"" />

The above screenshots are obtained by asking the question specified
in `q.md`. 

(**IMPORTANT NOTE**: LLMs have no responsibility of generating the identical
result for the same question,  you will likely get a completely different
style or content. The screenshots or questions provided here are just for the reference)


<a id=""org862e7dc""></a>

# Using code-to-tree

Before everthing, you need to have the code-to-tree executable on your
machine (`code-to-tree.exe` for Windows, `code-to-tree` for macOS),
you can download at GitHub release [page](https://github.com/micl2e2/code-to-tree/releases) or build it yourself. Once
downloaded, you configure your MCP clients to install it, check the section
*""Configure MCP Clients""* for more details.


<a id=""orge54fa87""></a>

# Configure MCP Clients

Here we use Claude as thehttps://github.com/oraios/serena,oraios/serena,"<p align=""center"" style=""text-align:center"">
  <img src=""resources/serena-logo.svg#gh-light-mode-only"" style=""width:500px"">
  <img src=""resources/serena-logo-dark-mode.svg#gh-dark-mode-only"" style=""width:500px"">
</p>

* :rocket: Serena is a powerful **coding agent toolkit** capable of turning an LLM into a fully-featured agent that works **directly on your codebase**.
* :wrench: Serena provides essential **semantic code retrieval and editing tools** that are akin to an IDE's capabilities, extracting code entities at the symbol level and exploiting relational structure.
* :free: Serena is **free & open-source**, enhancing the capabilities of LLMs you already have access to free of charge.

### Demonstration

Here is a demonstration of Serena implementing a small feature for itself (a better log GUI) with Claude Desktop.
Note how Serena's tools enable Claude to find and edit the right symbols.

https://github.com/user-attachments/assets/6eaa9aa1-610d-4723-a2d6-bf1e487ba753

<p align=""center"">
  <em>Serena is under active development! See the latest updates, upcoming features, and lessons learned to stay up to date.</em>
</p>

<p align=""center"">
  <a href=""CHANGELOG.md"">
    <img src=""https://img.shields.io/badge/Updates-1e293b?style=flat&logo=rss&logoColor=white&labelColor=1e293b"" alt=""Changelog"" />
  </a>
  <a href=""roadmap.md"">
    <img src=""https://img.shields.io/badge/Roadmap-14532d?style=flat&logo=target&logoColor=white&labelColor=14532d"" alt=""Roadmap"" />
  </a>
  <a href=""lessons_learned.md"">
    <img src=""https://img.shields.io/badge/Lessons-Learned-7c4700?style=flat&logo=readthedocs&logoColor=white&labelColor=7c4700"" alt=""Lessons Learned"" />
  </a>
</p>



### LLM Integration

Serena provides the necessary [tools](#full-list-of-tools) for coding workflows, but an LLM is required to do the actual work,
orchestrating tool use.

For example, **supercharge the performance of Claude Code** with a [one-line shell command](#claude-code).

Serena can be integrated with an LLM in several ways:
 * by using the **model context protocol (MCP)**.  
   Serena provides an MCP server which integrates with 
     * Claude Code and Claude Desktop, 
     * IDEs like VSCode, Cursor or IntelliJ,
     * Extensions like Cline or Roo Code
     * Goose (for a nice CLI experience)
     * and many others, including [the ChatGPT app soon](https://x.com/OpenAIDevs/status/1904957755829481737)
 * by using **Agno – the model-agnostic agent framework**.  
   Serena's Agno-based agent allows you to turn virtually any LLM into a coding agent, whether it's provided by Google, OpenAI or Anthropic (with a paid API key)
   or a free model provided by Ollama, Together or Anyscale.
 * by incorporating Serena's tools into an agent framework of your choice.  
   Serena's tool implementation is decoupled from the framework-specific code and can thus easily be adapted to any agent framework.

### Programming Language Support & Semantic Analysis Capabilities

Serena's semantic code analysis capabilities build on **language servers** using the widely implemented
language server protocol (LSP). The LSP provides a set of versatile code querying
and editing functionalities based on symbolic understanding of the code. 
Equipped with these capabilities, Serena discovers and edits code just like a seasoned developer 
making use of an IDE's capabilities would.
Serena can efficiently find the right context and do the right thing even in very large and
complex projects! So not only is it free and open-source, it frequently achieves better results 
than existing solutions that charge a premium.

Language servers provide support for a wide range of programming languages.
With Serena, we provide 
 * direct, out-of-the-box support for:
     * Python 
     * TypeScript/Javascript
     * PHP
     * Go (need to install go and gopls first)
     * Rust
     * C/C++
     * Java (_Note_: startup is slow, initial startup especially so. There seem to be issues with java on macos)
 * indirect support (may require some code changes/manual installation) for:
     * Ruby (untested)
     * C# (untested)
     * Kotlin (untested)
     * Dart (untested)
     
   These languages are supported by the language server library, but
   we did not explicitly test whether the support for these languages actually works flawlesshttps://github.com/oraios/serena,oraios/serena,"<p align=""center"" style=""text-align:center"">
  <img src=""resources/serena-logo.svg#gh-light-mode-only"" style=""width:500px"">
  <img src=""resources/serena-logo-dark-mode.svg#gh-dark-mode-only"" style=""width:500px"">
</p>

* :rocket: Serena is a powerful **coding agent toolkit** capable of turning an LLM into a fully-featured agent that works **directly on your codebase**.
* :wrench: Serena provides essential **semantic code retrieval and editing tools** that are akin to an IDE's capabilities, extracting code entities at the symbol level and exploiting relational structure.
* :free: Serena is **free & open-source**, enhancing the capabilities of LLMs you already have access to free of charge.

### Demonstration

Here is a demonstration of Serena implementing a small feature for itself (a better log GUI) with Claude Desktop.
Note how Serena's tools enable Claude to find and edit the right symbols.

https://github.com/user-attachments/assets/6eaa9aa1-610d-4723-a2d6-bf1e487ba753

<p align=""center"">
  <em>Serena is under active development! See the latest updates, upcoming features, and lessons learned to stay up to date.</em>
</p>

<p align=""center"">
  <a href=""CHANGELOG.md"">
    <img src=""https://img.shields.io/badge/Updates-1e293b?style=flat&logo=rss&logoColor=white&labelColor=1e293b"" alt=""Changelog"" />
  </a>
  <a href=""roadmap.md"">
    <img src=""https://img.shields.io/badge/Roadmap-14532d?style=flat&logo=target&logoColor=white&labelColor=14532d"" alt=""Roadmap"" />
  </a>
  <a href=""lessons_learned.md"">
    <img src=""https://img.shields.io/badge/Lessons-Learned-7c4700?style=flat&logo=readthedocs&logoColor=white&labelColor=7c4700"" alt=""Lessons Learned"" />
  </a>
</p>



### LLM Integration

Serena provides the necessary [tools](#full-list-of-tools) for coding workflows, but an LLM is required to do the actual work,
orchestrating tool use.

For example, **supercharge the performance of Claude Code** with a [one-line shell command](#claude-code).

Serena can be integrated with an LLM in several ways:
 * by using the **model context protocol (MCP)**.  
   Serena provides an MCP server which integrates with 
     * Claude Code and Claude Desktop, 
     * IDEs like VSCode, Cursor or IntelliJ,
     * Extensions like Cline or Roo Code
     * Goose (for a nice CLI experience)
     * and many others, including [the ChatGPT app soon](https://x.com/OpenAIDevs/status/1904957755829481737)
 * by using **Agno – the model-agnostic agent framework**.  
   Serena's Agno-based agent allows you to turn virtually any LLM into a coding agent, whether it's provided by Google, OpenAI or Anthropic (with a paid API key)
   or a free model provided by Ollama, Together or Anyscale.
 * by incorporating Serena's tools into an agent framework of your choice.  
   Serena's tool implementation is decoupled from the framework-specific code and can thus easily be adapted to any agent framework.

### Programming Language Support & Semantic Analysis Capabilities

Serena's semantic code analysis capabilities build on **language servers** using the widely implemented
language server protocol (LSP). The LSP provides a set of versatile code querying
and editing functionalities based on symbolic understanding of the code. 
Equipped with these capabilities, Serena discovers and edits code just like a seasoned developer 
making use of an IDE's capabilities would.
Serena can efficiently find the right context and do the right thing even in very large and
complex projects! So not only is it free and open-source, it frequently achieves better results 
than existing solutions that charge a premium.

Language servers provide support for a wide range of programming languages.
With Serena, we provide 
 * direct, out-of-the-box support for:
     * Python 
     * TypeScript/Javascript
     * PHP
     * Go (need to install go and gopls first)
     * Rust
     * C/C++
     * Java (_Note_: startup is slow, initial startup especially so. There seem to be issues with java on macos)
 * indirect support (may require some code changes/manual installation) for:
     * Ruby (untested)
     * C# (untested)
     * Kotlin (untested)
     * Dart (untested)
     
   These languages are supported by the language server library, but
   we did not explicitly test whether the support for these languages actually works flawlessly.
       
Further languages can, in principle, easily be supported by providing a shallow adapter for a new language server
implementation.


## Table of Contents

<!-- Created with markdown-toc -i README.md -->
<!-- Install it with npm install -g markdown-toc -->

<!-- toc -->

- [What Can I Use Serena For?](#what-can-i-use-serena-for)
- [Free Coding Agents with Serena](#free-coding-agents-with-serena)
- [Quick Start](#quick-start)
  * [Running the Serena MCP Server](#running-the-serena-mcp-server)
    + [Usage](#usage)
        * [Local Installation](#local-installation)
      - [Using uvx](#using-uvx)
      - [Using Docker (Experimental)](#using-docker-experimental)
    + [SSE Mode](#sse-mode)
    + [Command-Line Arguments](#command-line-arguments)
  * [Configuration](#configuration)
  * [Project Activation & Indexing](#project-activation--indexing)
  * [Claude Code](#claude-code)
  * [Claude Desktop](#claude-desktop)
  * [Other MCP Clients (Cline, Roo-Code, Cursor, Windsurf, etc.)](#other-mcp-clients-cline-roo-code-cursor-windsurf-etc)
  * [Agno Agent](#agno-agent)
  * [Other Agent Frameworks](#other-agent-frameworks)
- [Detailed Usage and Recommendations](#detailed-usage-and-recommendations)
  * [Tool Execution](#tool-execution)
    + [Shell Execution and Editing Tools](#shell-execution-and-editing-tools)
  * [Modes and Contexts](#modes-and-contexts)
    + [Contexts](#contexts)
    + [Modes](#modes)
    + [Customization](#customization)
  * [Onboarding and Memories](#onboarding-and-memories)
  * [Prepare Your Project](#prepare-your-project)
    + [Structure Your Codebase](#structure-your-codebase)
    + [Start from a Clean State](#start-from-a-clean-state)
    + [Logging, Linting, and Automated Tests](#logging-linting-and-automated-tests)
  * [Prompting Strategies](#prompting-strategies)
  * [Potential Issues in Code Editing](#potential-issues-in-code-editing)
  * [Running Out of Context](#running-out-of-context)
  * [Combining Serena with Other MCP Servers](#combining-serena-with-other-mcp-servers)
  * [Serena's Logs: The Dashboard and GUI Tool](#serenas-logs-the-dashboard-and-gui-tool)
  * [Troubleshooting](#troubleshooting)
- [Comparison with Other Coding Agents](#comparison-with-other-coding-agents)
  * [Subscription-Based Coding Agents](#subscription-based-coding-agents)
  * [API-Based Coding Agents](#api-based-coding-agents)
  * [Other MCP-Based Coding Agents](#other-mcp-based-coding-agents)
- [Acknowledgements](#acknowledgements)
- [Customizing and Extending Serena](#customizing-and-extending-serena)
- [Full List of Tools](#full-list-of-tools)

<!-- tocstop -->

## What Can I Use Serena For?

You can use Serena for any coding tasks – whether it is focussed on analysis, planning, 
designing new components or refactoring existing ones.
Since Serena's tools allow an LLM to close the cognitive perception-action loop, 
agents based on Serena can autonomously carry out coding tasks from start to finish – 
from the initial analysis to the implementation, testing and, finally, the version
control system commit.

Serena can read, write and execute code, read logs and the terminal output.
While we do not necessarily encourage it, ""vibe coding"" is certainly possible, and if you 
want to almost feel like ""the code no longer exists"",
you may find Serena even more adequate for vibing than an agent inside an IDE
(since you will have a separate GUI that really lets you forget).

## Free Coding Agents with Serena

Even the free tier of Anthropic's Claude has support for MCP Servers, so you can use Serena with Claude for free.
Presumably, the same will soon be possible with ChatGPT Desktop once support for MCP servers is added.  
Through Agno, you furthermore have the option to use Serena with a free/open-weights model.

Serena is [Oraios AI](https://oraios-ai.de/)'s contribution to the developer community.  
We use it ourselves on a regular basis.

We got tired of having to pay multiple
IDE-based subscriptions (such as Windsurf or Cursor) that forced us to keep purchasing tokens on top of the chat subscription costs we already had.
The substantial API costs incurred by tools like Claude Code, Cline, Aider and other API-based tools are similarly unattractive.
We thus built Serena with the prospect of being able to cancel most other subscriptions.

## Quick Start

Serena can be used in various ways, below you will find instructions for selected integrations.

- If you just want to turn Claude into a free-to-use coding agent, we recommend using Serena through [Claude Code](#claude-code) or [Claude Desktop](#claude-desktop).
- If you want to use Gemini or any other model, and you want a GUI experience, you can use [Agno](#agno-agent). On macOS, you can also use the GUI of [goose](#goose).
- If you want to use Serena integrated in your IDE, see the section on [other MCP clients](#other-mcp-clients---cline-roo-code-cursor-windsurf-etc).

Serena is managed by `uv`, so you will need to [install it](https://docs.astral.sh/uv/getting-started/installation/)).

### Running the Serena MCP Server

You have several options for running the MCP server, which are explained in the subsections below.

#### Usage

The typical usage involves the client (Claude Code, Claude Desktop, etc.) running
the MCP server as a subprocess (using stdio communication), 
so the client needs to be provided with the command to run the MCP server.
(Alternatively, you can run the MCP server in SSE mode and tell your client 
how to connect to it.)

Note that no matter how you run the MCP server, Serena will, by default, start a small web-based dashboard on localhost that will display logs and allow shutting down the
MCP server (since many clients fail to clean up processes correctly).
This and other settings can be adjusted in the [configuration](#configuration) and/or by providing [command-line arguments](#command-line-arguments).

###### Local Installation

1. Clone the repository and change into it.
   ```shell
   git clone https://github.com/oraios/serena
   cd serena
   ```
2. Optionally create a config file from the template and adjust it according to your preferences.
   ```shell
   cp src/serena/resources/serena_config.template.yml serena_config.yml
   ```
   If you just want the default config, you can skip this part, and a config file will be created when you first run Serena.
3. Run the server with `uv`:
   ```shell
   uv run serena-mcp-server
   ```
   When running from outside the serena installation directory, be sure to pass it, i.e. use
   ```shell
    uv run --directory /abs/path/to/serena serena-mcp-server
    ```

##### Using uvx

`uvx` can be used to run the latest version of Serena directly from the repository, without an explicit local installation.

* Windows:
  ```shell
  uvx --from git+https://github.com/oraios/serena serena-mcp-server.exe
  ```
* Other operating systems:
  ```shell
  uvx --from git+https://github.com/oraios/serena serena-mcp-server
  ```

##### Using Docker (Experimental)

⚠️ Docker support is currently experimental with several limitations. Please read the [Docker documentation](DOCKER.md) for important caveats before using it.

You can run the Serena MCP server directly via docker as follows,
assuming that the projects you want to work on are all located in `/path/to/your/projects`:

```shell
docker run --rm -i --network host -v /path/to/your/projects:/workspaces/projects ghcr.io/oraios/serena:latest serena-mcp-server --transport stdio
```

Replace `/path/to/your/projects` with the absolute path to your projects directory. The Docker approach provides:
- Better security isolation for shell command execution
- No need to install language servers and dependencies locally
- Consistent environment across different systems

See the [Docker documentation](DOCKER.md) for detailed setup instructions, configuration options, and known limitations.

#### SSE Mode

ℹ️ Note that MCP servers which use stdio as a protocol are somewhat unusual as far as client/server architectures go, as the server
necessarily has to be started by the client in order for communication to take place via the server's standard input/output stream.
In other words, you do not need to start the server yourself. The client application (e.g. Claude Desktop) takes care of this and
therefore needs to be configured with a launch command. 

When using instead the SSE mode, which uses HTTP-based communication, you control the server lifecycle yourself,
i.e. you start the server and provide the client with the URL to connect to it.

Simply provide `serena-mcp-server` with the `--transport sse` option and optionally provide the port.
For example, to run the Serena MCP server in SSE mode on port 9121 using a local installation,
you would run this command from the Serena directory, 

```shell
uv run serena-mcp-server --transport sse --port 9121
```

and then configure your client to connect to `http://localhost:9121`.


#### Command-Line Arguments

The Serena MCP server supports a wide range of additional command-line options, including the option to run in SSE mode
and to adapt Serena to various [contexts and modes of operation](#modes-and-contexts).

Run with parameter `--help` to get a list of available options.


### Configuration

Serena's behavior (active tools and prompts as well as logging configuration, etc.) is configured in four places:

1. The `serena_config.yml` for general settings that apply to all clients and projects
2. In the arguments passed to the `serena-mcp-server` in your client's config (see below), 
   which will apply to all sessions started by the respective client. In particular, the [context](#contexts) parameter
   should be set appropriately for Serena to be best adjusted to existing tools and capabilities of your client.
   See for a detailed explanation. You can override all entries from the `serena_config.yml` through command line arguments.
3. In the `.serena/project.yml` file within your project. This will hold project-level configuration that is used whenever
   that project is activated.
4. Through the currently active set of [modes](#modes).


> ⚠️ **Note:** Serena is under active development. We are continuously adding features, improving stability and the UX.
> As a result, configuration may change in a breaking manner. If you have an invalid configuration,
> the MCP server or Serena-based Agent may fail to start (investigate the MCP logs in the former case).
> Check the [changelog](CHANGELOG.md)
> and the configuration templates when updating Serena, adapting your configurations accordingly.

After the initial setup, continue with one of the sections below, depending on how you
want to use Serena.

You can just ask the LLM to show you the config of your session, Serena has a tool for it.

### Project Activation & Indexing

The recommended way is to just ask the LLM to activate a project by providing it an absolute path to, or,
in case the project was activated in the past, by its name. The default project name is the directory name.

  * ""Activate the project /path/to/my_project""
  * ""Activate the project my_project""

All projects that have been activated will be automatically added to your `serena_config.yml`, and for each 
project, the file `.serena/project.yml` will be generated. You can adjust the latter, e.g., by changing the name
(which you refer to during the activation) or other options. Make sure to not have two different projects with the
same name.

If you are mostly working with the same project, you can also configure to always activate a project at startup
by passing `--project <path_or_name>` to the `serena-mcp-server` command in your client's MCP config.

ℹ️ For larger projects, we recommend that you index your project to accelerate Serena's tools; otherwise the first
tool application may be very slow.
To do so, run one of these commands the project directory or pass the path to the project as an argument:

* When using a local installation:
  ```shell
  uv run --directory /abs/path/to/serena index-project
  ```
* When using uvx:
  ```shell
  uvx --from git+https://github.com/oraios/serena index-project
  ```

### Claude Code

Serena is a great way to make Claude Code both cheaper and more powerful! 

From your project directory, add serena with a command like this,

```shell
claude mcp add serena -- <serena-mcp-server> --context ide-assistant --project $(pwd)
```

where `<serena-mcp-server>` is your way of [running the Serena MCP server](#running-the-serena-mcp-server).
For example, when using `uvx`, you would run
```shell
claude mcp add serena -- uvx --from git+https://github.com/oraios/serena serena-mcp-server --context ide-assistant --project $(pwd)
```

ℹ️ Once in Claude Code, you should ask Claude to ""Read the initial instructions"" as your first prompt, such that it will receive information
on how to use Serena's tools.


### Claude Desktop

For [Claude Desktop](https://claude.ai/download) (available for Windows and macOS), go to File / Settings / Developer / MCP Servers / Edit Config,
which will let you open the JSON file `claude_desktop_config.json`. 
Add the `serena` MCP server configuration, using a [run command](#running-the-serena-mcp-server) depending on your setup.

* local installation:
   ```json
   {
       ""mcpServers"": {
           ""serena"": {
               ""command"": ""/abs/path/to/uv"",
               ""args"": [""run"", ""--directory"", ""/abs/path/to/serena"", ""serena-mcp-server""]
           }
       }
   }
   ```
* uvx:
   ```json
   {
       ""mcpServers"": {
           ""serena"": {
               ""command"": ""/abs/path/to/uvx"",
               ""args"": [""--from"", ""git+https://github.com/oraios/serena"", ""serena-mcp-server""]
           }
       }
  }
  ```
* docker:
  ```json
   {
       ""mcpServers"": {
           ""serena"": {
               ""command"": ""docker"",
               ""args"": [""run"", ""--rm"", ""-i"", ""--network"", ""host"", ""-v"", ""/path/to/your/projects:/workspaces/projects"", ""ghcr.io/oraios/serena:latest"", ""serena-mcp-server"", ""--transport"", ""stdio""]
           }
       }
   }
   ```

If you are using paths containing backslashes for paths on Windows
(note that you can also just use forward slashes), be sure to escape them correctly (`\\`).

That's it! Save the config and then restart Claude Desktop. You are ready for activating your first project.

ℹ️ You can further customize the run command using additional arguments (see [above](#command-line-arguments)).

Note: on Windows and macOS there are official Claude Desktop applications by Anthropic, for Linux there is an [open-source
community version](https://github.com/aaddrick/claude-desktop-debian).

⚠️ Be sure to fully quit the Claude Desktop application, as closing Claude will just minimize it to the system tray – at least on Windows.  

⚠️ Some clients, currently including Claude Desktop, may leave behind zombie processes. You will have to find and terminate them manually then.
    With Serena, you can activate the [dashboard](#serenas-logs-the-dashboard-and-gui-tool) to prevent unnoted processes and also use the dashboard
    for shutting down Serena.

After restarting, you should see Serena's tools in your chat interface (notice the small hammer icon).

For more information on MCP servers with Claude Desktop, see [the official quick start guide](https://modelcontextprotocol.io/quickstart/user).

### Other MCP Clients (Cline, Roo-Code, Cursor, Windsurf, etc.)

Being an MCP Server, Serena can be included in any MCP Client. The same configuration as above,
perhaps with small client-specific modifications, should work. Most of the popular
existing coding assistants (IDE extensions or VSCode-like IDEs) support connections
to MCP Servers. It is **recommended to use the `ide-assistant` context** for these integrations by adding `""--context"", ""ide-assistant""` to the `args` in your MCP client's configuration. Including Serena generally boosts their performance
by providing them tools for symbolic operations.

In this case, the billing for the usage continues to be controlled by the client of your choice
(unlike with the Claude Desktop client). But you may still want to use Serena through such an approach,
e.g., for one of the following reasons:

1. You are already using a coding assistant (say Cline or Cursor) and just want to make it more powerful.
2. You are on Linux and don't want to use the [community-created Claude Desktop](https://github.com/aaddrick/claude-desktop-debian).
3. You want tighter integration of Serena into your IDE and don't mind paying for that.

### Agno Agent

Agno is a model-agnostic agent framework that allows you to turn Serena into an agent 
(independent of the MCP technology) with a large number of underlying LLMs. Agno is currently
the simplest way of running Serena in a chat GUI with an LLM of your choice 
(unless you are using a Mac, then you might prefer goose, which requires almost no setup).

While Agno is not yet entirely stable, we chose it, because it comes with its own open-source UI, 
making it easy to directly use the agent using a chat interface.  With Agno, Serena is turned into an agent
(so no longer an MCP Server), so it can be used in programmatic ways (for example for benchmarking or within 
your application).

Here's how it works (see also [Agno's documentation](https://docs.agno.com/introduction/playground)):

1. Download the agent-ui code with npx
   ```shell
   npx create-agent-ui@latest
   ```
   or, alternatively, clone it manually:
   ```shell
   git clone https://github.com/agno-agi/agent-ui.git
   cd agent-ui 
   pnpm install 
   pnpm dev
   ```

2. Install serena with the optional requirements:
   ```shell
   # You can also only select agno,google or agno,anthropic instead of all-extras
   uv pip install --all-extras -r pyproject.toml -e .
   ```
   
3. Copy `.env.example` to `.env` and fill in the API keys for the provider(s) you
   intend to use.

4. Start the agno agent app with
   ```shell
   uv run python scripts/agno_agent.py
   ```
   By default, the script uses Claude as the model, but you can choose any model
   supported by Agno (which is essentially any existing model).

5. In a new terminal, start the agno UI with
   ```shell
   cd agent-ui 
   pnpm dev
   ```
   Connect the UI to the agent you started above and start chatting. You will have
   the same tools as in the MCP server version.


Here is a short demo of Serena performing a small analysis task with the newest Gemini model:

https://github.com/user-attachments/assets/ccfcb968-277d-4ca9-af7f-b84578858c62


⚠️ IMPORTANT: In contrast to the MCP server approach, tool execution in the Agno UI does
not ask for the user's permission. The shell tool is particularly critical, as it can perform arbitrary code execution. 
While we have never encountered any issues with
this in our testing with Claude, allowing this may not be entirely safe. 
You may choose to disable certain tools for your setup in your Serena project's
configuration file (`.yml`).

### Other Agent Frameworks

It should be straightforward to incorporate Serena into any
agent framework (like [pydantic-ai](https://ai.pydantic.dev/), [langgraph](https://langchain-ai.github.io/langgraph/tutorials/introduction/) or others).
Typically, you need only to write an adapter for Serena's tools to the tool representation in the framework of your choice, 
as was done by us for Agno with [SerenaAgnoToolkit](/src/serena/agno.py).


## Detailed Usage and Recommendations

### Tool Execution

Serena combines tools for semantic code retrieval with editing capabilities and shell execution.
Serena's behavior can be further customized through [Modes and Contexts](#modes-and-contexts).
Find the complete list of tools [below](#full-list-of-tools).

The use of all tools is generally recommended, as this allows Serena to provide the most value:
Only by executing shell commands (in particular, tests) can Serena identify and correct mistakes
autonomously.

#### Shell Execution and Editing Tools

However, it should be noted that the `execute_shell_command` tool allows for arbitrary code execution.
When using Serena as an MCP Server, clients will typically ask the user for permission
before executing a tool, so as long as the user inspects execution parameters beforehand,
this should not be a problem.
However, if you have concerns, you can choose to disable certain commands in your project's
.yml configuration file.
If you only want to use Serena purely for analyzing code and suggesting implementations
without modifying the codebase, you can enable read-only mode by setting `read_only: true` in your project configuration file.
This will automatically disable all editing tools and prevent any modifications to your codebase while still
allowing all analysis and exploration capabilities.

In general, be sure to back up your work and use a version control system in order to avoid
losing any work.


### Modes and Contexts

Serena's behavior and toolset can be adjusted using contexts and modes. 
These allow for a high degree of customization to best suit your workflow and the environment Serena is operating in.

#### Contexts

A context defines the general environment in which Serena is operating. 
It influences the initial system prompt and the set of available tools. 
A context is set at startup when launching Serena (e.g., via CLI options for an MCP server or in the agent script) and cannot be changed during an active session.

Serena comes with pre-defined contexts:
*   `desktop-app`: Tailored for use with desktop applications like Claude Desktop. This is the default.
*   `agent`: Designed for scenarios where Serena acts as a more autonomous agent, for example, when used with Agno.
*   `ide-assistant`: Optimized for integration into IDEs like VSCode, Cursor, or Cline, focusing on in-editor coding assistance.
Choose the context that best matches the type of integration you are using.

When launching Serena, specify the context using `--context <context-name>`.  
Note that for cases where parameter lists are specified (e.g. Claude Desktop), you must add two parameters to the list.

#### Modes

Modes further refine Serena's behavior for specific types of tasks or interaction styles. Multiple modes can be active simultaneously, allowing you to combine their effects. Modes influence the system prompt and can also alter the set of available tools by excluding certain ones.

Examples of built-in modes include:
*   `planning`: Focuses Serena on planning and analysis tasks.
*   `editing`: Optimizes Serena for direct code modification tasks.
*   `interactive`: Suitable for a conversational, back-and-forth interaction style.
*   `one-shot`: Configures Serena for tasks that should be completed in a single response, often used with `planning` for generating reports or initial plans.
*   `no-onboarding`: Skips the initial onboarding process if it's not needed for a particular session.
*   `onboarding`: (Usually triggered automatically) Focuses on the project onboarding process.

Modes can be set at startup (similar to contexts) but can also be *switched dynamically* during a session. You can instruct the LLM to use the `switch_modes` tool to activate a different set of modes (e.g., ""switch to planning and one-shot modes"").

When launching Serena, specify modes using `--mode <mode-name>`; multiple modes can be specified, e.g. `--mode planning --mode no-onboarding`.

:warning: **Mode Compatibility**: While you can combine modes, some may be semantically incompatible (e.g., `interactive` and `one-shot`). Serena currently does not prevent incompatible combinations; it is up to the user to choose sensible mode configurations.

#### Customization

You can create your own contexts and modes to precisely tailor Serena to your needs in two ways:
*  **Adding to Serena's configuration directory**: Create new `.yml` files in the `config/contexts/` or `config/modes/` directories within your local Serena repository. These custom contexts/modes will be automatically registered and available for use by their name (filename without the `.yml` extension). They will also appear in listings of available contexts/modes.
*  **Using external YAML files**: When starting Serena, you can provide an absolute path to a custom `.yml` file for a context or mode.

A context or mode YAML file typically defines:
*   `name`: (Optional if filename is used) The name of the context/mode.
*   `prompt`: A string that will be incorporated into Serena's system prompt.
*   `description`: (Optional) A brief description.
*   `excluded_tools`: A list of tool names (strings) to disable when this context/mode is active.

This customization allows for deep integration and adaptation of Serena to specific project requirements or personal preferences.


### Onboarding and Memories

By default, Serena will perform an **onboarding process** when
it is started for the first time for a project.
The goal of the onboarding is for Serena to get familiar with the project
and to store memories, which it can then draw upon in future interactions.
If an LLM should fail to complete the onboarding and does not actually write the
respective memories to disk, you may need to ask it to do so explicitly.

The onboarding will usually read a lot of content from the project, thus filling
up the context. It can therefore be advisable to switch to another conversation
once the onboarding is complete.
After the onboarding, we recommend that you have a quick look at the memories and,
if necessary, edit them or add additional ones.

**Memories** are files stored in `.serena/memories/` in the project directory,
which the agent can choose to read in subsequent interactions.
Feel free to read and adjust them as needed; you can also add new ones manually.
Every file in the `.serena/memories/` directory is a memory file.
Whenever Serena starts working on a project, the list of memories is
provided, and the agent can decide to read them.
We found that memories can significantly improve the user experience with Serena.


### Prepare Your Project

#### Structure Your Codebase

Serena uses the code structure for finding, reading and editing code. This means that it will
work well with well-structured code but may perform poorly on fully unstructured one (like a ""God class""
with enormous, non-modular functions).  
Furthermore, for languages that are not statically typed, type annotations are highly beneficial.

#### Start from a Clean State

It is best to start a code generation task from a clean git state. Not only will
this make it easier for you to inspect the changes, but also the model itself will
have a chance of seeing what it has changed by calling `git diff` and thereby
correct itself or continue working in a followup conversation if needed.

:warning: **Important**: since Serena will write to files using the system-native line endings
and it might want to look at the git diff, it is important to
set `git config core.autocrlf` to `true` on Windows.
With `git config core.autocrlf` set to `false` on Windows, you may end up with huge diffs
only due to line endings. It is generally a good idea to globally enable this git setting on Windows:

```shell
git config --global core.autocrlf true
```

#### Logging, Linting, and Automated Tests

Serena can successfully complete tasks in an _agent loop_, where it iteratively
acquires information, performs actions, and reflects on the results.
However, Serena cannot use a debugger; it must rely on the results of program executions,
linting results, and test results to assess the correctness of its actions.
Therefore, software that is designed to meaningful interpretable outputs (e.g. log messages)
and that has a good test coverage is much easier to work with for Serena.

We generally recommend to start an editing task from a state where all linting checks and tests pass.

### Prompting Strategies

We found that it is often a good idea to spend some time conceptualizing and planning a task
before actually implementing it, especially for non-trivial task. This helps both in achieving
better results and in increasing the feeling of control and staying in the loop. You can
make a detailed plan in one session, where Serena may read a lot of your code to build up the context,
and then continue with the implementation in another (potentially after creating suitable memories).

### Potential Issues in Code Editing

In our experience, LLMs are bad at counting, i.e. they have problems
inserting blocks of code in the right place. Most editing operations can be performed
at the symbolic level, allowing this problem is overcome. However, sometimes,
line-level insertions are useful.

Serena is instructed to double-check the line numbers and any code blocks that it will
edit, but you may find it useful to explicitly tell it how to edit code if you run into
problems.  
We are working on making Serena's editing capabilities more robust.

### Running Out of Context

For long and complicated tasks, or tasks where Serena has read a lot of content, you
may come close to the limits of context tokens. In that case, it is often a good idea to continue
in a new conversation. Serena has a dedicated tool to create a summary of the current state
of the progress and all relevant info for continuing it. You can request to create this summary and
write it to a memory. Then, in a new conversation, you can just ask Serena to read the memory and
continue with the task. In our experience, this worked really well. On the up-side, since in a 
single session there is no summarization involved, Serena does not usually get lost (unlike some
other agents that summarize under the hood), and it is also instructed to occasionally check whether
it's on the right track.

Moreover, Serena is instructed to be frugal with context 
(e.g., to not read bodies of code symbols unnecessarily),
but we found that Claude is not always very good in being frugal (Gemini seemed better at it).
You can explicitly instruct it to not read the bodies if you know that it's not needed.

### Combining Serena with Other MCP Servers

When using Serena through an MCP Client, you can use it together with other MCP servers.
However, beware of tool name collisions! See info on that above.

Currently, there is a collision with the popular Filesystem MCP Server. Since Serena also provides
filesystem operations, there is likely no need to ever enable these two simultaneously.

### Serena's Logs: The Dashboard and GUI Tool

Serena provides two convenient ways of accessing the logs of the current session:

  * via the **web-based dashboard** (enabled by default)
    
    This is supported on all platforms.
    By default, it will be accessible at `http://localhost:24282/dashboard/index.html`, 
    but a higher port may be used if the default port is unavailable/multiple instances are running.
    
  * via the **GUI tool** (disabled by default)

    This is mainly supported on Windows, but it may also work on Linux; macOS is unsupported.

Both can be enabled or disabled in Serena's configuration file (`serena_config.yml`, see above).
If enabled, they will automatically be opened as soon as the Serena agent/MCP server is started.

In addition to viewing logs, both tools allow to shut down the Serena agent.
This function is provided, because clients like Claude Desktop may fail to terminate the MCP server subprocess 
when they themselves are closed.

### Troubleshooting

Support for MCP Servers in Claude Desktop and the various MCP Server SDKs are relatively new developments and may display instabilities.

The working configuration of an MCP server may vary from platform to
platform and from client to client. We recommend always using absolute paths, as relative paths may be sources of
errors. The language server is running in a separate sub-process and is called with asyncio – sometimes
a client may make it crash. If you have Serena's log window enabled, and it disappears, you'll know what happened.

Some clients (like goose) may not properly terminate MCP servers,
look out for hanging python processes and terminate them
manually, if needed.

## Comparison with Other Coding Agents

To our knowledge, Serena is the first fully-featured coding agent where the
entire functionality
is available through an MCP server, thus not requiring API keys or
subscriptions.

### Subscription-Based Coding Agents

The most prominent subscription-based coding agents are parts of IDEs like
Windsurf, Cursor and VSCode.
Serena's functionality is similar to Cursor's Agent, Windsurf's Cascade or
VSCode's
upcoming [agent mode](https://code.visualstudio.com/blogs/2025/02/24/introducing-copilot-agent-mode).

Serena has the advantage of not requiring a subscription.
A potential disadvantage is that it
is not directly integrated into an IDE, so the inspection of newly written code
is not as seamless.

More technical differences are:
* Serena is not bound to a specific IDE.
  Serena's MCP server can be used with any MCP client (including some IDEs),
  and the Agno-based agent provides additional ways of applying its functionality.
* Serena is not bound to a specific large language model or API.
* Serena navigates and edits code using a language server, so it has a symbolic
  understanding of the code.
  IDE-based tools often use a RAG-based or purely text-based approach, which is often
  less powerful, especially for large codebases.
* Serena is open-source and has a small codebase, so it can be easily extended
  and modified.

### API-Based Coding Agents

An alternative to subscription-based agents are API-based agents like Claude
Code, Cline, Aider, Roo Code and others, where the usage costs map directly
to the API costs of the underlying LLM.
Some of them (like Cline) can even be included in IDEs as an extension.
They are often very powerful and their main downside are the (potentially very
high) API costs.

Serena itself can be used as an API-based agent (see the section on Agno above).
We have not yet written a CLI tool or a
dedicated IDE extension for Serena (and there is probably no need for the latter, as
Serena can already be used with any IDE that supports MCP servers).
If there is demand for a Serena as a CLI tool like Claude Code, we will
consider writing one.

The main difference between Serena and other API-based agents is that Serena can
also be used as an MCP server, thus not requiring
an API key and bypassing the API costs. This is a unique feature of Serena.

### Other MCP-Based Coding Agents

There are other MCP servers designed for coding, like [DesktopCommander](https://github.com/wonderwhy-er/DesktopCommanderMCP) and
[codemcp](https://github.com/ezyang/codemcp).
However, to the best of our knowledge, none of them provide semantic code
retrieval and editing tools; they rely purely on text-based analysis.
It is the integration of language servers and the MCP that makes Serena unique
and so powerful for challenging coding tasks, especially in the context of
larger codebases.


## Acknowledgements

We built Serena on top of multiple existing open-source technologies, the most important ones being:

1. [multilspy](https://github.com/microsoft/multilspy).
   A library which wraps language server implementations and adapts them for interaction via Python
   and which provided the basis for our library Solid-LSP (src/solidlsp). 
   Solid-LSP provides pure synchronous LSP calls and extends the original library with the symbolic logic 
   that Serena required.
2. [Python MCP SDK](https://github.com/modelcontextprotocol/python-sdk)
3. [Agno](https://github.com/agno-agi/agno) and
   the associated [agent-ui](https://github.com/agno-agi/agent-ui),
   which we use to allow Serena to work with any model, beyond the ones
   supporting the MCP.
4. All the language servers that we use through Solid-LSP.

Without these projects, Serena would not have been possible (or would have been significantly more difficult to build).


## Customizing and Extending Serena

It is straightforward to extend Serena's AI functionality with your own ideas. 
Simply implement a new tool by subclassing 
`serena.agent.Tool` and implement the `apply` method with a signature
that matches the tool's requirements. 
Once implemented, `SerenaAgent` will automatically have access to the new tool.

It is also relatively straightforward to add [support for a new programming language](/CONTRIBUTING.md#adding-a-new-supported-language). 

We look forward to seeing what the community will come up with! 
For details on contributing, see [here](/CONTRIBUTING.md).

## Full List of Tools

Here is the full list of Serena's tools with a short description (output of `uv run serena-list-tools`):

 * `activate_project`: Activates a project by name.
 * `check_onboarding_performed`: Checks whether project onboarding was already performed.
 * `create_text_file`: Creates/overwrites a file in the project directory.
 * `delete_lines`: Deletes a range of lines within a file.
 * `delete_memory`: Deletes a memory from Serena's project-specific memory store.
 * `execute_shell_command`: Executes a shell command.
 * `find_referehttps://github.com/horw/esp-mcp,horw/esp-mcp,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)

### Goal
The goal of this MCP is to:
- Consolidate ESP-IDF and related project commands in one place.
- Simplify getting started using only LLM communication.

### How to contribute to the project

Simply find a command that is missing from this MCP and create a PR for it!

If you want someone to help you with this implementation, just open an issue.


### Notice
This project is currently a **Proof of Concept (PoC)** for an MCP server tailored for ESP-IDF workflows. 

**Current Capabilities:**
*   Supports basic ESP-IDF project build commands.
*   Flash built firmware to connected ESP devices with optional port specification.
*   Includes experimental support for automatic issue fixing based on build logs.

**Vision & Future Work:**
The long-term vision is to expand this MCP into a comprehensive toolkit for interacting with embedded devices, potentially integrating with home assistant platforms, and streamlining documentation access for ESP-IDF and related technologies. 

We envision features such as:
*   Broader ESP-IDF command support (e.g., `monitor`, `menuconfig` interaction if feasible).
*   Device management and information retrieval.
*   Integration with other embedded development tools and platforms.

Your ideas and contributions are welcome! Please feel free to discuss them by opening an issue.


### Install  

First, clone this MCP repository:  

```bash
<NAME_EMAIL>:horw/esp-mcp.git
```  

Then, configure it in your chatbot. 

The JSON snippet below is an example of how you might configure this `esp-mcp` server within a chatbot or an agent system that supports the Model Context Protocol (MCP). The exact configuration steps and format may vary depending on the specific chatbot system you are using. Refer to your chatbot's documentation for details on how to integrate MCP servers.

```json
{
    ""mcpServers"": {
        ""esp-run"": { // ""esp-run"" is an arbitrary name you can assign to this server configuration.
            ""command"": ""<path_to_uv_or_python_executable>"",
            ""args"": [
                ""--directory"",
                ""<path_to_cloned_esp-mcp_repository>"", // e.g., /path/to/your/cloned/esp-mcp
                ""run"",
                ""main.py"" // If using python directly, this might be just ""main.py"" and `command` would be your python interpreter
            ],
            ""env"": {
                ""IDF_PATH"": ""<path_to_your_esp-idf_directory>"" // e.g., ~/esp/esp-idf or C:\\Espressif\\frameworks\\esp-idf
            }
        }
    }
}
```

A few notes on the configuration:

*   **`command`**: This should be the full path to your `uv` executable if you are using it, or your Python interpreter (e.g., `/usr/bin/python3` or `C:\\Python39\\python.exe`) if you plan to run `main.py` directly.
*   **`args`**:
    *   The first argument to `--directory` should be the absolute path to where you cloned the `esp-mcp` repository.
    *   If you're using `uv`, the arguments `run main.py` are appropriate. If you're using Python directly, you might only need `main.py` in the `args` list, and ensure your `command` points to the Python executable.
*   **`IDF_PATH`**: This environment variable must point to the root directory of your ESP-IDF installation. ESP-IDF is Espressif's official IoT Development Framework. If you haven't installed it, please refer to the [official ESP-IDF documentation](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/index.html) for installation instructions.

### Usage

Once the `esp-mcp` server is configured and running, your LLM or chatbot can interact with it using the tools defined in this MCP. For example, you could ask your chatbot to:

*   ""Build the project located at `/path/to/my/esp-project` using the `esp-mcp`.""
*   ""Clean the build files for the ESP32 project in the `examples/hello_world` directory.""
*   ""Flash the firmware to my connected ESP32 device for the project in `my_app`.""

The MCP server will then execute the corresponding ESP-IDF commands (like `idf.py build`, `idf.py fullclean`, `idf.py flash`) based on the tools implemented in `main.py`.

The `result.gif` below shows an example interaction:

![Result](./result.gif)


### Examples 


1. Build ahttps://github.com/horw/esp-mcp,horw/esp-mcp,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)

### Goal
The goal of this MCP is to:
- Consolidate ESP-IDF and related project commands in one place.
- Simplify getting started using only LLM communication.

### How to contribute to the project

Simply find a command that is missing from this MCP and create a PR for it!

If you want someone to help you with this implementation, just open an issue.


### Notice
This project is currently a **Proof of Concept (PoC)** for an MCP server tailored for ESP-IDF workflows. 

**Current Capabilities:**
*   Supports basic ESP-IDF project build commands.
*   Flash built firmware to connected ESP devices with optional port specification.
*   Includes experimental support for automatic issue fixing based on build logs.

**Vision & Future Work:**
The long-term vision is to expand this MCP into a comprehensive toolkit for interacting with embedded devices, potentially integrating with home assistant platforms, and streamlining documentation access for ESP-IDF and related technologies. 

We envision features such as:
*   Broader ESP-IDF command support (e.g., `monitor`, `menuconfig` interaction if feasible).
*   Device management and information retrieval.
*   Integration with other embedded development tools and platforms.

Your ideas and contributions are welcome! Please feel free to discuss them by opening an issue.


### Install  

First, clone this MCP repository:  

```bash
<NAME_EMAIL>:horw/esp-mcp.git
```  

Then, configure it in your chatbot. 

The JSON snippet below is an example of how you might configure this `esp-mcp` server within a chatbot or an agent system that supports the Model Context Protocol (MCP). The exact configuration steps and format may vary depending on the specific chatbot system you are using. Refer to your chatbot's documentation for details on how to integrate MCP servers.

```json
{
    ""mcpServers"": {
        ""esp-run"": { // ""esp-run"" is an arbitrary name you can assign to this server configuration.
            ""command"": ""<path_to_uv_or_python_executable>"",
            ""args"": [
                ""--directory"",
                ""<path_to_cloned_esp-mcp_repository>"", // e.g., /path/to/your/cloned/esp-mcp
                ""run"",
                ""main.py"" // If using python directly, this might be just ""main.py"" and `command` would be your python interpreter
            ],
            ""env"": {
                ""IDF_PATH"": ""<path_to_your_esp-idf_directory>"" // e.g., ~/esp/esp-idf or C:\\Espressif\\frameworks\\esp-idf
            }
        }
    }
}
```

A few notes on the configuration:

*   **`command`**: This should be the full path to your `uv` executable if you are using it, or your Python interpreter (e.g., `/usr/bin/python3` or `C:\\Python39\\python.exe`) if you plan to run `main.py` directly.
*   **`args`**:
    *   The first argument to `--directory` should be the absolute path to where you cloned the `esp-mcp` repository.
    *   If you're using `uv`, the arguments `run main.py` are appropriate. If you're using Python directly, you might only need `main.py` in the `args` list, and ensure your `command` points to the Python executable.
*   **`IDF_PATH`**: This environment variable must point to the root directory of your ESP-IDF installation. ESP-IDF is Espressif's official IoT Development Framework. If you haven't installed it, please refer to the [official ESP-IDF documentation](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/index.html) for installation instructions.

### Usage

Once the `esp-mcp` server is configured and running, your LLM or chatbot can interact with it using the tools defined in this MCP. For example, you could ask your chatbot to:

*   ""Build the project located at `/path/to/my/esp-project` using the `esp-mcp`.""
*   ""Clean the build files for the ESP32 project in the `examples/hello_world` directory.""
*   ""Flash the firmware to my connected ESP32 device for the project in `my_app`.""

The MCP server will then execute the corresponding ESP-IDF commands (like `idf.py build`, `idf.py fullclean`, `idf.py flash`) based on the tools implemented in `main.py`.

The `result.gif` below shows an example interaction:

![Result](./result.gif)


### Examples 


1. Build and Flash
<img src=""./examples/build-flash.png"">




","Star
 62",2025-06-24T08:43:26.213729,esp-mcp - MCP Server | Model Context Protocol Integration,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)

### Goal
The goal of this MCP is to:...","['mcp server', 'model context protocol', 'ai integration', 'esp-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'esp-mcp', 'description': '[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)\n\n### Goal\nThe goal of this MCP is to:...', 'url': 'https://github.com/horw/esp-mcp', 'codeRepository': 'https://github.com/horw/esp-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Development,IoT,,"Examples, Goal, Install, Notice, Usage","{
    ""mcpServers"": {
        ""esp-run"": { // ""esp-run"" is an arbitrary name you can assign to this server configuration.
            ""command"": ""<path_to_uv_or_python_executable>"",
            ""args"": [
                ""--directory"",
                ""<path_to_cloned_esp-mcp_repository>"", // e.g., /path/to/your/cloned/esp-mcp
                ""run"",
                ""main.py"" // If using python directly, this might be just ""main.py"" and `command` would be your python interpreter
            ],
   ",,,2025-06-23,horw,The goal of this MCP is to:
https://github.com/pab1it0/chess-mcp,pab1it0/chess-mcp,"# Chess.com MCP Server

A [Model Context Protocol][mcp] (MCP) server for Chess.com's Published Data API.

This provides access to Chess.com player data, game records, and other public information through standardized MCP interfaces, allowing AI assistants to search and analyze chess information.

https://github.com/user-attachments/assets/3b33361b-b604-465c-9f6a-3699b6907757

[mcp]: https://modelcontextprotocol.io/introduction/introduction

## Features

- [x] Access player profiles, stats, and game records
- [x] Search games by date and player
- [x] Check player online status
- [x] Get information about clubs and titled players
- [x] No authentication required (uses Chess.com's public API)
- [x] Docker containerization support
- [x] Provide interactivehttps://github.com/ac3xx/mcp-servers-kagi,ac3xx/mcp-servers-kagi,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integration

This is a TypeScript-based MCP server that integrates the Kagi Search API. It demonstrates core MCP concepts by providing:

- Tools for performing web searches and other operations using Kagi's API (currently in private beta)

## Features

### Implemented Tools
- `kagi_search` - Perform web searches using Kagi
  - Takes a query string and optional limit as parameters
  - Returns search results from Kagi's API

### Planned Tools (Not Yet Implemented)
- `kagi_summarize` - Generate summaries of web pages or text
- `kagi_fastgpt` - Get quick responses using Kagi's FastGPT
- `kagi_enrich` - Fetch enriched news results on specific topics

## Development

Install dependencies:
```bash
npm install
```

Build the server:
```bash
npm run build
```

For development with auto-rebuild:
```bash
npm run watch
```

## Environment Setup

Create a `.env` file in the root directory with your Kagi API key:

```
KAGI_API_KEY=your_api_key_here
```

Make sure to add `.env` to your `.gitignore` file to keep your API key secure.

## Installation

### Installing via Smithery

To install Kagi Server for Claude Desktop automatically via [Smithery](https://smithery.ai/protocol/kagi-server):

```bash
npx @smithery/cli install kagi-server --client claude
```

To use with Claude Desktop, add the server config:

On MacOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
        ""KAGI_API_KEY"": ""your_api_key_here""
      }
    }
  }
}
```

### Debugging

Since MCP servers communicate over stdio, debugging can be challenging. We recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
npm run inspector
```

The Inspector will provide a URL to access debugging tools in your browser.

## Usage

Once the server is running and connected to Claude Desktop, you can use it to perform web searches. For example:

1. Ask Claude: ""Can you search for information about the latest advancements in quantum computing?""
2. Claude will use the `kagi_search` tool to fetch results from Kagi's API.
3. Claude will then summarize or analyze the search results for you.

Note: The planned tools (summarize, fastgpt, enrich) are not yet implemented and cannot be used.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. Some areas for contribution include:

- Implementing the planned tools (summarize, fastgpt, enrich)
- Improving error handling and input validation
- Enhancing documentation and usage examples

## License

This project is licensed under the MIT License.

## Roadmap

- Implement `kagi_summarize` tool for webpage and text summarization
- Implement `kagi_fastgpt` tool for quick responses
- Implement `kagi_enrich` tool for fetching enriched news results
- Improve error handling and add more robust input validation
- Add more comprehensive usage examples and documentation
- Publish the package to npm for easy installation and use with Claude Desktop and npx","Star
 33",2025-06-24T08:43:26.213725,mcp-servers-kagi - MCP Server | Model Context Protocol Integration,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integr...","['mcp server', 'model context protocol', 'ai integration', 'mcp-servers-kagi']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-servers-kagi', 'description': '# kagi-server MCP Server\n\n[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)\nMCP server for Kagi API integr...', 'url': 'https://github.com/ac3xx/mcp-servers-kagi', 'codeRepository': 'https://github.com/ac3xx/mcp-servers-kagi', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,AI Tools,Search Engine,npm install,Debugging,"{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
   https://github.com/ac3xx/mcp-servers-kagi,ac3xx/mcp-servers-kagi,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integration

This is a TypeScript-based MCP server that integrates the Kagi Search API. It demonstrates core MCP concepts by providing:

- Tools for performing web searches and other operations using Kagi's API (currently in private beta)

## Features

### Implemented Tools
- `kagi_search` - Perform web searches using Kagi
  - Takes a query string and optional limit as parameters
  - Returns search results from Kagi's API

### Planned Tools (Not Yet Implemented)
- `kagi_summarize` - Generate summaries of web pages or text
- `kagi_fastgpt` - Get quick responses using Kagi's FastGPT
- `kagi_enrich` - Fetch enriched news results on specific topics

## Development

Install dependencies:
```bash
npm install
```

Build the server:
```bash
npm run build
```

For development with auto-rebuild:
```bash
npm run watch
```

## Environment Setup

Create a `.env` file in the root directory with your Kagi API key:

```
KAGI_API_KEY=your_api_key_here
```

Make sure to add `.env` to your `.gitignore` file to keep your API key secure.

## Installation

### Installing via Smithery

To install Kagi Server for Claude Desktop automatically via [Smithery](https://smithery.ai/protocol/kagi-server):

```bash
npx @smithery/cli install kagi-server --client claude
```

To use with Claude Desktop, add the server config:

On MacOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
        ""KAGI_API_KEY"": ""your_api_key_here""
      }
    }
  }
}
```

### Debugging

Since MCP servers communicate over stdio, debugging can be challenging. We recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
npm run inspector
```

The Inspector will provide a URL to access debugging tools in your browser.

## Usage

Once the server is running and connected to Claude Desktop, you can use it to perform web searches. For example:

1. Ask Claude: ""Can you search for information about the latest advancements in quantum computing?""
2. Claude will use the `kagi_search` tool to fetch results from Kagi's API.
3. Claude will then summarize or analyze the search results for you.

Note: The planned tools (summarize, fastgpt, enrich) are not yet implemented and cannot be used.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. Some areas for contribution include:

- Implementing the planned tools (summarize, fastgpt, enrich)
- Improving error handling and input validation
- Enhancing documentation and usage examples

## License

This project is licensed under the MIT License.

## Roadmap

- Implement `kagi_summarize` tool for webpage and text summarization
- Implement `kagi_fastgpt` tool for quick responses
- Implement `kagi_enrich` tool for fetching enriched news results
- Improve error handling and add more robust input validation
- Add more comprehensive usage examples and documentation
- Publish the package to npm for easy installation and use with Claude Desktop and npx","Star
 33",2025-06-24T08:43:26.213725,mcp-servers-kagi - MCP Server | Model Context Protocol Integration,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integr...","['mcp server', 'model context protocol', 'ai integration', 'mcp-servers-kagi']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-servers-kagi', 'description': '# kagi-server MCP Server\n\n[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)\nMCP server for Kagi API integr...', 'url': 'https://github.com/ac3xx/mcp-servers-kagi', 'codeRepository': 'https://github.com/ac3xx/mcp-servers-kagi', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,AI Tools,Search Engine,npm install,Debugging,"{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
        ""KAGI_API_KEY"": ""your_api_key_here""
      }
    }
  }
}",,MIT License,2025-06-03,ac3xx,MCP server for Kagi API integration
https://github.com/ahnlabio/bicscan-mcp,ahnlabio/bicscan-mcp,"# BICScan MCP Server

A powerful and efficient Blockchain address risk scoring API MCP Server, leveraging the BICScan API to provide comprehensive risk assessments and asset information for blockchain addresses, domains, and decentralized applications (dApps).

🎉 We're listed on https://github.com/modelcontextprotocol/servers for official integration 🎉


https://github.com/user-attachments/assets/f9425429-1cb1-4508-b962-81351075258b

## Key Features
- **Risk Scoring**: Obtain risk scores for various blockchain entities, including crypto addresses, domain names, and decentralized application URLs, with scores ranging from 0 to 100, where 100 indicates high risk.
- **Asset Information**: Retrieve detailed asset holdings for specified crypto addresses, including cryptocurrencies and tokens, with support for multiple blockchain networks.
- **Real-time Scanning**: Utilize the BICScan API to perfhttps://github.com/0xshellming/mcp-summarizer,0xshellming/mcp-summarizer,"# MCP Content Summarizer Server

A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content using Google's Gemini 1.5 Pro model. This server can help you generate concise summaries while maintaining key information from different content formats.

<a href=""https://3min.top""><img width=""380"" height=""200"" src=""/public/imgs/section1_en.jpg"" alt=""MCP Content Summarizer Server"" /></a>

## Powered by 3MinTop

The summarization service is powered by [3MinTop](https://3min.top), an AI-powered reading tool that helps you understand a chapter's content in just three minutes. 3MinTop transforms complex content into clear summaries, making learning efficient and helping build lasting reading habits.

## Features

- Universal content summarization using Google's Gemini 1.5 Pro model
- Support for multiple content types:
  - Plain text
  - Web pages
  - PDF documents
  - EPUB books
  - HTML content
- Customizable summary length
- Multi-language support
- Smart context preservation
- Dynamic greeting resource for testing

## Getting Started

1. Clone this repository
2. Install dependencies:
   ```
   pnpm install
   ```

3. Build the project:
   ```
   pnpm run build
   ```

4. Start the server:
   ```
   pnpm start
   ```

## Development

- Use `pnpm run dev` to start the TypeScript compiler in watch mode
- Modify `src/index.ts` to customize server behavior or add new tools

## Usage with Desktop App

To integrate this server with a desktop app, add the following to your app's server configuration:

```js
{
  ""mcpServers"": {
    ""content-summarizer"": {
      ""command"": ""node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}
```

## Available Tools

### summarize

Summarizes content from various sources using the following parameters:
- `content` (string | object): The input content to summarize. Can be:
  - Text string
  - URL for web pages
  - Base64 encoded PDF
  - EPUB file content
- `type` (string): Content type (""text"", ""url"", ""pdf"", ""epub"")
- `maxLength` (number, optional): Maximum length of the summary in characters (default: 200)
- `language` (string, optional): Target language for the summary (default: ""en"")
- `focus` (string, optional): Specific aspect to focus on in the summary
- `style` (string, optional): Summary style (""concise"", ""detailed"", ""bullet-points"")

Example usage:

```typescript
// Summarize a webpage
const result = await server.invoke(""summarize"", {
  content: ""https://example.com/article"",
  type: ""url"",
  maxLength: 300,
  style: ""bullet-points""
});

// Summarize a PDF document
const result = await server.invoke(""summarize"", {
  content: pdfBase64Content,
  type: ""pdf"",
  language: ""zh"",
  style: ""detailed""
});
```

### greeting

A dynamic resource that demonstrates basic MCP resource functionality:
- URI format: `greeting://{name}`
- Returns a greeting message with the provided name

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details. ","Star
 86",2025-06-24T08:43:26.213721,mcp-summarizer - MCP Server | Model Context Protocol Integration,"# MCP Content Summarizer Server

A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content usin...","['mcp server', 'model context protocol', 'ai integration', 'mcp-summarizer']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-summarizer', 'description': '# MCP Content Summarizer Server\n\nA Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content usin...', 'url': 'https://github.com/0xshellming/mcp-summarizer', 'codeRepository': 'https://github.com/0xshellming/mcp-summarizer', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,AI Tools,Text Processing,npm install,"greeting, summarize","""content-summarizer"": {
      ""command"": ""node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE",,MIT,2025-06-23,0xshellming,A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types ohttps://github.com/0xshellming/mcp-summarizer,0xshellming/mcp-summarizer,"# MCP Content Summarizer Server

A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content using Google's Gemini 1.5 Pro model. This server can help you generate concise summaries while maintaining key information from different content formats.

<a href=""https://3min.top""><img width=""380"" height=""200"" src=""/public/imgs/section1_en.jpg"" alt=""MCP Content Summarizer Server"" /></a>

## Powered by 3MinTop

The summarization service is powered by [3MinTop](https://3min.top), an AI-powered reading tool that helps you understand a chapter's content in just three minutes. 3MinTop transforms complex content into clear summaries, making learning efficient and helping build lasting reading habits.

## Features

- Universal content summarization using Google's Gemini 1.5 Pro model
- Support for multiple content types:
  - Plain text
  - Web pages
  - PDF documents
  - EPUB books
  - HTML content
- Customizable summary length
- Multi-language support
- Smart context preservation
- Dynamic greeting resource for testing

## Getting Started

1. Clone this repository
2. Install dependencies:
   ```
   pnpm install
   ```

3. Build the project:
   ```
   pnpm run build
   ```

4. Start the server:
   ```
   pnpm start
   ```

## Development

- Use `pnpm run dev` to start the TypeScript compiler in watch mode
- Modify `src/index.ts` to customize server behavior or add new tools

## Usage with Desktop App

To integrate this server with a desktop app, add the following to your app's server configuration:

```js
{
  ""mcpServers"": {
    ""content-summarizer"": {
      ""command"": ""node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}
```

## Available Tools

### summarize

Summarizes content from various sources using the following parameters:
- `content` (string | object): The input content to summarize. Can be:
  - Text string
  - URL for web pages
  - Base64 encoded PDF
  - EPUB file content
- `type` (string): Content type (""text"", ""url"", ""pdf"", ""epub"")
- `maxLength` (number, optional): Maximum length of the summary in characters (default: 200)
- `language` (string, optional): Target language for the summary (default: ""en"")
- `focus` (string, optional): Specific aspect to focus on in the summary
- `style` (string, optional): Summary style (""concise"", ""detailed"", ""bullet-points"")

Example usage:

```typescript
// Summarize a webpage
const result = await server.invoke(""summarize"", {
  content: ""https://example.com/article"",
  type: ""url"",
  maxLength: 300,
  style: ""bullet-points""
});

// Summarize a PDF document
const result = await server.invoke(""summarize"", {
  content: pdfBase64Content,
  type: ""pdf"",
  language: ""zh"",
  style: ""detailed""
});
```

### greeting

A dynamic resource that demonstrates basic MCP resource functionality:
- URI format: `greeting://{name}`
- Returns a greeting message with the provided name

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details. ","Star
 86",2025-06-24T08:43:26.213721,mcp-summarizer - MCP Server | Model Context Protocol Integration,"# MCP Content Summarizer Server

A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content usin...","['mcp server', 'model context protocol', 'ai integration', 'mcp-summarizer']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-summarizer', 'description': '# MCP Content Summarizer Server\n\nA Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content usin...', 'url': 'https://github.com/0xshellming/mcp-summarizer', 'codeRepository': 'https://github.com/0xshellming/mcp-summarizer', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,AI Tools,Text Processing,npm install,"greeting, summarize","""content-summarizer"": {
      ""command"": ""node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE",,MIT,2025-06-23,0xshellming,A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content using Google's Gemini 1.5 Pro model. This server can help you generate concise s...
https://github.com/MarketplaceAdPros/amazon-ads-mcp-server,MarketplaceAdPros/amazhttps://github.com/Dumpling-AI/mcp-server-dumplingai,Dumpling-AI/mcp-server-dumplingai,"# Dumpling AI MCP Server

A Model Context Protocol (MCP) server implementation that integrates with Dumpling AI for data scraping, content processing, knowledge management, AI agents, and code execution capabilities.

[![smithery badge](https://smithery.ai/badge/@Dumpling-AI/mcp-server-dumplingai)](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai)

## Features

- Complete integration with all Dumpling AI API endpoints
- Data APIs for YouTube transcripts, search, autocomplete, maps, places, news, and reviews
- Web scraping with support for scraping, crawling, screenshots, and structured data extraction
- Document conversion tools for text extraction, PDF operations, video processing
- Extract data from documents, images, audio, and video
- AI capabilities including agent completions, knowledge base management, and image generation
- Developer tools for running JavaScript and Python code in a secure environment
- Automatic error handling and detailed response formatting

## Installation

### Installing via Smithery

To install mcp-server-dumplingai for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai):

```bash
npx -y @smithery/cli install @Dumpling-AI/mcp-server-dumplingai --client claude
```

### Running with npx

```bash
env DUMPLING_API_KEY=your_api_key npx -y mcp-server-dumplingai
```

### Manual Installation

```bash
npm install -g mcp-server-dumplingai
```

### Running on Cursor

Configuring Cursor 🖥️ Note: Requires Cursor version 0.45.6+

To configure Dumpling AI MCP in Cursor:

1. Open Cursor Settings
2. Go to Features > MCP Servers
3. Click ""+ Add New MCP Server""
4. Enter the following:

```
{
  ""mcpServers"": {
    ""dumplingai"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-server-dumplingai""],
      ""env"": {
        ""DUMPLING_API_KEY"": ""<your-api-key>""
      }
    }
  }
}
```

> If you are using Windows and are running into issues, try `cmd /c ""set DUMPLING_API_KEY=your-api-key && npx -y mcp-server-dumplingai""`

Replace `your-api-key` with your Dumpling AI API key.

## Configuration

### Environment Variables

- `DUMPLING_API_KEY`: Your Dumpling AI API key (required)

## Available Tools

### Data APIs

#### 1. Get YouTube Transcript (`get-youtube-transcript`)

Extract transcripts from YouTube videos with optional timestamps.

```json
{
  ""name"": ""get-youtube-transcript"",
  ""arguments"": {
    ""videoUrl"": ""https://www.youtube.com/watch?v=dQw4w9WgXcQ"",
    ""includeTimestamps"": true,
    ""timestampsToCombine"": 3,
    ""preferredLanguage"": ""en""
  }
}
```

#### 2. Search (`search`)

Perform Google web searches and optionally scrape content from results.

```json
{
  ""name"": ""search"",
  ""arguments"": {
    ""query"": ""machine learning basics"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastMonth"",
    ""scrapeResults"": true,
    ""numResultsToScrape"": 3,
    ""scrapeOptions"": {
      ""format"": ""markdown"",
      ""cleaned"": true
    }
  }
}
```

#### 3. Get Autocomplete (`get-autocomplete`)

Get Google search autocomplete suggestions for a query.

```json
{
  ""name"": ""get-autocomplete"",
  ""arguments"": {
    ""query"": ""how to learn"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""location"": ""New York""
  }
}
```

#### 4. Search Maps (`search-maps`)

Search Google Maps for locations and businesses.

```json
{
  ""name"": ""search-maps"",
  ""arguments"": {
    ""query"": ""coffee shops"",
    ""gpsPositionZoom"": ""37.7749,-122.4194,14z"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 5. Search Places (`search-places`)

Search for places with more detailed information.

```json
{
  ""name"": ""search-places"",
  ""arguments"": {
    ""query"": ""hotels in paris"",
    ""country"": ""fr"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 6. Search News (`search-news`)

Search for news articles with customizable parameters.

```json
{
  ""name"": ""search-news"",
  ""arguments"": {
    ""query"": ""climate change"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastWeek""
  }
}
```

#### 7. Get Google Reviews (`get-google-reviews`)

Retrieve Google reviews for businesses or places.

```json
{
  ""name"": ""get-googlehttps://github.com/Dumpling-AI/mcp-server-dumplingai,Dumpling-AI/mcp-server-dumplingai,"# Dumpling AI MCP Server

A Model Context Protocol (MCP) server implementation that integrates with Dumpling AI for data scraping, content processing, knowledge management, AI agents, and code execution capabilities.

[![smithery badge](https://smithery.ai/badge/@Dumpling-AI/mcp-server-dumplingai)](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai)

## Features

- Complete integration with all Dumpling AI API endpoints
- Data APIs for YouTube transcripts, search, autocomplete, maps, places, news, and reviews
- Web scraping with support for scraping, crawling, screenshots, and structured data extraction
- Document conversion tools for text extraction, PDF operations, video processing
- Extract data from documents, images, audio, and video
- AI capabilities including agent completions, knowledge base management, and image generation
- Developer tools for running JavaScript and Python code in a secure environment
- Automatic error handling and detailed response formatting

## Installation

### Installing via Smithery

To install mcp-server-dumplingai for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai):

```bash
npx -y @smithery/cli install @Dumpling-AI/mcp-server-dumplingai --client claude
```

### Running with npx

```bash
env DUMPLING_API_KEY=your_api_key npx -y mcp-server-dumplingai
```

### Manual Installation

```bash
npm install -g mcp-server-dumplingai
```

### Running on Cursor

Configuring Cursor 🖥️ Note: Requires Cursor version 0.45.6+

To configure Dumpling AI MCP in Cursor:

1. Open Cursor Settings
2. Go to Features > MCP Servers
3. Click ""+ Add New MCP Server""
4. Enter the following:

```
{
  ""mcpServers"": {
    ""dumplingai"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-server-dumplingai""],
      ""env"": {
        ""DUMPLING_API_KEY"": ""<your-api-key>""
      }
    }
  }
}
```

> If you are using Windows and are running into issues, try `cmd /c ""set DUMPLING_API_KEY=your-api-key && npx -y mcp-server-dumplingai""`

Replace `your-api-key` with your Dumpling AI API key.

## Configuration

### Environment Variables

- `DUMPLING_API_KEY`: Your Dumpling AI API key (required)

## Available Tools

### Data APIs

#### 1. Get YouTube Transcript (`get-youtube-transcript`)

Extract transcripts from YouTube videos with optional timestamps.

```json
{
  ""name"": ""get-youtube-transcript"",
  ""arguments"": {
    ""videoUrl"": ""https://www.youtube.com/watch?v=dQw4w9WgXcQ"",
    ""includeTimestamps"": true,
    ""timestampsToCombine"": 3,
    ""preferredLanguage"": ""en""
  }
}
```

#### 2. Search (`search`)

Perform Google web searches and optionally scrape content from results.

```json
{
  ""name"": ""search"",
  ""arguments"": {
    ""query"": ""machine learning basics"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastMonth"",
    ""scrapeResults"": true,
    ""numResultsToScrape"": 3,
    ""scrapeOptions"": {
      ""format"": ""markdown"",
      ""cleaned"": true
    }
  }
}
```

#### 3. Get Autocomplete (`get-autocomplete`)

Get Google search autocomplete suggestions for a query.

```json
{
  ""name"": ""get-autocomplete"",
  ""arguments"": {
    ""query"": ""how to learn"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""location"": ""New York""
  }
}
```

#### 4. Search Maps (`search-maps`)

Search Google Maps for locations and businesses.

```json
{
  ""name"": ""search-maps"",
  ""arguments"": {
    ""query"": ""coffee shops"",
    ""gpsPositionZoom"": ""37.7749,-122.4194,14z"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 5. Search Places (`search-places`)

Search for places with more detailed information.

```json
{
  ""name"": ""search-places"",
  ""arguments"": {
    ""query"": ""hotels in paris"",
    ""country"": ""fr"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 6. Search News (`search-news`)

Search for news articles with customizable parameters.

```json
{
  ""name"": ""search-news"",
  ""arguments"": {
    ""query"": ""climate change"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastWeek""
  }
}
```

#### 7. Get Google Reviews (`get-google-reviews`)

Retrieve Google reviews for businesses or places.

```json
{
  ""name"": ""get-google-reviews"",
  ""arguments"": {
    ""businessName"": ""Eiffel Tower"",
    ""location"": ""Paris, France"",
    ""limit"": 10,
    ""sortBy"": ""relevance""
  }
}
```

### Web Scraping

#### 8. Scrape (`scrape`)

Extract content from a web page with formatting options.

```json
{
  ""name"": ""scrape"",
  ""arguments"": {
    ""url"": ""https://example.com"",
    ""format"": ""markdown"",
    ""cleaned"": true,
    ""renderJs"": true
  }
}
```

#### 9. Crawl (`crawl`)

Recursively crawl websites and extract content with customizable parameters.

```json
{
  ""name"": ""crawl"",
  ""arguments"": {
    ""baseUrl"": ""https://example.com"",
    ""maxPages"": 10,
    ""crawlBeyondBaseUrl"": false,
    ""depth"": 2,
    ""scrapeOptions"": {
      ""format"": ""markdown"",
      ""cleaned"": true,
      ""renderJs"": true
    }
  }
}
```

#### 10. Screenshot (`screenshot`)

Capture screenshots of web pages with customizable viewport and format options.

```json
{
  ""name"": ""screenshot"",
  ""arguments"": {
    ""url"": ""https://example.com"",
    ""width"": 1280,
    ""height"": 800,
    ""fullPage"": true,
    ""format"": ""png"",
    ""waitFor"": 1000
  }
}
```

#### 11. Extract (`extract`)

Extract structured data from web pages using AI-powered instructions.

```json
{
  ""name"": ""extract"",
  ""arguments"": {
    ""url"": ""https://example.com/products"",
    ""instructions"": ""Extract all product names, prices, and descriptions from this page"",
    ""schema"": {
      ""products"": [
        {
          ""name"": ""string"",
          ""price"": ""number"",
          ""description"": ""string""
        }
      ]
    },
    ""renderJs"": true
  }
}
```

### Document Conversion

#### 12. Doc to Text (`doc-to-text`)

Convert documents to plaintext with optional OCR.

```json
{
  ""name"": ""doc-to-text"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""options"": {
      ""ocr"": true,
      ""language"": ""en""
    }
  }
}
```

#### 13. Convert to PDF (`convert-to-pdf`)

Convert various file formats to PDF.

```json
{
  ""name"": ""convert-to-pdf"",
  ""arguments"": {
    ""url"": ""https://example.com/document.docx"",
    ""format"": ""docx"",
    ""options"": {
      ""quality"": 90,
      ""pageSize"": ""A4"",
      ""margin"": 10
    }
  }
}
```

#### 14. Merge PDFs (`merge-pdfs`)

Combine multiple PDFs into a single document.

```json
{
  ""name"": ""merge-pdfs"",
  ""arguments"": {
    ""urls"": [""https://example.com/doc1.pdf"", ""https://example.com/doc2.pdf""],
    ""options"": {
      ""addPageNumbers"": true,
      ""addTableOfContents"": true
    }
  }
}
```

#### 15. Trim Video (`trim-video`)

Extract a specific clip from a video.

```json
{
  ""name"": ""trim-video"",
  ""arguments"": {
    ""url"": ""https://example.com/video.mp4"",
    ""startTime"": 30,
    ""endTime"": 60,
    ""output"": ""mp4"",
    ""options"": {
      ""quality"": 720,
      ""fps"": 30
    }
  }
}
```

#### 16. Extract Document (`extract-document`)

Extract specific content from documents in various formats.

```json
{
  ""name"": ""extract-document"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""format"": ""structured"",
    ""options"": {
      ""ocr"": true,
      ""language"": ""en"",
      ""includeMetadata"": true
    }
  }
}
```

#### 17. Extract Image (`extract-image`)

Extract text and information from images.

```json
{
  ""name"": ""extract-image"",
  ""arguments"": {
    ""url"": ""https://example.com/image.jpg"",
    ""extractionType"": ""text"",
    ""options"": {
      ""language"": ""en"",
      ""detectOrientation"": true
    }
  }
}
```

#### 18. Extract Audio (`extract-audio`)

Transcribe and extract information from audio files.

```json
{
  ""name"": ""extract-audio"",
  ""arguments"": {
    ""url"": ""https://example.com/audio.mp3"",
    ""language"": ""en"",
    ""options"": {
      ""model"": ""enhanced"",
      ""speakerDiarization"": true,
      ""wordTimestamps"": true
    }
  }
}
```

#### 19. Extract Video (`extract-video`)

Extract content from videos including transcripts, scenes, and objects.

```json
{
  ""name"": ""extract-video"",
  ""arguments"": {
    ""url"": ""https://example.com/video.mp4"",
    ""extractionType"": ""transcript"",
    ""options"": {
      ""language"": ""en"",
      ""speakerDiarization"": true
    }
  }
}
```

#### 20. Read PDF Metadata (`read-pdf-metadata`)

Extract metadata from PDF files.

```json
{
  ""name"": ""read-pdf-metadata"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""includeExtended"": true
  }
}
```

#### 21. Write PDF Metadata (`write-pdf-metadata`)

Update metadata in PDF files.

```json
{
  ""name"": ""write-pdf-metadata"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""metadata"": {
      ""title"": ""New Title"",
      ""author"": ""John Doe"",
      ""keywords"": [""keyword1"", ""keyword2""]
    }
  }
}
```

### AI

#### 22. Generate Agent Completion (`generate-agent-completion`)

Get AI agent completions with optional tool definitions.

```json
{
  ""name"": ""generate-agent-completion"",
  ""arguments"": {
    ""prompt"": ""How can I improve my website's SEO?"",
    ""model"https://github.com/olalonde/mcp-human,olalonde/mcp-human,"# MCP-Human: Human Assistance for AI Assistants

A Model Context Protocol (MCP) server that enables AI assistants to get human input when needed. This tool creates tasks on Amazon Mechanical Turk that let real humans answer questions from AI systems. While primarily a proof-of-concept, it demonstrates how to build human-in-the-loop AI systems using the MCP standard. See [limitations](#limitations) for current constraints.

![we need to go deeper](./deeper.gif)

## Setup

### Prerequisites

- Node.js 16+
- AWS credentials with MTurk permissions. See [instructions below](#setting-up-aws-user-with-mechanical-turk-access).
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html) (recommended for setting aws credentials)

### Configuring AWS credentials

```sh
# Configure AWS credentials for profile mcp-human
export AWS_ACCESS_KEY_ID=""your_access_key""
export AWS_SECRET_ACCESS_KEY=""your_secret_key""
aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID} --profile mcp-human
aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY} --profile mcp-human
```

### Configuring MCP server with your MCP client

### Claude code

Sandbox mode:

```sh
claude mcp add human -- npx -y mcp-human@latest
```

The server defaults to [sandbox mode](https://workersandbox.mturk.com/) (for testing). If you want to submit real requests, use `MTURK_SANDBOX=false`.

```sh
claude mcp add human -e MTURK_SANDBOX=false -- npx -y mcp-human@latest
```

### Generic

Update the configuration of your MCP client to the following:

```json
{
  ""mcpServers"": {
    ""human"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-human@latest""]
    }
  }
}
```

e.g.: Claude Desktop (MacOS): `~/Library/Application\ Support/Claude/claude_desktop_config.json`

## Configuration

The server can be configured with the following environment variables:

| Variable         | Description                                        | Default                          |
| ---------------- | -------------------------------------------------- | -------------------------------- |
| `MTURK_SANDBOX`  | Use MTurk sandbox (`true`) or production (`false`) | `true`                           |
| `AWS_REGION`     | AWS region for MTurk                               | `us-east-1`                      |
| `AWS_PROFILE`    | AWS profile to use for credentials                 | `mcp-human`                      |
| `DEFAULT_REWARD` | The reward amount in USD.                          | `0.05`                           |
| `FORM_URL`       | URL where the form is hosted. Needs to be https.   | `https://syskall.com/mcp-human/` |

## Setting Up AWS User with Mechanical Turk Access

To create an AWS user with appropriate permissions for Mechanical Turk:

1. **Log in to the AWS Management Console**:

   - Go to https://aws.amazon.com/console/
   - Sign in as a root user or an administrator

2. **Create a new IAM User**:

   - Navigate to IAM (Identity and Access Management)
   - Click ""Users"" > ""Create user""
   - Enter a username (e.g., `mturk-api-user`)
   - Click ""Next"" to proceed to permissions

3. **Set Permissions**:

   - Choose ""Attach existing policies directly""
   - Search for and select `AmazonMechanicalTurkFullAccess`
   - If you need more granular control, you can create a custom policy with specific MTurk permissions
   - Click ""Next"" and then ""Create user""

4. **Create Access Keys**:

   - After user creation, click on the username to go to their detail page
   - Go to the ""Security credentials"" tab
   - In the ""Access keys"" section, click ""Create access key""
   - Choose ""Application running outside AWS"" or appropriate option
   - Click through the wizard and finally ""Create access key""

5. **Save Credentials**:

   - Download the CSV file or copy the Access key ID and Secret access key
   - These will be used as `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` environment variables
   - **Important**: This is the only time you'll see the secret access key, so save it securely

6. **Configure MTurk Requester Settings**:
   - Visit the MTurk Requester website: https://requester.mturk.com/
   - Set up payment method and other account details
   - For testing, use the MTurk Sandbox: https://requestersandbox.mturk.com/

> **Note**: Always start with the MTurk Sandbox (`MThttps://github.com/Flux159/mcp-server-kubernetes,Flux159/mcp-server-kubernetes,"# MCP Server Kubernetes

[![CI](https://github.com/Flux159/mcp-server-kubernetes/actions/workflows/ci.yml/badge.svg)](https://github.com/yourusername/mcp-server-kubernetes/actions/workflows/ci.yml)
[![Language](https://img.shields.io/github/languages/top/Flux159/mcp-server-kubernetes)](https://github.com/yourusername/mcp-server-kubernetes)
[![Bun](https://img.shields.io/badge/runtime-bun-orange)](https://bun.sh)
[![Kubernetes](https://img.shields.io/badge/kubernetes-%23326ce5.svg?style=flat&logo=kubernetes&logoColor=white)](https://kubernetes.io/)
[![Docker](https://img.shields.io/badge/docker-%230db7ed.svg?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)
[![Stars](https://img.shields.io/github/stars/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/stargazers)
[![Issues](https://img.shields.io/github/issues/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/issues)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/Flux159/mcp-server-kubernetes/pulls)
[![Last Commit](https://img.shields.io/github/last-commit/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/commits/main)
[![smithery badge](https://smithery.ai/badge/mcp-server-kubernetes)](https://smithery.ai/protocol/mcp-server-kubernetes)

MCP Server that can connect to a Kubernetes cluster and manage it. Supports loading kubeconfig from multiple sources in priority order.

https://github.com/user-attachments/assets/f25f8f4e-4d04-479b-9ae0-5dac452dd2ed

<a href=""https://glama.ai/mcp/servers/w71ieamqrt""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/w71ieamqrt/badge"" /></a>

## Usage with Claude Desktop

```json
{
  ""mcpServers"": {
    ""kubernetes"": {
      ""command"": ""npx"",
      ""args"": [""mcp-server-kubernetes""]
    }
  }
}
```

By default, the server loads kubeconfig from `~/.kube/config`. For additional authentication options (environment variables, custom paths, etc.), see [ADVANCED_README.md](ADVANCED_README.md).

The server will automatically connect to your current kubectl context. Make sure you have:

1. kubectl installed and in your PATH
2. A valid kubeconfig file with contexts configured
3. Access to a Kubernetes cluster configured for kubectl (e.g. minikube, Rancher Desktop, GKE, etc.)
4. Helm v3 installed and in your PATH (no Tiller required). Optional if you don't plan to use Helm.

You can verify your connection by asking Claude to list your pods or create a test deployment.

If you have errors open up a standard terminal and run `kubectl get pods` to see if you can connect to your cluster without credentials issues.

## Usage with mcp-chat

[mcp-chat](https://github.com/Flux159/mcp-chat) is a CLI chat client for MCP servers. You can use it to interact with the Kubernetes server.

```shell
npx mcp-chat --server ""npx mcp-server-kubernetes""
```

Alternatively, pass it your existing Claude Desktop configuration file from above (Linux should pass the correct path to config):

Mac:

```shell
npx mcp-chat --config ""~/Library/Application Support/Claude/claude_desktop_config.json""
```

Windows:

```shell
npx mcp-chat --config ""%APPDATA%\Claude\claude_desktop_config.json""
```

## Features

- [x] Connect to a Kubernetes cluster
- [x] Unified kubectl API for managing resources
  - Get or list resources with `kubectl_get`
  - Describe resources with `kubectl_describe`
  - List resources with `kubectl_list`
  - Create resources with `kubectl_create`
  - Apply YAML manifests with `kubectl_apply`
  - Delete resources with `kubectl_delete`
  - Get logs with `kubectl_logs`
  - Manage kubectl contexts with `kubectl_context`
  https://github.com/Flux159/mcp-server-kubernetes,Flux159/mcp-server-kubernetes,"# MCP Server Kubernetes

[![CI](https://github.com/Flux159/mcp-server-kubernetes/actions/workflows/ci.yml/badge.svg)](https://github.com/yourusername/mcp-server-kubernetes/actions/workflows/ci.yml)
[![Language](https://img.shields.io/github/languages/top/Flux159/mcp-server-kubernetes)](https://github.com/yourusername/mcp-server-kubernetes)
[![Bun](https://img.shields.io/badge/runtime-bun-orange)](https://bun.sh)
[![Kubernetes](https://img.shields.io/badge/kubernetes-%23326ce5.svg?style=flat&logo=kubernetes&logoColor=white)](https://kubernetes.io/)
[![Docker](https://img.shields.io/badge/docker-%230db7ed.svg?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)
[![Stars](https://img.shields.io/github/stars/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/stargazers)
[![Issues](https://img.shields.io/github/issues/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/issues)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/Flux159/mcp-server-kubernetes/pulls)
[![Last Commit](https://img.shields.io/github/last-commit/Flux159/mcp-server-kubernetes)](https://github.com/Flux159/mcp-server-kubernetes/commits/main)
[![smithery badge](https://smithery.ai/badge/mcp-server-kubernetes)](https://smithery.ai/protocol/mcp-server-kubernetes)

MCP Server that can connect to a Kubernetes cluster and manage it. Supports loading kubeconfig from multiple sources in priority order.

https://github.com/user-attachments/assets/f25f8f4e-4d04-479b-9ae0-5dac452dd2ed

<a href=""https://glama.ai/mcp/servers/w71ieamqrt""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/w71ieamqrt/badge"" /></a>

## Usage with Claude Desktop

```json
{
  ""mcpServers"": {
    ""kubernetes"": {
      ""command"": ""npx"",
      ""args"": [""mcp-server-kubernetes""]
    }
  }
}
```

By default, the server loads kubeconfig from `~/.kube/config`. For additional authentication options (environment variables, custom paths, etc.), see [ADVANCED_README.md](ADVANCED_README.md).

The server will automatically connect to your current kubectl context. Make sure you have:

1. kubectl installed and in your PATH
2. A valid kubeconfig file with contexts configured
3. Access to a Kubernetes cluster configured for kubectl (e.g. minikube, Rancher Desktop, GKE, etc.)
4. Helm v3 installed and in your PATH (no Tiller required). Optional if you don't plan to use Helm.

You can verify your connection by asking Claude to list your pods or create a test deployment.

If you have errors open up a standard terminal and run `kubectl get pods` to see if you can connect to your cluster without credentials issues.

## Usage with mcp-chat

[mcp-chat](https://github.com/Flux159/mcp-chat) is a CLI chat client for MCP servers. You can use it to interact with the Kubernetes server.

```shell
npx mcp-chat --server ""npx mcp-server-kubernetes""
```

Alternatively, pass it your existing Claude Desktop configuration file from above (Linux should pass the correct path to config):

Mac:

```shell
npx mcp-chat --config ""~/Library/Application Support/Claude/claude_desktop_config.json""
```

Windows:

```shell
npx mcp-chat --config ""%APPDATA%\Claude\claude_desktop_config.json""
```

## Features

- [x] Connect to a Kubernetes cluster
- [x] Unified kubectl API for managing resources
  - Get or list resources with `kubectl_get`
  - Describe resources with `kubectl_describe`
  - List resources with `kubectl_list`
  - Create resources with `kubectl_create`
  - Apply YAML manifests with `kubectl_apply`
  - Delete resources with `kubectl_delete`
  - Get logs with `kubectl_logs`
  - Manage kubectl contexts with `kubectl_context`
  - Explain Kubernetes resources with `explain_resource`
  - List API resources with `list_api_resources`
  - Scale resources with `kubectl_scale`
  - Update field(s) of a resource with `kubectl_patch`
  - Manage deployment rollouts with `kubectl_rollout`
  - Execute any kubectl command with `kubectl_generic`
- [x] Advanced operations
  - Scale deployments with `kubectl_scale` (replaces legacy `scale_deployment`)
  - Port forward to pods and services with `port_forward`
  - Run Helm operations
    - Install, upgrade, and uninstall charts
    - Support for custom values, repositories, and versions
- [x] Troubleshooting Prompt (`k8s-troubleshoot`)
  - Guides through a systematic Kubernetes troubleshooting flow for pods based on a keyword and optional namespace.
- [x] Non-destructive mode for read and create/update-only access to clusters

## Prompts

The MCP Kubernetes server includes specialized prompts to assist with common operations.

### k8s-troubleshoot Prompt

This prompt provides a systematic troubleshooting flow for Kubernetes pods. It accepts a `keyword` to identify relevant pods and an optional `namespace` to narrow the search.
The prompt's output will guide you through an autonomous troubleshooting flow, providing instructions for identifying issues, collecting evidence, and suggesting remediation steps.

## Local Development

Make sure that you have [bun installed](https://bun.sh/docs/installation). Clone the repo & install dependencies:

```bash
git clone https://github.com/Flux159/mcp-server-kubernetes.git
cd mcp-server-kubernetes
bun install
```

### Development Workflow

1. Start the server in development mode (watches for file changes):

```bash
bun run dev
```

2. Run unit tests:

```bash
bun run test
```

3. Build the project:

```bash
bun run build
```

4. Local Testing with [Inspector](https://github.com/modelcontextprotocol/inspector)

```bash
npx @modelcontextprotocol/inspector node dist/index.js
# Follow further instructions on terminal for Inspector link
```

5. Local testing with Claude Desktop

```json
{
  ""mcpServers"": {
    ""mcp-server-kubernetes"": {
      ""command"": ""node"",
      ""args"": [""/path/to/your/mcp-server-kubernetes/dist/index.js""]
    }
  }
}
```

6. Local testing with [mcp-chat](https://github.com/Flux159/mcp-chat)

```bash
bun run chat
```

## Contributing

See the [CONTRIBUTING.md](CONTRIBUTING.md) file for details.

## Advanced

### Non-Destructive Mode

You can run the server in a non-destructive mode that disables all destructive operations (delete pods, delete deployments, delete namespaces, etc.):

```shell
ALLOW_ONLY_NON_DESTRUCTIVE_TOOLS=true npx mcp-server-kubernetes
```

For Claude Desktop configuration with non-destructive mode:

```json
{
  ""mcpServers"": {
    ""kubernetes-readonly"": {
      ""command"": ""npx"",
      ""args"": [""mcp-server-kubernetes""],
      ""env"": {
        ""ALLOW_ONLY_NON_DESTRUCTIVE_TOOLS"": ""true""
      }
    }
  }
}
```

### Commands Available in Non-Destructive Mode

All read-only and resource creation/update operations remain available:

- Resource Information: `kubectl_get`, `kubectl_describe`, `kubectl_list`, `kubectl_logs`, `explain_resource`, `list_api_resources`
- Resource Creation/Modification: `kubectl_apply`, `kubectl_create`, `kubectl_scale`, `kubectl_patch`, `kubectl_rollout`
- Helm Operations: `install_helm_chart`, `upgrade_helm_chart`
- Connectivity: `port_forward`, `stop_port_forward`
- Context Management: `kubectl_context`

### Commands Disabled in Non-Destructive Mode

The following destructive operations are disabled:

- `kubectl_delete`: Deleting any Kubernetes resources
- `uninstall_helm_chart`: Uninstalling Helm charts
- `cleanup`: Cleanup of managed resources
- `kubectl_generic`: General kubectl command access (may include destructive operations)

For additional advanced features, see the [ADVANCED_README.md](ADVANCED_README.md).

## Architecture

See this [DeepWiki link](https://deepwiki.com/Flux159/mcp-server-kubernetes) for a more indepth architecture overview created by Devin.

This section describes the high-level architecture of the MCP Kubernetes server.

### Request Flow

The sequence diagrahttps://github.com/Gaffx/volatility-mcp,Gaffx/volatility-mcp,"![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)
<p align=""center"">
<img src=""assets/logo.png"" height=""300"">
</p>
<h1 align=""center"">
Your AI Assistant in Memory Forensics
</h1>

## Overview
Volatility MCP seamlessly integrates Volatility 3's powerful memory analysis with FastAPI and the Model Context Protocol (MCP). Experience memory forensics without barriers as plugins like `pslist` and `netscan` become accessible through clean REST APIs, connecting memory artifacts directly to AI assistants and web applications

## Features
* **Volatility 3 Integration:** Leverages the Volatility 3 framework for memory image analysis.
* **FastAPI Backend:** Provides RESTful APIs to interact with Volatility plugins.
* **Web Front End Support (future feature):** Designed to connect with a web-based front end for interactive analysis.
* **Model Context Protocol (MCP):** Enables standardized communication with MCP clients like Claude Desktop.
* **Plugin Support:** Supports various Volatility plugins, including `pslist` for process listing and `netscan` for network connection analysis.


## Architecture

The project architecture consists of the following components:

* **MCP Client:** MCP client like Claude Desktop that interacts with the FastAPI backend.
* **FastAPI Server:** A Python-based server that exposes Volatility plugins as API endpoints.
* **Volatility 3:** The memory forensics framework performing the analysis.

This architecture allows users to analyze memory images through MCP clients like Claude Desktop. Users can use natural language prompts to perform memory forensics analysis such as
show me the list of the processes in memory image x, or show me all the external connections made

## Getting Started

### Prerequisites

* Python 3.7+ installed on your system
* Volatility 3 binary installed (see [Volatility 3 Installation Guide](https://github.com/volatilityfoundation/volatility3?tab=readme-ov-file#installing)) and added to your env path called **VOLATILITY_BIN**

### Installation

1. Clone the repository:

    ```
    git clone <repository_url>
    cd <repository_directory>
    ```

2. Install the required Python dependencies:

    ```
    pip install -r requirements.txt
    ```

3. Start the FastAPI server to expose Volatility 3 APIs:

    ```
    uvicorn volatility_fastapi_server:app 
    ```
4. Install Claude Desktop (see [Claude Desktop](https://claude.ai/download)
5. To configure Claude Desktop as a volatility MCP client, navigate to Claude → Settings → Developer → Edit Config, locate the claude_desktop_config.json file, and insert the following configuration details
6. Please note that the `-i` option in the config.json file specifies the directory path of your memory image file.

   ```
       {
        ""mcpServers"": {
          ""vol"": {
            ""command"": ""python"",
            ""args"": [
              ""/ABSOLUTE_PATH_TO_MCP-SERVER/vol_mcp_server.py"", ""-i"",     
              ""/ABSOLUTE_PATH_TO_MEMORY_IMAGE/<memory_image>""
            ]
          }
        }
    }
   ```
Alternatively, update this file directly:

`/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json`

### Usage

1. Start the FastAPI server as described above.
2. Connect an MCP client (e.g., Claude Desktop) to the FastAPI server.
3. Start the prompt by asking questions regarding the memory image in scope, such as showing me the running processes, creating a tree relationship graph for process x, or showing me all external RFC1918 connections.

![image](https://github.com/user-attachments/assets/23f6fd4f-76b4-4255-a0a6-534ed3459bb3)
![image](https://github.com/user-attachments/assets/e5cd74ae-72ff-4c5b-8bd8-fbeb1348https://github.com/Gaffx/volatility-mcp,Gaffx/volatility-mcp,"![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)
<p align=""center"">
<img src=""assets/logo.png"" height=""300"">
</p>
<h1 align=""center"">
Your AI Assistant in Memory Forensics
</h1>

## Overview
Volatility MCP seamlessly integrates Volatility 3's powerful memory analysis with FastAPI and the Model Context Protocol (MCP). Experience memory forensics without barriers as plugins like `pslist` and `netscan` become accessible through clean REST APIs, connecting memory artifacts directly to AI assistants and web applications

## Features
* **Volatility 3 Integration:** Leverages the Volatility 3 framework for memory image analysis.
* **FastAPI Backend:** Provides RESTful APIs to interact with Volatility plugins.
* **Web Front End Support (future feature):** Designed to connect with a web-based front end for interactive analysis.
* **Model Context Protocol (MCP):** Enables standardized communication with MCP clients like Claude Desktop.
* **Plugin Support:** Supports various Volatility plugins, including `pslist` for process listing and `netscan` for network connection analysis.


## Architecture

The project architecture consists of the following components:

* **MCP Client:** MCP client like Claude Desktop that interacts with the FastAPI backend.
* **FastAPI Server:** A Python-based server that exposes Volatility plugins as API endpoints.
* **Volatility 3:** The memory forensics framework performing the analysis.

This architecture allows users to analyze memory images through MCP clients like Claude Desktop. Users can use natural language prompts to perform memory forensics analysis such as
show me the list of the processes in memory image x, or show me all the external connections made

## Getting Started

### Prerequisites

* Python 3.7+ installed on your system
* Volatility 3 binary installed (see [Volatility 3 Installation Guide](https://github.com/volatilityfoundation/volatility3?tab=readme-ov-file#installing)) and added to your env path called **VOLATILITY_BIN**

### Installation

1. Clone the repository:

    ```
    git clone <repository_url>
    cd <repository_directory>
    ```

2. Install the required Python dependencies:

    ```
    pip install -r requirements.txt
    ```

3. Start the FastAPI server to expose Volatility 3 APIs:

    ```
    uvicorn volatility_fastapi_server:app 
    ```
4. Install Claude Desktop (see [Claude Desktop](https://claude.ai/download)
5. To configure Claude Desktop as a volatility MCP client, navigate to Claude → Settings → Developer → Edit Config, locate the claude_desktop_config.json file, and insert the following configuration details
6. Please note that the `-i` option in the config.json file specifies the directory path of your memory image file.

   ```
       {
        ""mcpServers"": {
          ""vol"": {
            ""command"": ""python"",
            ""args"": [
              ""/ABSOLUTE_PATH_TO_MCP-SERVER/vol_mcp_server.py"", ""-i"",     
              ""/ABSOLUTE_PATH_TO_MEMORY_IMAGE/<memory_image>""
            ]
          }
        }
    }
   ```
Alternatively, update this file directly:

`/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json`

### Usage

1. Start the FastAPI server as described above.
2. Connect an MCP client (e.g., Claude Desktop) to the FastAPI server.
3. Start the prompt by asking questions regarding the memory image in scope, such as showing me the running processes, creating a tree relationship graph for process x, or showing me all external RFC1918 connections.

![image](https://github.com/user-attachments/assets/23f6fd4f-76b4-4255-a0a6-534ed3459bb3)
![image](https://github.com/user-attachments/assets/e5cd74ae-72ff-4c5b-8bd8-fbeb13488a70)
![image](https://github.com/user-attachments/assets/779707ef-4910-4503-b6b0-43f6c37075ef)
![image](https://github.com/user-attachments/assets/668e9b91-463a-424f-a3ef-ee2baf44308d)

## Future Features and Enhancements

*  **Native Volatility Python Integration:** Incorporate Volatility Python SDK directly in the code base as opposed to subprocess volatility binary
*   **Yara Integration:** Implement functionality to dump a process from memory and scan it with Yara rules for malware analysis.
*   **Multi-Image Analysis:** Enable the analysis of multiple memory images simultaneously to correlate events and identify patterns across different systems.
*   **Adding more Volatility Plugins:** add more volatility plugins to expand the scope of memory analysis
*   **GUI Enhancements:** Develop a user-friendly web interface for interactive memory analysis and visualization.
*   **Automated Report Generation:** Automate the generation of detailed reports summarizing the findings of memory analysis.
*   **Advanced Threat Detection:** Incorporate advanced techniques for detecting sophisticated threats and anomalies in memory.

## Contributing

Contributions are welcome! Please follow these steps to contribute:

1. Fork this repository.
2. Create a new branch (`git checkout -b feature/my-feature`).
3. Commit your changes (`git commit -m 'Add some feature'`).
4. Push to your branch (`git push origin feature/my-feature`).
5. Open a pull request.

[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/gaffx-volatility-mcp-badge.png)](https://mseep.ai/app/gaffx-volatility-mcp)


","Star
 19",2025-06-24T08:43:21.243989,volatility-mcp - MCP Server | Model Context Protocol Integration,"![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)
<p align=""center"">
<img src=""assets/logo.png"" height=""300"">
</p>
<h...","['mcp server', 'model context protocol', 'ai integration', 'volatility-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'volatility-mcp', 'description': '![](https://img.shields.io/badge/License-Apache%202.0-blue?style=plastic&logo=adobefonts)\n<p align=""center"">\n<img src=""assets/logo.png"" height=""300"">\n</p>\n<h...', 'url': 'https://github.com/Gaffx/volatility-mcp', 'codeRepository': 'https://github.com/Gaffx/volatility-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,API Integration,Image Processing,pip install -r requirements.txt,"Installation, Prerequisites, Usage","""vol"": {
            ""command"": ""python"",
            ""args"": [
              ""/ABSOLUTE_PATH_TO_MCP-SERVER/vol_mcp_server.py"", ""-i"",     
              ""/ABSOLUTE_PATH_TO_MEMORY_IMAGE/<memory_image>""
            ]","-r, requirements.txt",Apache License 2.0,2025-06-23,Gaffx,"<img src=""assets/logo.png"" height=""300"">"
https://github.com/kiwamizamurai/mcp-kibela-server,kiwamizamurai/mcp-kibela-server,"# Kibela MCP Server
![NPM Version](https://img.shields.io/npm/v/%40kiwamizamurai%2Fmcp-kibela-server)
![NPM Downloads](https://img.shields.io/npm/dm/%40kiwamizamurai%2Fmcp-kibela-server)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![smithery badge](https://smithery.ai/badge/@kiwamizamurai/mcp-kibela-server)](https://smithery.ai/server/@kiwamizamurai/mcp-kibela-server)
[![Build and Push Docker Image](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/docker.yml/badge.svg?branch=main)](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/docker.yml)
[![Lint](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/lint.yml/badge.svg?branch=main)](https://github.com/kiwamizamurai/mcp-kibela-server/actions/workflows/lint.yml)

MCP server implementation for Kibela API integration, enabling LLMs to interact with Kibela content.

<img width=""320"" alt=""Example"" src=""https://github.com/user-attachments/assets/eeed8f45-eb24-456d-bb70-9e738aa1bfb3"" />

<a href=""https://glama.ai/mcp/servers/m21nkeig1p""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/m21nkeig1p/badge"" alt=""Kibela Server MCP server"" /></a>

## Features

- Search notes with advanced filters
- Get your latest notes
- Get note content and comments
- Manage groups and folders
- Like/unlike notes
- List users
- View note attachments
- View recently viewed notes
- Get notes by path

## Configuration

### Environment Variables

- `KIBELA_TEAM`: Your Kibela team name (required)
- `KIBELA_TOKEN`: Your Kibela API token (required)

## Cursor Integration

Add to your `~/.cursor/mcp.json`:

```json
{
    ""mcpServers"": {
        ""kibela"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@kiwamizamurai/mcp-kibela-server""],
            ""env"": {
                ""KIBELA_TEAM"": ""YOUR_TEAM_NAME"",
                ""KIBELA_TOKEN"": ""YOUR_TOKEN""
            }
        }
    }
}
```

If you want to use docker instead

```json
{
    ""mcpServers"": {
        ""kibela"": {
            ""command"": ""docker"",
            ""args"": [
                ""run"",
                ""-i"",
                ""--rm"",
                ""-e"",
                ""KIBELA_TEAM"",
                ""-e"",
                ""KIBELA_TOKEN"",
                ""ghcr.io/kiwamizamurai/mcp-kibela-server:latest""
            ],
            ""env"": {
                ""KIBELA_TEAM"": ""YOUR_TEAM_NAME"",
                ""KIBELA_TOKEN"": ""YOUR_TOKEN""
            }
        }
    }
}
```

## Tools

### kibela_search_notes
Search Kibela notes with given query
- Input:
  - `query` (string): Search query
  - `coediting` (boolean, optional): Filter by co-editing status
  - `isArchived` (boolean, optional): Filter by archive status
  - `sortBy` (string, optional): Sort by (RELEVANT, CONTENT_UPDATED_AT)
  - `userIds` (string[], optional): Filter by user IDs
  - `folderIds` (string[], optional): Filter by folder IDs
- Returns: List of matching notes with ID, title, URL, author, groups and more

### kibela_get_my_notes
Get your latest notes from Kibela
- Input:
  - `limit` (number, optional): Number of notes to fetch (default: 15)
- Returns: List of your latest notes with author information

### kibela_get_note_content
Get content and comments of a specific note
- Input:
  - `id` (string): Note ID
  - `include_image_data` (boolean, optional): Whether to include image data URLs in the response (default: false)
- Returns: Full note content including HTML, comments, attachments, groups, folders and more

### kibela_get_groups
Get list of accessible groups
- Input: None
- Returns: List of groups with details like privacy settings and permissions

### kibela_get_group_folders
Get folders in a group
- Input:
  - `groupId` (string): Group ID
  - `parentFolderId` (string, optional): Parent folder ID for nested folders
- Returns: List of folders with their notes and metadata

### kibela_get_group_notes
Get notes in a group that are not attached to any folder
- Input:
  - `groupId` (string): Group ID
- Returns: List of notes with author information, sorted by last update time

### kibela_get_folder_notes
Get notes in a folder
- Input:
  - `folderId` (string): Folder ID
  - `limit` (number, optional): Number of notes to fetch (default: 100)
- Returns: List of notes with author information, sorted by last update time

### kibela_get_users
Get list of users
- Input: None
- Returns: List of users with ID, account and real name

### kibela_like_note
Like a note
- Input:
  - `noteId` (shttps://github.com/ananddtyagi/webpage-screenshot-mcp,ananddtyagi/webpage-screenshot-mcp,"# Webpage Screenshot MCP Server

An MCP (Model Context Protocol) server that captures screenshots of web pages using Puppeteer. This server allows AI agents to visually verify web applications and see their progress when generating web apps.

![Screen Recording May 27 2025 (2)](https://github.com/user-attachments/assets/9f186ec4-5a5c-449b-9a30-a5ec0cdba695)


## Features

- **Full page screenshots**: Capture entire web pages or just the viewport
- **Element screenshots**: Target specific elements using CSS selectors
- **Multiple formats**: Support for PNG, JPEG, and WebP formats
- **Customizable options**: Set viewport size, image quality, wait conditions, and delays
- **Base64 encoding**: Returns screenshots as base64 encoded images for easy integration
- **Authentication support**: Manual login and cookie persistence
- **Default browser integration**: Use your system's default browser for a more natural experience
- **Session persistence**: Keep browser sessions open for multi-step workflows

## Installation

To install and build the MCP:

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/ananddtyagi/webpage-screenshot-mcp.git
cd webpage-screenshot-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

The MCP server is built using TypeScript and compiled to JavaScript. The `dist` folder contains the compiled JavaScript files. 

### Adding to Claude or Cursor

To add this MCP to Claude Desktop or Cursor:

1. **Claude Desktop**:
   - Go to Settings > Developer
   - Click ""Edit Config""
   - Add the following:

   ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [
        ""~/path/to/webpage-screenshot-mcp/dist/index.js""
      ]
    }
   ```
   - Save and reload Claude

2. **Cursor**:
   - Open Cursor and go to Cursor Settings > MCP
   - Click ""Add new global MCP server""
   - Add the following:
  
  ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [""~/path/to/webpage-screenshot-mcp/dist/index.js""]
    }
   ```

   - Save and reload Cursor

## Usage

### Tools

This MCP server provides several tools:

#### 1. login-and-wait

Opens a webpage in a visible browser window for manual login, waits for user to complete login, then saves cookies.

```json
{
  ""url"": ""https://example.com/login"",
  ""waitMinutes"": 5,
  ""successIndicator"": "".dashboard-welcome"",
  ""useDefaultBrowser"": true
}
```

- `url` (required): The URL of the login page
- `waitMinutes` (optional): Maximum minutes to wait for login (default: 5)
- `successIndicator` (optional): CSS selector or URL pattern that indicates successful login
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: true)

#### 2. screenshot-page

Captures a screenshot of a given URL and returns it as base64 encoded image.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""fullPage"": true,
  ""width"": 1920,
  ""height"": 1080,
  ""format"": ""png"",
  ""quality"": 80,
  ""waitFor"": ""networkidle2"",
  ""delay"": 500,
  ""useSavedAuth"": true,
  ""reuseAuthPage"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage to screenshot
- `fullPage` (optional): Whether to capture the full page or just the viewport (default: true)
- `width` (optional): Viewport width in pixels (default: 1920)
- `height` (optional): Viewport height in pixels (default: 1080)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `waitFor` (optional): When to consider page loaded - ""lhttps://github.com/ananddtyagi/webpage-screenshot-mcp,ananddtyagi/webpage-screenshot-mcp,"# Webpage Screenshot MCP Server

An MCP (Model Context Protocol) server that captures screenshots of web pages using Puppeteer. This server allows AI agents to visually verify web applications and see their progress when generating web apps.

![Screen Recording May 27 2025 (2)](https://github.com/user-attachments/assets/9f186ec4-5a5c-449b-9a30-a5ec0cdba695)


## Features

- **Full page screenshots**: Capture entire web pages or just the viewport
- **Element screenshots**: Target specific elements using CSS selectors
- **Multiple formats**: Support for PNG, JPEG, and WebP formats
- **Customizable options**: Set viewport size, image quality, wait conditions, and delays
- **Base64 encoding**: Returns screenshots as base64 encoded images for easy integration
- **Authentication support**: Manual login and cookie persistence
- **Default browser integration**: Use your system's default browser for a more natural experience
- **Session persistence**: Keep browser sessions open for multi-step workflows

## Installation

To install and build the MCP:

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/ananddtyagi/webpage-screenshot-mcp.git
cd webpage-screenshot-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

The MCP server is built using TypeScript and compiled to JavaScript. The `dist` folder contains the compiled JavaScript files. 

### Adding to Claude or Cursor

To add this MCP to Claude Desktop or Cursor:

1. **Claude Desktop**:
   - Go to Settings > Developer
   - Click ""Edit Config""
   - Add the following:

   ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [
        ""~/path/to/webpage-screenshot-mcp/dist/index.js""
      ]
    }
   ```
   - Save and reload Claude

2. **Cursor**:
   - Open Cursor and go to Cursor Settings > MCP
   - Click ""Add new global MCP server""
   - Add the following:
  
  ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [""~/path/to/webpage-screenshot-mcp/dist/index.js""]
    }
   ```

   - Save and reload Cursor

## Usage

### Tools

This MCP server provides several tools:

#### 1. login-and-wait

Opens a webpage in a visible browser window for manual login, waits for user to complete login, then saves cookies.

```json
{
  ""url"": ""https://example.com/login"",
  ""waitMinutes"": 5,
  ""successIndicator"": "".dashboard-welcome"",
  ""useDefaultBrowser"": true
}
```

- `url` (required): The URL of the login page
- `waitMinutes` (optional): Maximum minutes to wait for login (default: 5)
- `successIndicator` (optional): CSS selector or URL pattern that indicates successful login
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: true)

#### 2. screenshot-page

Captures a screenshot of a given URL and returns it as base64 encoded image.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""fullPage"": true,
  ""width"": 1920,
  ""height"": 1080,
  ""format"": ""png"",
  ""quality"": 80,
  ""waitFor"": ""networkidle2"",
  ""delay"": 500,
  ""useSavedAuth"": true,
  ""reuseAuthPage"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage to screenshot
- `fullPage` (optional): Whether to capture the full page or just the viewport (default: true)
- `width` (optional): Viewport width in pixels (default: 1920)
- `height` (optional): Viewport height in pixels (default: 1080)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `waitFor` (optional): When to consider page loaded - ""load"", ""domcontentloaded"", ""networkidle0"", or ""networkidle2"" (default: ""networkidle2"")
- `delay` (optional): Additional delay in milliseconds after page load (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `reuseAuthPage` (optional): Whether to use the existing authenticated page (default: false)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 3. screenshot-element

Captures a screenshot of a specific element on a webpage using a CSS selector.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""selector"": "".user-profile"",
  ""waitForSelector"": true,
  ""format"": ""png"",
  ""quality"": 80,
  ""padding"": 10,
  ""useSavedAuth"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage
- `selector` (required): CSS selector for the element to screenshot
- `waitForSelector` (optional): Whether to wait for the selector to appear (default: true)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `padding` (optional): Padding around the element in pixels (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 4. clear-auth-cookies

Clears saved authentication cookies for a specific domain or all domains.

```json
{
  ""url"": ""https://example.com""
}
```

- `url` (optional): URL of the domain to clear cookies for. If not provided, clears all cookies.

## Default Browser Mode

The default browser mode allows you to use your system's regular browser (Chrome, Edge, etc.) instead of Puppeteer's bundled Chromium. This is useful for:

1. Using your existing browser sessions and extensions
2. Manually logging in to websites with your saved credentials
3. Having a more natural browsing experience for multi-step workflows
4. Testing with the same browser environment as your users

To enable default browser mode, set `useDefaultBrowser: true` and `visibleBrowser: true` in your tool parameters.

### How Default Browser Mode Works

When you enable default browser mode:

1. The tool will attempt to locate your system's default browser (Chrome, Edge, etc.)
2. It launches your browser with remote debugging enabled on a random port
3. Puppeteer connects to this browser instance instead of launching its own
4. Your existing profiles, extensions, and cookies are available during the session
5. The browser window remains visible so you can interact with it manually

This mode is particularly useful for workflows that require authentication or complex user interactions.

## Browser Persistence

The MCP server can maintain a persistent browser session across multiple tool calls:

1. When you use `login-and-wait`, the browser session is kept open
2. Subsequent calls to `screenshot-page` or `screenshot-element` with `reuseAuthPage: true` will use the same page
3. This allows for multi-step workflows without having to re-authenticate

## Cookie Management

Cookies are automatically saved for each domain you visit:

1. After using `login-and-wait`, cookies are saved to the `.mcp-screenshot-cookies` directory in your home folder
2. These cookies are automatically loaded when visiting the same domain again with `useSavedAuth: true`
3. You can clear cookies using the `clear-auth-cookies` tool

## Example Workflow: Protected Page Screenshots

Here's an example workflow for taking screenshots of pages that require authentication:

1. **Manual Login Phase**

```json
{
  ""name"": ""login-and-wait"",
  ""parameters"": {
    ""url"": ""https://example.com/login"",
    ""waitMinutes"": 3,
    ""successIndicator"": "".dashboard-welcome"",
    ""useDefaultBrowser"": true
  }
}
```

This will open your default browser with the login page. You can manually log in, and once complete (either by detecting the success indicator or after navigating away from the login page), the session cookies will be saved.

2. **Take Screenshots Using Saved Session**

```json
{
  ""name"": ""screenshhttps://github.com/JoshuaRileyDev/simulator-mcp-server,JoshuaRileyDev/simulator-mcp-server,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized interface.

## Features

- List available iOS simulators
- Boot and shutdown simulators
- Install .app bundles on simulators
- Launch installed apps by bundle ID

## Installation
Add the following to your Claude Config JSON file
```
{
  ""mcpServers"": {
    ""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]
    }
  }
}
```","Star
 43",2025-06-24T08:43:21.243970,simulator-mcp-server - MCP Server | Model Context Protocol Integration,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...","['mcp server', 'model context protocol', 'ai integration', 'simulator-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'simulator-mcp-server', 'description': '# iOS Simulator MCP Server\n\nA Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...', 'url': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'codeRepository': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,AI Tools,Text Processing,,,"""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]",,,,JoshuaRileyDev,A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized inte...
https://github.com/zcaceres/gtasks-mcp,zcaceres/gtasks-mcp,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcaceres/gtasks)

This MCP server integrates with Google Tasks to allow listing, reading, searching, creating, updating, and deleting tasks.

## Components

### Tools

- **search**
  - Search for tasks in Google Tasks
  - Input: `query` (string): Search query
  - Returns matching tasks with details

- **list**
  - List all tasks in Google Tasks
  - Optional input: `cursor` (string): Cursor for pagination
  - Returns a list of all tasks

- **create**
  - Create a new task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `title` (string, required): Task title
    - `notes` (string, optional): Task notes
    - `due` (string, optional): Due date
  - Returns confirmation of task creation

- **update**
  - Update an existing task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `id` (string, required): Task ID
    - `uri` (string, required): Task URI
    - `title` (string, optional): New task title
    - `notes` (string, optional): New task notes
    - `status` (string, optional): New task status (""needsAction"" or ""completed"")
    - `due` (string, optional): New due date
  - Returns confirmation of task update

- **delete**
  - Delete a task in Google Tasks
  - Input:
    - `taskListId` (string, required): Task list ID
    - `id` (string, required): Task ID
  - Returns confirmation of task deletion

- **clear**
  - Clear completed tasks from a Google Tasks task list
  - Input: `taskListId` (string, required): Task list ID
  - Returns confirmation of cleared tasks

### Resources

The server provides access to Google Tasks resources:https://github.com/JoshuaRileyDev/simulator-mcp-server,JoshuaRileyDev/simulator-mcp-server,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized interface.

## Features

- List available iOS simulators
- Boot and shutdown simulators
- Install .app bundles on simulators
- Launch installed apps by bundle ID

## Installation
Add the following to your Claude Config JSON file
```
{
  ""mcpServers"": {
    ""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]
    }
  }
}
```","Star
 43",2025-06-24T08:43:21.243970,simulator-mcp-server - MCP Server | Model Context Protocol Integration,"# iOS Simulator MCP Server

A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...","['mcp server', 'model context protocol', 'ai integration', 'simulator-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'simulator-mcp-server', 'description': '# iOS Simulator MCP Server\n\nA Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP spec...', 'url': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'codeRepository': 'https://github.com/JoshuaRileyDev/simulator-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,AI Tools,Text Processing,,,"""simulator"": {
      ""command"": ""npx"",
      ""args"": [
        ""y"",
        ""@joshuarileydev/simulator-mcp-server""
      ]",,,,JoshuaRileyDev,A Model Context Protocol (MCP) server that provides programmatic control over iOS simulators. This server implements the MCP specification to expose simulator functionality through a standardized inte...
https://github.com/zcaceres/gtasks-mcp,zcaceres/gtasks-mcp,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcaceres/gtasks)

This MCP server integrates with Google Tasks to allow listing, reading, searching, creating, updating, and deleting tasks.

## Components

### Tools

- **search**
  - Search for tasks in Google Tasks
  - Input: `query` (string): Search query
  - Returns matching tasks with details

- **list**
  - List all tasks in Google Tasks
  - Optional input: `cursor` (string): Cursor for pagination
  - Returns a list of all tasks

- **create**
  - Create a new task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `title` (string, required): Task title
    - `notes` (string, optional): Task notes
    - `due` (string, optional): Due date
  - Returns confirmation of task creation

- **update**
  - Update an existing task in Google Tasks
  - Input:
    - `taskListId` (string, optional): Task list ID
    - `id` (string, required): Task ID
    - `uri` (string, required): Task URI
    - `title` (string, optional): New task title
    - `notes` (string, optional): New task notes
    - `status` (string, optional): New task status (""needsAction"" or ""completed"")
    - `due` (string, optional): New due date
  - Returns confirmation of task update

- **delete**
  - Delete a task in Google Tasks
  - Input:
    - `taskListId` (string, required): Task list ID
    - `id` (string, required): Task ID
  - Returns confirmation of task deletion

- **clear**
  - Clear completed tasks from a Google Tasks task list
  - Input: `taskListId` (string, required): Task list ID
  - Returns confirmation of cleared tasks

### Resources

The server provides access to Google Tasks resources:

- **Tasks** (`gtasks:///<task_id>`)
  - Represents individual tasks in Google Tasks
  - Supports reading task details including title, status, due date, notes, and other metadata
  - Can be listed, read, created, updated, and deleted using the provided tools

## Getting started

1. [Create a new Google Cloud project](https://console.cloud.google.com/projectcreate)
2. [Enable the Google Tasks API](https://console.cloud.google.com/workspace-api/products)
3. [Configure an OAuth consent screen](https://console.cloud.google.com/apis/credentials/consent) (""internal"" is fine for testing)
4. Add scopes `https://www.googleapis.com/auth/tasks`
5. [Create an OAuth Client ID](https://console.cloud.google.com/apis/credentials/oauthclient) for application type ""Desktop App""
6. Download the JSON file of your client's OAuth keys
7. Rename the key file to `gcp-oauth.keys.json` and place into the root of this repo (i.e. `gcp-oauth.keys.json`)

Make sure to build the server with either `npm run build` or `npm run watch`.

### Installing via Smithery

To install Google Tasks Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@zcaceres/gtasks):

```bash
npx -y @smithery/cli install @zcaceres/gtasks --client claude
```

### Authentication

To authenticate and save credentials:

1. Run the server with the `auth` argument: `npm run start auth`
2. This will open an authentication flow in your system browser
3. Complete the authentication process
4. Credentials will be saved in the root of this repo (i.e. `.gdrive-server-credentials.json`)

### Usage with Desktop App

To integrate this server with the desktop app, add the following to your app's server configuration:

```json
{
  ""mcpServers"": {
    ""gtasks"": {
      ""command"": ""/opt/homebrew/bin/node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}
```
","Star
 63",2025-06-24T08:43:21.243964,gtasks-mcp - MCP Server | Model Context Protocol Integration,"# Google Tasks MCP Server

![gtasks mcp logo](./logo.jpg)
[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcacere...","['mcp server', 'model context protocol', 'ai integration', 'gtasks-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'gtasks-mcp', 'description': '# Google Tasks MCP Server\n\n![gtasks mcp logo](./logo.jpg)\n[![smithery badge](https://smithery.ai/badge/@zcaceres/gtasks)](https://smithery.ai/server/@zcacere...', 'url': 'https://github.com/zcaceres/gtasks-mcp', 'codeRepository': 'https://github.com/zcaceres/gtasks-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,Automation,Search Engine,,"Authentication, Resources, Tools","{
  ""mcpServers"": {
    ""gtasks"": {
      ""command"": ""/opt/homebrew/bin/node"",
      ""args"": [
        ""{ABSOLUTE PATH TO FILE HERE}/dist/index.js""
      ]
    }
  }
}",,MIT License,2025-06-21,zcaceres,"This MCP server integrates with Google Tasks to allow listing, reading, searching, creating, updating, and deleting tasks."
https://github.com/hbg/mcp-paperswithcode,hbg/mcp-paperswithcode,"# mcp-paperswithcode

[![smithery badge](https://smithery.ai/badge/@hbg/mcp-paperswithcode)](https://smithery.ai/server/@hbg/mcp-paperswithcode)

# 🦾 Features

> Allows AI assistants to find and read papers, as well as view related code repositories for further context.

This MCP server provides a Model Context Protocol (MCP) client that interfaces with the PapersWithCode API.

It includes tools for searching, retrieving, and parsing information on research papers, authors, datasets, conferences, and more.

# 🚀 Getting Starthttps://github.com/rad-security/mcp-server,rad-security/mcp-server,"# RAD Security MCP Server

A Model Context Protocol (MCP) server for RAD Security, providing AI-powered security insights for Kubernetes and cloud environments.

## Installation

```bash
npm install @rad-security/mcp-server
```

## Usage

### Prerequisites

- Node.js 20.x or higher

### Environment Variables

The following environment are required required to use the MCP server with Rad Security:

```bash
RAD_SECURITY_ACCESS_KEY_ID=""your_access_key""
RAD_SECURITY_SECRET_KEY=""your_secret_key""
RAD_SECURITY_ACCOUNT_ID=""your_account_id""
```

but you can also use few operations without authentication:

- List CVEs
- Get details of a specific CVE
- Get latest 30 CVEs
- List Kubernetes resource misconfiguration policies

### In cursor IDE

It's quite problematic to set ENV variables in cursor IDE.

So, you can use the following start.sh script to start the server.

```bash
./start.sh
```

Please set the ENV variables in the start.sh script first!

### In Claude Desktop

You can use the following config to start the server in Claude Desktop.

```json
{
  ""mcpServers"": {
    ""rad-security"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@rad-security/mcp-server""],
      ""env"": {
        ""RAD_SECURITY_ACCESS_KEY_ID"": ""<your-access-key-id>"",
        ""RAD_SECURITY_SECRET_KEY"": ""<your-secret-key>"",
        ""RAD_SECURITY_ACCOUNT_ID"": ""<your-account-id>""
      }
    }
  }
```

### As a Docker Container - with Streamable HTTP

```bash
docker build -t rad-security/mcp-server .
docker run \
  -e TRANSPORT_TYPE=streamable \
  -e RAD_SECURITY_ACCESS_KEY_ID=your_access_key \
  -e RAD_SECURITY_SECRET_KEY=your_secret_key \
  -e RAD_SECURITY_ACCOUNT_ID=your_account_id \
  -p 3000:3000 \
  rad-security/mcp-server
```

### As a Docker Container - with SSE (deprecated)

*Note:* The SSE transport is now deprecated in favor of Streamable HTTP. It's still supported for backward compatibility, but it's recommended to use Streamable HTTP instead.

```bash
docker build -t rad-security/mcp-server .
docker run \
  -e TRANSPORT_TYPE=sse \
  -e RAD_SECURITY_ACCESS_KEY_ID=your_access_key \
  -e RAD_SECURITY_SECRET_KEY=your_secret_key \
  -e RAD_SECURITY_ACCOUNT_ID=your_account_id \
  -p 3000:3000 \
  rad-security/mcp-server
```

## Features

- Account Inventory
  - List clusters and their details*

- Containers Inventory
  - List containers and their details*

- Security Findings
  - List and analyze security findings*

- Runtime Security
  - Get process trees of running containers*
  - Get runtime baselines of running containers*
  - Analyze process behavior of running containers*

- Network Security
  - Monitor HTTP requests*
  - Track network connections*
  - Analyze network patterns*

- Identity and Access
  - List identities*
  - Get identity details*

- Audit
  - List who shelled into a pod*

- Cloud Security
  - List and monitor cloud resources*
  - Get resource details and compliance status*

- Images
  - Get SBOMs*
  - List images and their vulnerabilities*
  - Get top vulnerable images*

- Kubernetes Objects
  - Get details of a specific Kubernetes resource*
  - List Kubernetes resources*
  - List Kubernetes resource misconfiguration policies*

- Threat Vector
  - List threat vectors*
  - Get details of a specific threat vector*

- CVEs
  - List CVEs
  - Get details of a specific CVE
  - Get latest 30 CVEs

`*` - requires authentication and account in Rad Security.

## Development

```bash
# Install dependencies
npm install

# Run type checking
npm run type-check

# Run linter
npm run lint

# Build
npm run build
```

## License

MIT License - see the [LICENSE](LICENSE) file for details
","Star
 3",2025-06-24T08:43:21.243953,mcp-server - MCP Server | Model Context Protocolhttps://github.com/rad-security/mcp-server,rad-security/mcp-server,"# RAD Security MCP Server

A Model Context Protocol (MCP) server for RAD Security, providing AI-powered security insights for Kubernetes and cloud environments.

## Installation

```bash
npm install @rad-security/mcp-server
```

## Usage

### Prerequisites

- Node.js 20.x or higher

### Environment Variables

The following environment are required required to use the MCP server with Rad Security:

```bash
RAD_SECURITY_ACCESS_KEY_ID=""your_access_key""
RAD_SECURITY_SECRET_KEY=""your_secret_key""
RAD_SECURITY_ACCOUNT_ID=""your_account_id""
```

but you can also use few operations without authentication:

- List CVEs
- Get details of a specific CVE
- Get latest 30 CVEs
- List Kubernetes resource misconfiguration policies

### In cursor IDE

It's quite problematic to set ENV variables in cursor IDE.

So, you can use the following start.sh script to start the server.

```bash
./start.sh
```

Please set the ENV variables in the start.sh script first!

### In Claude Desktop

You can use the following config to start the server in Claude Desktop.

```json
{
  ""mcpServers"": {
    ""rad-security"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@rad-security/mcp-server""],
      ""env"": {
        ""RAD_SECURITY_ACCESS_KEY_ID"": ""<your-access-key-id>"",
        ""RAD_SECURITY_SECRET_KEY"": ""<your-secret-key>"",
        ""RAD_SECURITY_ACCOUNT_ID"": ""<your-account-id>""
      }
    }
  }
```

### As a Docker Container - with Streamable HTTP

```bash
docker build -t rad-security/mcp-server .
docker run \
  -e TRANSPORT_TYPE=streamable \
  -e RAD_SECURITY_ACCESS_KEY_ID=your_access_key \
  -e RAD_SECURITY_SECRET_KEY=your_secret_key \
  -e RAD_SECURITY_ACCOUNT_ID=your_account_id \
  -p 3000:3000 \
  rad-security/mcp-server
```

### As a Docker Container - with SSE (deprecated)

*Note:* The SSE transport is now deprecated in favor of Streamable HTTP. It's still supported for backward compatibility, but it's recommended to use Streamable HTTP instead.

```bash
docker build -t rad-security/mcp-server .
docker run \
  -e TRANSPORT_TYPE=sse \
  -e RAD_SECURITY_ACCESS_KEY_ID=your_access_key \
  -e RAD_SECURITY_SECRET_KEY=your_secret_key \
  -e RAD_SECURITY_ACCOUNT_ID=your_account_id \
  -p 3000:3000 \
  rad-security/mcp-server
```

## Features

- Account Inventory
  - List clusters and their details*

- Containers Inventory
  - List containers and their details*

- Security Findings
  - List and analyze security findings*

- Runtime Security
  - Get process trees of running containers*
  - Get runtime baselines of running containers*
  - Analyze process behavior of running containers*

- Network Security
  - Monitor HTTP requests*
  - Track network connections*
  - Analyze network patterns*

- Identity and Access
  - List identities*
  - Get identity details*

- Audit
  - List who shelled into a pod*

- Cloud Security
  - List and monitor cloud resources*
  - Get resource details and compliance status*

- Images
  - Get SBOMs*
  - List images and their vulnerabilities*
  - Get top vulnerable images*

- Kubernetes Objects
  - Get details of a specific Kubernetes resource*
  - List Kubernetes resources*
  - List Kubernetes resource misconfiguration policies*

- Threat Vector
  - List threat vectors*
  - Get details of a specific threat vector*

- CVEs
  - List CVEs
  - Get details of a specific CVE
  - Get latest 30 CVEs

`*` - requires authentication and account in Rad Security.

## Development

```bash
# Install dependencies
npm install

# Run type checking
npm run type-check

# Run linter
npm run lint

# Build
npm run build
```

## License

MIT License - see the [LICENSE](LICENSE) file for details
","Star
 3",2025-06-24T08:43:21.243953,mcp-server - MCP Server | Model Context Protocol Integration,"# RAD Security MCP Server

A Model Context Protocol (MCP) server for RAD Security, providing AI-powered security insights for Kubernetes and cloud environmen...","['mcp server', 'model context protocol', 'ai integration', 'mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-server', 'description': '# RAD Security MCP Server\n\nA Model Context Protocol (MCP) server for RAD Security, providing AI-powered security insights for Kubernetes and cloud environmen...', 'url': 'https://github.com/rad-security/mcp-server', 'codeRepository': 'https://github.com/rad-security/mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,Security,Cursor IDE,npm install @rad-security/mcp-server | npm install,Prerequisites,"{
  ""mcpServers"": {
    ""rad-security"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@rad-security/mcp-server""],
      ""env"": {
        ""RAD_SECURITY_ACCESS_KEY_ID"": ""<your-access-key-id>"",
        ""RAD_SECURITY_SECRET_KEY"": ""<your-secret-key>"",
        ""RAD_SECURITY_ACCOUNT_ID"": ""<your-account-id>""
      }
    }
  }","@rad-security/mcp-server, Run, checking, type",MIT License,2025-06-18,rad-security,"A Model Context Protocol (https://github.com/benborla/mcp-server-mysql,benborla/mcp-server-mysql,"
# MCP Server for MySQL based on NodeJS
[![smithery badge](https://smithery.ai/badge/@benborla29/mcp-server-mysql)](https://smithery.ai/server/@benborla29/mcp-server-mysql)

![Demo](assets/demo.gif)
<a href=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql/badge"" />
</a>

A Model Context Protocol server that provides access to MySQL databases. This server enables LLMs to inspect database schemas and execute SQL queries.

## Table of Contents
- [Requirements](#requirements)
- [Installation](#installation)
  - [Smithery](#using-smithery)
  - [Clone to Local Repository](#running-from-local-repository)
- [Components](#components)
- [Configuration](#configuration)
- [Environment Variables](#environment-variables)
- [Multi-DB Mode](#multi-db-mode)
- [Schema-Specific Permissions](#schema-specific-permissions)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)

## Requirements

- Node.js v18 or higher
- MySQL 5.7 or higher (MySQL 8.0+ recommended)
- MySQL user with appropriate permissions for the operations you need
- For write operations: MySQL user with INSERT, UPDATE, and/or DELETE privileges

## Installation

There are several ways to install and configure the MCP server but the most common would be checking this website https://smithery.ai/server/@benborla29/mcp-server-mysql

### Cursor

For Cursor IDE, you can install this MCP server with the following command in your project:

1. Visit https://smithery.ai/server/@benborla29/mcp-server-mysql
2. Follow the instruction for Cursor


MCP Get provides a centralized registry of MCP servers and simplifies the installation process.

### Using NPM/PNPM

For manual installation:

```bash
# Using npm
npm install -g @benborla29/mcp-server-mysql

# Using pnpm
pnpm add -g @benborla29/mcp-server-mysql
```

After manual installation, you'll need to configure your LLM application to use the MCP server (see Configuration section below).

### Running from Local Repository

If you want to clone and run this MCP server directly from the source code, follow these steps:

1. **Clone the repository**
   ```bash
   git clone https://github.com/benborla/mcp-server-mysql.git
   cd mcp-server-mysql
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Build the project**
   ```bash
   npm run build
   # or
   pnpm run build
   ```

4. **Configure Claude Desktop**

   Add the following to your Claude Desktop configuration file (`claude_desktop_config.json`):

   ```json
   {
     ""mcpServers"": {
       ""mcp_server_mysql"": {
         ""command"": ""/path/to/node"",
         ""args"": [
           ""/full/path/to/mcp-server-mysql/dist/index.js""
         ],
         ""env"": {
           ""MYSQL_HOST"": ""127.0.0.1"",
           ""MYSQL_PORT"": ""3306"",
           ""MYSQL_USER"": ""root"",
           ""MYSQL_PASS"": ""your_password"",
           ""MYSQL_DB"": ""your_database"",
           ""ALLOW_INSERT_OPERATION"": ""false"",
           ""ALLOW_UPDATE_OPERATION"": ""false"",
           ""ALLOW_DELETE_OPERATION"": ""false"",
           ""PATH"": ""/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/bin:/usr/bin:/bin"", // <--- Important to add the following, run in your terminal `echo ""$(which node)/../""` to get the path
           ""NODE_PATH"": ""/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/lib/node_modules"" // <--- Important to add the following, run in your terminal `echo ""$(which node)/../../lib/node_modules""`
         }
       }
     }
   }
   ```

   Replahttps://github.com/benborla/mcp-server-mysql,benborla/mcp-server-mysql,"
# MCP Server for MySQL based on NodeJS
[![smithery badge](https://smithery.ai/badge/@benborla29/mcp-server-mysql)](https://smithery.ai/server/@benborla29/mcp-server-mysql)

![Demo](assets/demo.gif)
<a href=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@benborla/mcp-server-mysql/badge"" />
</a>

A Model Context Protocol server that provides access to MySQL databases. This server enables LLMs to inspect database schemas and execute SQL queries.

## Table of Contents
- [Requirements](#requirements)
- [Installation](#installation)
  - [Smithery](#using-smithery)
  - [Clone to Local Repository](#running-from-local-repository)
- [Components](#components)
- [Configuration](#configuration)
- [Environment Variables](#environment-variables)
- [Multi-DB Mode](#multi-db-mode)
- [Schema-Specific Permissions](#schema-specific-permissions)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)

## Requirements

- Node.js v18 or higher
- MySQL 5.7 or higher (MySQL 8.0+ recommended)
- MySQL user with appropriate permissions for the operations you need
- For write operations: MySQL user with INSERT, UPDATE, and/or DELETE privileges

## Installation

There are several ways to install and configure the MCP server but the most common would be checking this website https://smithery.ai/server/@benborla29/mcp-server-mysql

### Cursor

For Cursor IDE, you can install this MCP server with the following command in your project:

1. Visit https://smithery.ai/server/@benborla29/mcp-server-mysql
2. Follow the instruction for Cursor


MCP Get provides a centralized registry of MCP servers and simplifies the installation process.

### Using NPM/PNPM

For manual installation:

```bash
# Using npm
npm install -g @benborla29/mcp-server-mysql

# Using pnpm
pnpm add -g @benborla29/mcp-server-mysql
```

After manual installation, you'll need to configure your LLM application to use the MCP server (see Configuration section below).

### Running from Local Repository

If you want to clone and run this MCP server directly from the source code, follow these steps:

1. **Clone the repository**
   ```bash
   git clone https://github.com/benborla/mcp-server-mysql.git
   cd mcp-server-mysql
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Build the project**
   ```bash
   npm run build
   # or
   pnpm run build
   ```

4. **Configure Claude Desktop**

   Add the following to your Claude Desktop configuration file (`claude_desktop_config.json`):

   ```json
   {
     ""mcpServers"": {
       ""mcp_server_mysql"": {
         ""command"": ""/path/to/node"",
         ""args"": [
           ""/full/path/to/mcp-server-mysql/dist/index.js""
         ],
         ""env"": {
           ""MYSQL_HOST"": ""127.0.0.1"",
           ""MYSQL_PORT"": ""3306"",
           ""MYSQL_USER"": ""root"",
           ""MYSQL_PASS"": ""your_password"",
           ""MYSQL_DB"": ""your_database"",
           ""ALLOW_INSERT_OPERATION"": ""false"",
           ""ALLOW_UPDATE_OPERATION"": ""false"",
           ""ALLOW_DELETE_OPERATION"": ""false"",
           ""PATH"": ""/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/bin:/usr/bin:/bin"", // <--- Important to add the following, run in your terminal `echo ""$(which node)/../""` to get the path
           ""NODE_PATH"": ""/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/lib/node_modules"" // <--- Important to add the following, run in your terminal `echo ""$(which node)/../../lib/node_modules""`
         }
       }
     }
   }
   ```

   Replace:
   - `/path/to/node` with the full path to your Node.js binary (find it with `which node`)
   - `/full/path/to/mcp-server-mysql` with the full path to where you cloned the repository
   - Set the MySQL credentials to match your environment

5. **Test the server**
   ```bash
   # Run the server directly to test
   node dist/index.js
   ```

   If it connects to MySQL successfully, you're ready to use it with Claude Desktop.

## Components

### Tools

- **mysql_query**
  - Execute SQL queries against the connected database
  - Input: `sql` (string): The SQL query to execute
  - By default, limited to READ ONLY operations
  - Optional write operations (when enabled via configuration):
    - INSERT: Add new data to tables (requires `ALLOW_INSERT_OPERATION=true`)
    - UPDATE: Modify existing data (requires `ALLOW_UPDATE_OPERATION=true`)
    - DELETE: Remove data (requires `ALLOW_DELETE_OPERATION=true`)
  - All operations are executed within a transaction with proper commit/rollback handling
  - Supports prepared statements for secure parameter handling
  - Configurable query timeouts and result pagination
  - Built-in query execution statistics

### Resources

The server provides comprehensive database information:

- **Table Schemas**
  - JSON schema information for each table
  - Column names and data types
  - Index information and constraints
  - Foreign key relationships
  - Table statistics and metrics
  - Automatically discovered from database metadata

### Security Features

- SQL injection prevention through prepared statements
- Query whitelisting/blacklisting capabilities
- Rate limiting for query execution
- Query complexity analysis
- Configurable connection encryption
- Read-only transaction enforcement

### Performance Optimizations

- Optimized connection pooling
- Query result caching
- Large result set streaming
- Query execution plan analysis
- Configurable query timeouts

### Monitoring and Debugging

- Comprehensive query logging
- Performance metrics collection
- Error tracking and reporting
- Health check endpoints
- Query execution statistics

## Configuration

### Automatic Configuration with Smithery
If you installed using Smithery, your configuration is already set up. You can view or modify it with:

```bash
smithery configure @benborla29/mcp-server-mysql
```

When reconfiguring, you can update any of the MySQL connection details as well as the write operation settings:

- **Basic connection settings**:
  - MySQL Host, Port, User, Password, Database
  - SSL/TLS configuration (if your database requires secure connections)

- **Write operation permissions**:
  - Allow INSERT Operations: Set to true if you want to allow adding new data
  - Allow UPDATE Operations: Set to true if you want to allow updating existing data
  - Allow DELETE Operations: Set to true if you want to allow deleting data

For security reasons, all write operations are disabled by default. Only enable these settings if you specifically need Claude to modify your database data.

### Advanced Configuration Options
For more control over the MCP server's behavior, you can use these advanced configuration options:

```json
{
  ""mcpServers"": {
    ""mcp_server_mysql"": {
      ""command"": ""/path/to/npx/binary/npx"",
      ""args"": [
        ""-y"",
        ""@benborla29/mcp-server-mysql""
      ],
      ""env"": {
        // Basic connection settings
        ""MYSQL_HOST"": ""127.0.0.1"",
        ""MYSQL_PORT"": ""3306"",
        ""MYSQL_USER"": ""root"",
        ""MYSQL_PASS"": """",
        ""MYSQL_DB"": ""db_name"",
        ""PATH"": ""/path/to/node/bin:/usr/bin:/bin"",
        
        // Performance settings
        ""MYSQL_POOL_SIZE"": ""10"",
        ""MYSQL_QUERY_TIMEOUT"": ""30000"",
        ""MYSQL_CACHE_TTL"": ""60000"",
        
        // Security settings
        ""MYSQL_RATE_LIMIT"": ""100"",
        ""MYSQL_MAX_QUERY_COMPLEXITY"": ""1000"",
        ""MYSQL_SSL"": ""true"",
        
        // Monitoring settings
        ""ENABLE_LOGGING"": ""true"",
        ""MYSQL_LOG_LEVEL"": ""info"",
        ""MYSQL_METRICS_ENABLED"": ""true"",
        
        // Write operation flags
        ""ALLOW_INSERT_OPERATION"": ""false"",
        ""ALLOW_UPDATE_OPERATION"": ""false"",
        ""ALLOW_DELETE_OPERATION"": ""false""
      }
    }
  }
}
```

## Environment Variables

### Basic Connection
- `MYSQL_SOCKET_PATH`: Unix socket path for local connections (e.g., ""/tmp/mysql.sock"")
- `MYSQL_HOST`: MySQL server host (default: ""127.0.0.1"") - ignored if MYSQL_SOCKET_PATH is set
- `MYSQL_PORT`: MySQL server port (default: ""3306"") - ignored if MYSQL_SOCKET_PATH is set
- `MYSQL_USER`: MySQL username (default: ""root"")
- `MYSQL_PASS`: MySQL password
- `MYSQL_DB`: Target database name (leave empty for multi-DB mode)

### Performance Configuration
- `MYSQL_POOL_SIZE`: Connection pool size (default: ""10"")
- `MYSQL_QUERY_TIMEOUT`: Query timeout in milliseconds (default: ""30000"")
- `MYSQL_CACHE_TTL`: Cache time-to-live in milliseconds (default: ""60000"")

### Security Configuration
- `MYSQL_RATE_LIMIT`: Maximum queries per minute (default: ""100"")
- `MYSQL_MAX_QUERY_COMPLEXITY`: Maximum query complexity score (default: ""1000"")
- `MYSQL_SSL`: Enable SSL/TLS encryption (default: ""false"")
- `ALLOW_INSERT_OPERATION`: Enable INSERT operations (default: ""false"")
- `ALLOW_UPDATE_OPERATION`: Enable UPDATE operations (default: ""false"")
- `ALLOW_DELETE_OPERATION`: Enable DELETE operations (default: ""false"")
- `ALLOW_DDL_OPERATION`: Enable DDL operations (default: ""false"")
- `SCHEMA_INSERT_PERMISSIONS`: Schema-specific INSERT permissions
- `SCHEMA_UPDATE_PERMISSIONS`: Schema-specific UPDATE permissions
- `SCHEMA_DELETE_PERMISSIONS`: Schema-specific DELETE permissions
- `SCHEMA_DDL_PERMISSIONS`: Schema-specific DDL permissions
- `MULTI_DB_WRITE_MODE`: Enable write operations in multi-DB mode (default: ""false"")

### Monitoring Configuration
- `MYSQL_ENABLE_LOGGING`: Enable query logging (default: ""false"")
- `MYSQL_LOG_LEVEL`: Logging level (default: ""info"")
- `MYSQL_METRICS_ENABLED`: Enable performance metrics (default: ""false"")

## Multi-DB Mode

MCP-Server-MySQL supports connecting to multiple databases when no specific database is set. This allows the LLM to query any database the MySQL user has access to. For full details, see [README-MULTI-DB.md](./README-MULTI-DB.md).

### Enabling Multi-DB Mode

To enable multi-DB mode, simply leave the `MYSQL_DB` environment variable empty. In multi-DB mode, queries require schema qualification:

```sql
-- Use fully qualified table names
SELECT * FROM database_name.table_name;

-- Or use USE statements to switch between databases
USE database_name;
SELECT * FROM table_name;
```

## Schema-Specific Permissions

For fine-grained control over database operations, MCP-Server-MySQL now supports schema-specific permissions. This allows different databases to have different levels of access (read-only, read-write, etc.).

### Configuration Example

```
SCHEMA_INSERT_PERMISSIONS=development:true,test:true,production:false
SCHEMA_UPDATE_PERMISSIONS=development:true,test:true,production:false
SCHEMA_DELETE_PERMISSIONS=development:false,test:true,production:false
SCHEMA_DDL_PERMISSIONS=development:false,test:true,production:false
```

For complete details and security recommendations, see [README-MULTI-DB.md](./README-MULTI-DB.md).

## Testing

### Database Setup

Before running tests, you need to set up the test database and seed it with test data:

1. **Create Test Database and User**
   ```sql
   -- Connect as root and create test database
   CREATE DATABASE IF NOT EXISTS mcp_test;
   
   -- Create test user with appropriate permissions
   CREATE USER IF NOT EXISTS 'mcp_test'@'localhost' IDENTIFIED BY 'mcp_test_password';
   GRANT ALL PRIVILEGES ON mcp_test.* TO 'mcp_test'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **Run Database Setup Script**
   ```bash
   # Run the database setup script
   pnpm run setup:test:db
   ```

   This will create the necessary tables and seed data. The script is located in `scripts/setup-test-db.ts`

3. **Configure Test Environment**
   Create a `.env.test` file in the project root (if not existing):
   ```env
   MYSQL_HOST=127.0.0.1
   MYSQL_PORT=3306
   MYSQL_USER=mcp_test
   MYSQL_PASS=mcp_test_password
   MYSQL_DB=mcp_test
   ```

4. **Update package.json Scripts**
   Add these scripts to your package.json:
   ```json
   {
     ""scripts"": {
       ""setup:test:db"": ""ts-node scripts/setup-test-db.ts"",
       ""pretest"": ""pnpm run setup:test:db"",
       ""test"": ""vitest run"",
       ""test:watch"": ""vitest"",
       ""test:coverage"": ""vitest run --coverage""
     }
   }
   ```

### Running Tests

The project includes a comprehensive test suite to ensure functionality and reliability:

```bash
# First-time setup
pnpm run setup:test:db

# Run all tests
pnpm test
```



## Running evals

The evals package loads an mcp client that then runs the index.ts file, so there is no need to rebuild between tests. You can load environment variables by prefixing the npx command. Full documentation can be found [here](https://www.mcpevals.io/docs).

```bash
OPENAI_API_KEY=your-key  npx mcp-eval evals.ts index.ts
```
## Troubleshooting

### Common Issues

1. **Connection Issues**
   - Verify MySQL server is running and accessible
   - Check credentials and permissions
   - Ensure SSL/TLS configuration is correct if enabled
   - Try connecting with a MySQL client to confirm access

2. **Performance Issues**
   - Adjust connection pool size
   - Configure query timeout values
   - Enable query caching if needed
   - Check query complexity settings
   - Monitor server resource usage

3. **Security Restrictions**
   - Review rate limiting configuration
   - Check query whitelist/blacklist settings
   - Verify SSL/TLS settings
   - Ensure the user has appropriate MySQL permissions

4. **Path Resolution**
If you encounter an error ""Could not connect to MCP server mcp-server-mysql"", explicitly set the path of all required binaries:
```json
{
  ""env"": {
    ""PATH"": ""/path/to/node/bin:/usr/bin:/bin""
  }
}
```

*Where can I find my `node` bin path*
Run the following command to get it:

For **PATH**
```bash
echo ""$(which node)/../""    
```

For **NODE_PATH**
```bash
echo ""$(which node)/../../lib/node_modules""    
```

5. **Claude Desktop Specific Issues**
   - If you see ""Server disconnected"" logs in Claude Desktop, check the logs at `~/Library/Logs/Claude/mcp-server-mcp_server_mysql.log`
   - Ensure you're using the absolute path to both the Node binary and the server script
   - Check if your `.env` file is being properly loaded; use explicit environment variables in the configuration
   - Try running the server directly from the command line to see if there are connection issues
   - If you need write operations (INSERT, UPDATE, DELETE), set the appropriate flags to ""true"" in your configuration:
     ```json
     ""env"": {
       ""ALLOW_INSERT_OPERATION"": ""true"",  // Enable INSERT operations
       ""ALLOW_UPDATE_OPERATION"": ""true"",  // Enable UPDATE operations
       ""ALLOW_DELETE_OPERATION"": ""true""   // Enable DELETE operations
     }
     ```
   - Ensure your MySQL user has the appropriate permissions for the operations you're enabling
   - For direct execution configuration, use:
     ```json
     {
       ""mcpServers"": {
         ""mcp_server_mysql"": {
           ""command"": ""/full/path/to/node"",
           ""args"": [
             ""/full/path/to/mcp-server-mysql/dist/index.js""
           ],
           ""env"": {
             ""MYSQL_HOST"": ""127.0.0.1"",
             ""MYSQL_PORThttps://github.com/MarkusPfundstein/mcp-obsidian,MarkusPfundstein/mcp-obsidian,"# MCP server for Obsidian

MCP server to interact with Obsidian via the Local REST API community plugin.

<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/3wko1bhuek/badge"" alt=""server for Obsidian MCP server"" /></a>

## Components

### Tools

The server implements multiple tools to interact with Obsidian:

- list_files_in_vault: Lists all files and directories in the root directory of your Obsidian vault
- list_files_in_dir: Lists all files and directories in a specific Obsidian directory
- get_file_contents: Return the content of a single file in your vault.
- search: Search for documents matching a specified text query across all files in the vault
- patch_content: Insert content into an existing note relative to a heading, block reference, or frontmatter field.
- append_content: Append content to a new or existing file in the vault.
- delete_file: Delete a file or directory from your vault.

### Example prompts

Its good to first instruct Claude to use Obsidian. Then it will always call the tool.

The use prompts like this:
- Get the contents of the last architecture call note and summarize them
- Search for all files where Azure CosmosDb is mentioned and quickly explain to me the context in which it is mentioned
- Summarize the last meeting notes and put them into a new note 'summary meeting.md'. Add an introduction so that I can send it via email.

## Configuration

### Obsidian REST API Key

There are two ways to configure the environment with the Obsidian REST API Key. 

1. Add to server config (preferred)

```json
{
  ""mcp-obsidian"": {
    ""command"": ""uvx"",
    ""args"": [
      ""mcp-obsidian""
    ],
    ""env"": {
      ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
      ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
      ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
    }
  }
}
```

2. Create a `.env` file in the working directory with the following required variables:

```
OBSIDIAN_API_KEY=your_api_key_here
OBSIDIAN_HOST=your_obsidian_host
OBSIDIAN_PORT=your_obsidian_port
```

Note:
- You can find the API key in the Obsidian plugin config
- Default port is 27124 if not specified
- Default host is 127.0.0.1 if not specified

## Quickstart

### Install

#### Obsidian REST API

You need the Obsidian REST API community plugin running: https://github.com/coddingtonbear/obsidian-local-rest-api

Install and enable it in the settings and copy the api key.

#### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`

On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<dir_to>/mcp-obsidian"",
        ""run"",
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uvx"",
      ""args"": [
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<YOUR_OBSIDIAN_API_KEY>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

## Development

### Building

To prepare the package for distribution:

1. Sync dependencies and update lockfile:
```bash
uv sync
```

### Debugging

Since MCP servers run https://github.com/MarkusPfundstein/mcp-obsidian,MarkusPfundstein/mcp-obsidian,"# MCP server for Obsidian

MCP server to interact with Obsidian via the Local REST API community plugin.

<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/3wko1bhuek/badge"" alt=""server for Obsidian MCP server"" /></a>

## Components

### Tools

The server implements multiple tools to interact with Obsidian:

- list_files_in_vault: Lists all files and directories in the root directory of your Obsidian vault
- list_files_in_dir: Lists all files and directories in a specific Obsidian directory
- get_file_contents: Return the content of a single file in your vault.
- search: Search for documents matching a specified text query across all files in the vault
- patch_content: Insert content into an existing note relative to a heading, block reference, or frontmatter field.
- append_content: Append content to a new or existing file in the vault.
- delete_file: Delete a file or directory from your vault.

### Example prompts

Its good to first instruct Claude to use Obsidian. Then it will always call the tool.

The use prompts like this:
- Get the contents of the last architecture call note and summarize them
- Search for all files where Azure CosmosDb is mentioned and quickly explain to me the context in which it is mentioned
- Summarize the last meeting notes and put them into a new note 'summary meeting.md'. Add an introduction so that I can send it via email.

## Configuration

### Obsidian REST API Key

There are two ways to configure the environment with the Obsidian REST API Key. 

1. Add to server config (preferred)

```json
{
  ""mcp-obsidian"": {
    ""command"": ""uvx"",
    ""args"": [
      ""mcp-obsidian""
    ],
    ""env"": {
      ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
      ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
      ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
    }
  }
}
```

2. Create a `.env` file in the working directory with the following required variables:

```
OBSIDIAN_API_KEY=your_api_key_here
OBSIDIAN_HOST=your_obsidian_host
OBSIDIAN_PORT=your_obsidian_port
```

Note:
- You can find the API key in the Obsidian plugin config
- Default port is 27124 if not specified
- Default host is 127.0.0.1 if not specified

## Quickstart

### Install

#### Obsidian REST API

You need the Obsidian REST API community plugin running: https://github.com/coddingtonbear/obsidian-local-rest-api

Install and enable it in the settings and copy the api key.

#### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`

On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<dir_to>/mcp-obsidian"",
        ""run"",
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  
```json
{
  ""mcpServers"": {
    ""mcp-obsidian"": {
      ""command"": ""uvx"",
      ""args"": [
        ""mcp-obsidian""
      ],
      ""env"": {
        ""OBSIDIAN_API_KEY"": ""<YOUR_OBSIDIAN_API_KEY>"",
        ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
        ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
      }
    }
  }
}
```
</details>

## Development

### Building

To prepare the package for distribution:

1. Sync dependencies and update lockfile:
```bash
uv sync
```

### Debugging

Since MCP servers run over stdio, debugging can be challenging. For the best debugging
experience, we strongly recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector).

You can launch the MCP Inspector via [`npm`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm) with this command:

```bash
npx @modelcontextprotocol/inspector uv --directory /path/to/mcp-obsidian run mcp-obsidian
```

Upon launching, the Inspector will display a URL that you can access in your browser to begin debugging.

You can also watch the server logs with this command:

```bash
tail -n 20 -f ~/Library/Logs/Claude/mcp-server-mcp-obsidian.log
```
","Star
 1.2k",2025-06-24T08:43:21.243936,mcp-obsidian - MCP Server | Model Context Protocol Integration,"# MCP server for Obsidian

MCP server to interact with Obsidian via the Local REST API community plugin.

<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><...","['mcp server', 'model context protocol', 'ai integration', 'mcp-obsidian']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-obsidian', 'description': '# MCP server for Obsidian\n\nMCP server to interact with Obsidian via the Local REST API community plugin.\n\n<a href=""https://glama.ai/mcp/servers/3wko1bhuek""><...', 'url': 'https://github.com/MarkusPfundstein/mcp-obsidian', 'codeRepository': 'https://github.com/MarkusPfundstein/mcp-obsidian', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,API Integration,Text Processing,,"Building, Debugging, Install, Tools","{
  ""mcp-obsidian"": {
    ""command"": ""uvx"",
    ""args"": [
      ""mcp-obsidian""
    ],
    ""env"": {
      ""OBSIDIAN_API_KEY"": ""<your_api_key_here>"",
      ""OBSIDIAN_HOST"": ""<your_obsidian_host>"",
      ""OBSIDIAN_PORT"": ""<your_obsidian_port>""
    }
  }
}",,MIT License,2025-06-24,MarkusPfundstein,MCP server to interact with Obsidian via the Local REST API community plugin.
https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git,SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git,,"Star
 8",2025-06-24T08:43:21.243930,Crypto-Trader-MCP-ClaudeDesktop.git - MCP Server | Model Context Protocol Integration,,"['mcp server', 'model context protocol', 'ai integration', 'Crypto-Trader-MCP-ClaudeDesktop.git']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'Crypto-Trader-MCP-ClaudeDesktop.git', 'description': '', 'url': 'https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git', 'codeRepository': 'https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Unknown,Other,,,,,,,,SaintDoresh,
https://github.com/micl2e2/code-to-tree,micl2e2/code-to-tree,"
# Table of Contents

-   [MCP Server: code-to-tree](#orgf542482)
-   [Using code-to-tree](#org862e7dc)
-   [Configure MCP Clients](#orge54fa87)
-   [Building (Windows)](#org48a8180)
-   [Building (macOS)](#orgbaa740e)



<a id=""orgf542482""></a>

# MCP Server: code-to-tree

The code-to-tree server's goals are:

1.  Give LLMs the capability of **accurately** converting source code into
    AST(Abstract Syntax Tree), regardless of language.
2.  One **standalone** binary should be everything the MCP client needs.

These goals imply:

1.  The underlying syntax parser should be **versatile** enough. Here we
    choose [tree-sitter](https://github.com/tree-sitter/tree-sitter), and languages are: C, C++, Rust, Ruby, Go, Java, Python.
2.  The server should be able to carry all capabilities within
    itself, imposing **minimum** software dependencies on the end user's
    machine. Here we choose [mcpc](https://github.com/micl2e2/mcpc).

**Screenshots:**

<img src=""./chathistory.png"" width=""450px"" /><img src=""./wholeast.png"" width=""200px"" />

The above screenshots are obtained by asking the question specified
in `q.md`. 

(**IMPORTANT NOTE**: LLMs have no responsibility of generating the identical
result for the same question,  you will likely get a completely different
style or content. The screenshots or questions provided here are just for the reference)


<a id=""org862e7dc""></a>

# Using code-to-tree

Before everthing, you need to have the code-to-tree executable on your
machine (`code-to-tree.exe` for Windows, `code-to-tree` for macOS),
you can download at GitHub release [page](https://github.com/micl2e2/code-to-tree/releases) or build it yourself. Once
downloaded, you configure your MCP clients to install it, check the section
*""Configure MCP Clients""* for more details.


<a id=""orge54fa87""></a>

# Configure MCP Clients

Here we use Claude as the example.


## Windows

In your Claude configuration
(`C:\Users\<USER>\AppData\Roaming\Claude\claude_desktop_config.json`),
specify the location of `code-to-tree.exe`:

    {
        ""mcpServers"": {
    	    ""code-to-tree"": { ""command"": ""C:\\path\\to\\code-to-tree.exe"" }
        }
    }


## macOS

In your Claude configuration,
(`~/Library/Application Support/Claude/claude_desktop_config.json`)
specify the location of `code-to-tree`

    {
        ""mcpServers"": {
    	    ""code-to-tree"": { ""command"": ""/path/to/code-to-tree"" }
        }
    }


<a id=""org48a8180""></a>

# https://github.com/oraios/serena,oraios/serena,"<p align=""center"" style=""text-align:center"">
  <img src=""resources/serena-logo.svg#gh-light-mode-only"" style=""width:500px"">
  <img src=""resources/serena-logo-dark-mode.svg#gh-dark-mode-only"" style=""width:500px"">
</p>

* :rocket: Serena is a powerful **coding agent toolkit** capable of turning an LLM into a fully-featured agent that works **directly on your codebase**.
* :wrench: Serena provides essential **semantic code retrieval and editing tools** that are akin to an IDE's capabilities, extracting code entities at the symbol level and exploiting relational structure.
* :free: Serena is **free & open-source**, enhancing the capabilities of LLMs you already have access to free of charge.

### Demonstration

Here is a demonstration of Serena implementing a small feature for itself (a better log GUI) with Claude Desktop.
Note how Serena's tools enable Claude to find and edit the right symbols.

https://github.com/user-attachments/assets/6eaa9aa1-610d-4723-a2d6-bf1e487ba753

<p align=""center"">
  <em>Serena is under active development! See the latest updates, upcoming features, and lessons learned to stay up to date.</em>
</p>

<p align=""center"">
  <a href=""CHANGELOG.md"">
    <img src=""https://img.shields.io/badge/Updates-1e293b?style=flat&logo=rss&logoColor=white&labelColor=1e293b"" alt=""Changelog"" />
  </a>
  <a href=""roadmap.md"">
    <img src=""https://img.shields.io/badge/Roadmap-14532d?style=flat&logo=target&logoColor=white&labelColor=14532d"" alt=""Roadmap"" />
  </a>
  <a href=""lessons_learned.md"">
    <img src=""https://img.shields.io/badge/Lessons-Learned-7c4700?style=flat&logo=readthedocs&logoColor=white&labelColor=7c4700"" alt=""Lessons Learned"" />
  </a>
</p>



### LLM Integration

Serena provides the necessary [tools](#full-list-of-tools) for coding workflows, but an LLM is required to do the actual work,
orchestrating tool use.

For example, **supercharge the performance of Claude Code** with a [one-line shell command](#claude-code).

Serena can be integrated with an LLM in several ways:
 * by using the **model context protocol (MCP)**.  
   Serena provides an MCP server which integrates with 
     * Claude Code and Claude Desktop, 
     * IDEs like VSCode, Cursor or IntelliJ,
     * Extensions like Cline or Roo Code
     * Goose (for a nice CLI experience)
     * and many others, including [the ChatGPT app soon](https://x.com/OpenAIDevs/status/1904957755829481737)
 * by using **Agno – the model-agnostic agent framework**.  
   Serena's Agno-based agent allows you to turn virtually any LLM into a coding agent, whether it's provided by Google, OpenAI or Anthropic (with a paid API key)
   or a free model provided by Ollama, Together or Anyscale.
 * by incorporating Serena's tools into an agent framework of your choice.  
   Serena's tool implementation is decoupled from the framework-specific code and can thus easily be adapted to any agent framework.

### Programming Language Support & Semantic Analysis Capabilities

Serena's semantic code analysis capabilities build on **language servers** using the widely implemented
language server protocol (LSP). The LSP provides a set of versatile code querying
and editing functionalities based on symbolic understanding of the code. 
Equipped with these capabilities, Serena discovers and edits code just like a seasoned developer 
making use of an IDE's capabilities would.
Serena can efficiently find the right context and do the right thing even in very large and
complex projects! So not only is it free and open-source, it frequently achieves better results 
than existing solutions that charge a premium.

Language servers provide support for a wide range of programming languages.
With Serena, we provide 
 * direct, out-of-https://github.com/oraios/serena,oraios/serena,"<p align=""center"" style=""text-align:center"">
  <img src=""resources/serena-logo.svg#gh-light-mode-only"" style=""width:500px"">
  <img src=""resources/serena-logo-dark-mode.svg#gh-dark-mode-only"" style=""width:500px"">
</p>

* :rocket: Serena is a powerful **coding agent toolkit** capable of turning an LLM into a fully-featured agent that works **directly on your codebase**.
* :wrench: Serena provides essential **semantic code retrieval and editing tools** that are akin to an IDE's capabilities, extracting code entities at the symbol level and exploiting relational structure.
* :free: Serena is **free & open-source**, enhancing the capabilities of LLMs you already have access to free of charge.

### Demonstration

Here is a demonstration of Serena implementing a small feature for itself (a better log GUI) with Claude Desktop.
Note how Serena's tools enable Claude to find and edit the right symbols.

https://github.com/user-attachments/assets/6eaa9aa1-610d-4723-a2d6-bf1e487ba753

<p align=""center"">
  <em>Serena is under active development! See the latest updates, upcoming features, and lessons learned to stay up to date.</em>
</p>

<p align=""center"">
  <a href=""CHANGELOG.md"">
    <img src=""https://img.shields.io/badge/Updates-1e293b?style=flat&logo=rss&logoColor=white&labelColor=1e293b"" alt=""Changelog"" />
  </a>
  <a href=""roadmap.md"">
    <img src=""https://img.shields.io/badge/Roadmap-14532d?style=flat&logo=target&logoColor=white&labelColor=14532d"" alt=""Roadmap"" />
  </a>
  <a href=""lessons_learned.md"">
    <img src=""https://img.shields.io/badge/Lessons-Learned-7c4700?style=flat&logo=readthedocs&logoColor=white&labelColor=7c4700"" alt=""Lessons Learned"" />
  </a>
</p>



### LLM Integration

Serena provides the necessary [tools](#full-list-of-tools) for coding workflows, but an LLM is required to do the actual work,
orchestrating tool use.

For example, **supercharge the performance of Claude Code** with a [one-line shell command](#claude-code).

Serena can be integrated with an LLM in several ways:
 * by using the **model context protocol (MCP)**.  
   Serena provides an MCP server which integrates with 
     * Claude Code and Claude Desktop, 
     * IDEs like VSCode, Cursor or IntelliJ,
     * Extensions like Cline or Roo Code
     * Goose (for a nice CLI experience)
     * and many others, including [the ChatGPT app soon](https://x.com/OpenAIDevs/status/1904957755829481737)
 * by using **Agno – the model-agnostic agent framework**.  
   Serena's Agno-based agent allows you to turn virtually any LLM into a coding agent, whether it's provided by Google, OpenAI or Anthropic (with a paid API key)
   or a free model provided by Ollama, Together or Anyscale.
 * by incorporating Serena's tools into an agent framework of your choice.  
   Serena's tool implementation is decoupled from the framework-specific code and can thus easily be adapted to any agent framework.

### Programming Language Support & Semantic Analysis Capabilities

Serena's semantic code analysis capabilities build on **language servers** using the widely implemented
language server protocol (LSP). The LSP provides a set of versatile code querying
and editing functionalities based on symbolic understanding of the code. 
Equipped with these capabilities, Serena discovers and edits code just like a seasoned developer 
making use of an IDE's capabilities would.
Serena can efficiently find the right context and do the right thing even in very large and
complex projects! So not only is it free and open-source, it frequently achieves better results 
than existing solutions that charge a premium.

Language servers provide support for a wide range of programming languages.
With Serena, we provide 
 * direct, out-of-the-box support for:
     * Python 
     * TypeScript/Javascript
     * PHP
     * Go (need to install go and gopls first)
     * Rust
     * C/C++
     * Java (_Note_: startup is slow, initial startup especially so. There seem to be issues with java on macos)
 * indirect support (may require some code changes/manual installation) for:
     * Ruby (untested)
     * C# (untested)
     * Kotlin (untested)
     * Dart (untested)
     
   These languages are supported by the language server library, but
   we did not explicitly test whether the support for these languages actually works flawlessly.
       
Further languages can, in principle, easily be supported by providing a shallow adapter for a new language server
implementation.


## Table of Contents

<!-- Created with markdown-toc -i README.md -->
<!-- Install it with npm install -g markdown-toc -->

<!-- toc -->

- [What Can I Use Serena For?](#what-can-i-use-serena-for)
- [Free Coding Agents with Serena](#free-coding-agents-with-serena)
- [Quick Start](#quick-start)
  * [Running the Serena MCP Server](#running-the-serena-mcp-server)
    + [Usage](#usage)
        * [Local Installation](#local-installation)
      - [Using uvx](#using-uvx)
      - [Using Docker (Experimental)](#using-docker-experimental)
    + [SSE Mode](#sse-mode)
    + [Command-Line Arguments](#command-line-arguments)
  * [Configuration](#configuration)
  * [Project Activation & Indexing](#project-activation--indexing)
  * [Claude Code](#claude-code)
  * [Claude Desktop](#claude-desktop)
  * [Other MCP Clients (Cline, Roo-Code, Cursor, Windsurf, etc.)](#other-mcp-clients-cline-roo-code-cursor-windsurf-etc)
  * [Agno Agent](#agno-agent)
  * [Other Agent Frameworks](#other-agent-frameworks)
- [Detailed Usage and Recommendations](#detailed-usage-and-recommendations)
  * [Tool Execution](#tool-execution)
    + [Shell Execution and Editing Tools](#shell-execution-and-editing-tools)
  * [Modes and Contexts](#modes-and-contexts)
    + [Contexts](#contexts)
    + [Modes](#modes)
    + [Customization](#customization)
  * [Onboarding and Memories](#onboarding-and-memories)
  * [Prepare Your Project](#prepare-your-project)
    + [Structure Your Codebase](#structure-your-codebase)
    + [Start from a Clean State](#start-from-a-clean-state)
    + [Logging, Linting, and Automated Tests](#logging-linting-and-automated-tests)
  * [Prompting Strategies](#prompting-strategies)
  * [Potential Issues in Code Editing](#potential-issues-in-code-editing)
  * [Running Out of Context](#running-out-of-context)
  * [Combining Serena with Other MCP Servers](#combining-serena-with-other-mcp-servers)
  * [Serena's Logs: The Dashboard and GUI Tool](#serenas-logs-the-dashboard-and-gui-tool)
  * [Troubleshooting](#troubleshooting)
- [Comparison with Other Coding Agents](#comparison-with-other-coding-agents)
  * [Subscription-Based Coding Agents](#subscription-based-coding-agents)
  * [API-Based Coding Agents](#api-based-coding-agents)
  * [Other MCP-Based Coding Agents](#other-mcp-based-coding-agents)
- [Acknowledgements](#acknowledgements)
- [Customizing and Extending Serena](#customizing-and-extending-serena)
- [Full List of Tools](#full-list-of-tools)

<!-- tocstop -->

## What Can I Use Serena For?

You can use Serena for any coding tasks – whether it is focussed on analysis, planning, 
designing new components or refactoring existing ones.
Since Serena's tools allow an LLM to close the cognitive perception-action loop, 
agents based on Serena can autonomously carry out coding tasks from start to finish – 
from the initial analysis to the implementation, testing and, finally, the version
control system commit.

Serena can read, write and execute code, read logs and the terminal output.
While we do not necessarily encourage it, ""vibe coding"" is certainly possible, and if you 
want to almost feel like ""the code no longer exists"",
you may find Serena even more adequate for vibing than an agent inside an IDE
(since you will have a separate GUI that really lets you forget).

## Free Coding Agents with Serena

Even the free tier of Anthropic's Claude has support for MCP Servers, so you can use Serena with Claude for free.
Presumably, the same will soon be possible with ChatGPT Desktop once support for MCP servers is added.  
Through Agno, you furthermore have the option to use Serena with a free/open-weights model.

Serena is [Oraios AI](https://oraios-ai.de/)'s contribution to the developer community.  
We use it ourselves on a regular basis.

We got tired of having to pay multiple
IDE-based subscriptions (such as Windsurf or Cursor) that forced us to keep purchasing tokens on top of the chat subscription costs we already had.
The substantial API costs incurred by tools like Claude Code, Cline, Aider and other API-based tools are similarly unattractive.
We thus built Serena with the prospect of being able to cancel most other subscriptions.

## Quick Start

Serena can be used in various ways, below you will find instructions for selected integrations.

- If you just want to turn Claude into a free-to-use coding agent, we recommend using Serena through [Claude Code](#claude-code) or [Claude Desktop](#claude-desktop).
- If you want to use Gemini or any other model, and you want a GUI experience, you can use [Agno](#agno-agent). On macOS, you can also use the GUI of [goose](#goose).
- If you want to use Serena integrated in your IDE, see the section on [other MCP clients](#other-mcp-clients---cline-roo-code-cursor-windsurf-etc).

Serena is managed by `uv`, so you will need to [install it](https://docs.astral.sh/uv/getting-started/installation/)).

### Running the Serena MCP Server

You have several options for running the MCP server, which are explained in the subsections below.

#### Usage

The typical usage involves the client (Claude Code, Claude Desktop, etc.) running
the MCP server as a subprocess (using stdio communication), 
so the client needs to be provided with the command to run the MCP server.
(Alternatively, you can run the MCP server in SSE mode and tell your client 
how to connect to it.)

Note that no matter how you run the MCP server, Serena will, by default, start a small web-based dashboard on localhost that will display logs and allow shutting down the
MCP server (since many clients fail to clean up processes correctly).
This and other settings can be adjusted in the [configuration](#configuration) and/or by providing [command-line arguments](#command-line-arguments).

###### Local Installation

1. Clone the repository and change into it.
   ```shell
   git clone https://github.com/oraios/serena
   cd serena
   ```
2. Optionally create a config file from the template and adjust it according to your preferences.
   ```shell
   cp src/serena/resources/serena_config.template.yml serena_config.yml
   ```
   If you just want the default config, you can skip this part, and a config file will be created when you first run Serena.
3. Run the server with `uv`:
   ```shell
   uv run serena-mcp-server
   ```
   When running from outside the serena installation directory, be sure to pass it, i.e. use
   ```shell
    uv run --directory /abs/path/to/serena serena-mcp-server
    ```

##### Using uvx

`uvx` can be used to run the latest version of Serena directly from the repository, without an explicit local installation.

* Windows:
  ```shell
  uvx --from git+https://github.com/oraios/serena serena-mcp-server.exe
  ```
* Other operating systems:
  ```shell
  uvx --from git+https://github.com/oraios/serena serena-mcp-server
  ```

##### Using Docker (Experimental)

⚠️ Docker support is currently experimental with several limitations. Please read the [Docker documentation](DOCKER.md) for important caveats before using it.

You can run the Serena MCP server directly via docker as follows,
assuming that the projects you want to work on are all located in `/path/to/your/projects`:

```shell
docker run --rm -i --network host -v /path/to/your/projects:/workspaces/projects ghcr.io/oraios/serena:latest serena-mcp-server --transport stdio
```

Replace `/path/to/your/projects` with the absolute path to your projects directory. The Docker approach provides:
- Better security isolation for shell command execution
- No need to install language servers and dependencies locally
- Consistent environment across different systems

See the [Docker documentation](DOCKER.md) for detailed setup instructions, configuration options, and known limitations.

#### SSE Mode

ℹ️ Note that MCP servers which use stdio as a protocol are somewhat unusual as far as client/server architectures go, as the server
necessarily has to be started by the client in order for communication to take place via the server's standard input/output stream.
In other words, you do not need to start the server yourself. The client application (e.g. Claude Desktop) takes care of this and
therefore needs to be configured with a launch command. 

When using instead the SSE mode, which uses HTTP-based communication, you control the server lifecycle yourself,
i.e. you start the server and provide the client with the URL to connect to it.

Simply provide `serena-mcp-server` with the `--transport sse` option and optionally provide the port.
For example, to run the Serena MCP server in SSE mode on port 9121 using a local installation,
you would run this command from the Serena directory, 

```shell
uv run serena-mcp-server --transport sse --port 9121
```

and then configure your client to connect to `http://localhost:9121`.


#### Command-Line Arguments

The Serena MCP server supports a wide range of additional command-line options, including the option to run in SSE mode
and to adapt Serena to various [contexts and modes of operation](#modes-and-contexts).

Run with parameter `--help` to get a list of available options.


### Configuration

Serena's behavior (active tools and prompts as well as logging configuration, etc.) is configured in four places:

1. The `serena_config.yml` for general settings that apply to all clients and projects
2. In the arguments passed to the `serena-mcp-server` in your client's config (see below), 
   which will apply to all sessions started by the respective client. In particular, the [context](#contexts) parameter
   should be set appropriately for Serena to be best adjusted to existing tools and capabilities of your client.
   See for a detailed explanation. You can override all entries from the `serena_config.yml` through command line arguments.
3. In the `.serena/project.yml` file within your project. This will hold project-level configuration that is used whenever
   that project is activated.
4. Through the currently active set of [modes](#modes).


> ⚠️ **Note:** Serena is under active development. We are continuously adding features, improving stability and the UX.
> As a result, configuration may change in a breaking manner. If you have an invalid configuration,
> the MCP server or Serena-based Agent may fail to start (investigate the MCP logs in the former case).
> Check the [changelog](CHANGELOG.md)
> and the configuration templates when updating Serena, adapting your configurations accordingly.

After the initial setup, continue with one of the sections below, depending on how you
want to use Serena.

You can just ask the LLM to show you the config of your session, Serena has a tool for it.

### Project Activation & Indexing

The recommended way is to just ask the LLM to activate a project by providing it an absolute path to, or,
in case the project was activated in the past, by its name. The default project name is the directory name.

  * ""Activate the project /path/to/my_project""
  * ""Activate the project my_project""

All projects that have been activated will be automatically added to your `serena_config.yml`, and for each 
project, the file `.serena/project.yml` will be generated. You can adjust the latter, e.g., by changing the name
(which you refer to during the activation) or other options. Make sure to not have two different projects with the
same name.

If you are mostly working with the same project, you can also configure to always activate a project at startup
by passing `--project <path_or_name>` to the `serena-mcp-server` command in your client's MCP config.

ℹ️ For larger projects, we recommend that you index your project to accelerate Serena's tools; otherwise the first
tool application may be very slow.
To do so, run one of these commands the project directory or pass the path to the project as an argument:

* When using a local installation:
  ```shell
  uv run --directory /abs/path/to/serena index-project
  ```
* When using uvx:
  ```shell
  uvx --from git+https://github.com/oraios/serena index-project
  ```

### Claude Code

Serena is a great way to make Claude Code both cheaper and more powerful! 

From your project directory, add serena with a command like this,

```shell
claude mcp add serena -- <serena-mcp-server> --context ide-assistant --project $(pwd)
```

where `<serena-mcp-server>` is your way of [running the Serena MCP server](#running-the-serena-mcp-server).
For example, when using `uvx`, you would run
```shell
claude mcp add serena -- uvx --from git+https://github.com/oraios/serena serena-mcp-server --context ide-assistant --project $(pwd)
```

ℹ️ Once in Claude Code, you should ask Claude to ""Read the initial instructions"" as your first prompt, such that it will receive information
on how to use Serena's tools.


### Claude Desktop

For [Claude Desktop](https://claude.ai/download) (available for Windows and macOS), go to File / Settings / Developer / MCP Servers / Edit Config,
which will let you open the JSON file `claude_desktop_config.json`. 
Add the `serena` MCP server configuration, using a [run command](#running-the-serena-mcp-server) depending on your setup.

* local installation:
   ```json
   {
       ""mcpServers"": {
           ""serena"": {
               ""command"": ""/abs/path/to/uv"",
               ""args"": [""run"", ""--directory"", ""/abs/path/to/serena"", ""serena-mcp-server""]
           }
       }
   }
   ```
* uvx:
   ```json
   {
       ""mcpServers"": {
           ""serena"": {
               ""command"": ""/abs/path/to/uvx"",
               ""args"": [""--from"", ""git+https://github.com/oraios/serena"", ""serena-mcp-server""]
           }
       }
  }
  ```
* docker:
  ```json
   {
       ""mcpServers"": {
           ""serena"": {
               ""command"": ""docker"",
               ""args"": [""run"", ""--rm"", ""-i"", ""--network"", ""host"", ""-v"", ""/path/to/your/projects:/workspaces/projects"", ""ghcr.io/oraios/serena:latest"", ""serena-mcp-server"", ""--transport"", ""stdio""]
           }
       }
   }
   ```

If you are using paths containing backslashes for paths on Windows
(note that you can also just use forward slashes), be sure to escape them correctly (`\\`).

That's it! Save the config and then restart Claude Desktop. You are ready for activating your first project.

ℹ️ You can further customize the run command using additional arguments (see [above](#command-line-arguments)).

Note: on Windows and macOS there are official Claude Desktop applications by Anthropic, for Linux there is an [open-source
community version](https://github.com/aaddrick/claude-desktop-debian).

⚠️ Be sure to fully quit the Claude Desktop application, as closing Claude will just minimize it to the system tray – at least on Windows.  

⚠️ Some clients, currently including Claude Desktop, may leave behind zombie processes. You will have to find and terminate them manually then.
    With Serena, you can activate the [dashboard](#serenas-logs-the-dashboard-and-gui-tool) to prevent unnoted processes and also use the dashboard
    for shutting down Serena.

After restarting, you should see Serena's tools in your chat interface (notice the small hammer icon).

For more information on MCP servers with Claude Desktop, see [the official quick start guide](https://modelcontextprotocol.io/quickstart/user).

### Other MCP Clients (Cline, Roo-Code, Cursor, Windsurf, etc.)

Being an MCP Server, Serena can be included in any MCP Client. The same configuration as above,
perhaps with small client-specific modifications, should work. Most of the popular
existing coding assistants (IDE extensions or VSCode-like IDEs) support connections
to MCP Servers. It is **recommended to use the `ide-assistant` context** for these integrations by adding `""--context"", ""ide-assistant""` to the `args` in your MCP client's configuration. Including Serena generally boosts their performance
by providing them tools for symbolic operations.

In this case, the billing for the usage continues to be controlled by the client of your choice
(unlike with the Claude Desktop client). But you may still want to use Serena through such an approach,
e.g., for one of the following reasons:

1. You are already using a coding assistant (say Cline or Cursor) and just want to make it more powerful.
2. You are on Linux and don't want to use the [community-created Claude Desktop](https://github.com/aaddrick/claude-desktop-debian).
3. You want tighter integration of Serena into your IDE and don't mind paying for that.

### Agno Agent

Agno is a model-agnostic agent framework that allows you to turn Serena into an agent 
(independent of the MCP technology) with a large number of underlying LLMs. Agno is currently
the simplest way of running Serena in a chat GUI with an LLM of your choice 
(unless you are using a Mac, then you might prefer goose, which requires almost no setup).

While Agno is not yet entirely stable, we chose it, because it comes with its own open-source UI, 
making it easy to directly use the agent using a chat interface.  With Agno, Serena is turned into an agent
(so no longer an MCP Server), so it can be used in programmatic ways (for example for benchmarking or within 
your application).

Here's how it works (see also [Agno's documentation](https://docs.agno.com/introduction/playground)):

1. Download the agent-ui code with npx
   ```shell
   npx create-agent-ui@latest
   ```
   or, alternatively, clone it manually:
   ```shell
   git clone https://github.com/agno-agi/agent-ui.git
   cd agent-ui 
   pnpm install 
   pnpm dev
   ```

2. Install serena with the optional requirements:
   ```shell
   # You can also only select agno,google or agno,anthropic instead of all-extras
   uv pip install --all-extras -r pyproject.toml -e .
   ```
   
3. Copy `.env.example` to `.env` and fill in the API keys for the provider(s) you
   intend to use.

4. Start the agno agent app with
   ```shell
   uv run python scripts/agno_agent.py
   ```
   By default, the script uses Claude as the model, but you can choose any model
   supported by Agno (which is essentially any existing model).

5. In a new terminal, start the agno UI with
   ```shell
   cd agent-ui 
   pnpm dev
   ```
   Connect the UI to the agent you started above and start chatting. You will have
   the same tools as in the MCP server version.


Here is a short demo of Serena performing a small analysis task with the newest Gemini model:

https://github.com/user-attachments/assets/ccfcb968-277d-4ca9-af7f-b84578858c62


⚠️ IMPORTANT: In contrast to the MCP server approach, tool execution in the Agno UI does
not ask for the user's permission. The shell tool is particularly critical, as it can perform arbitrary code execution. 
While we have never encountered any issues with
this in our testing with Claude, allowing this may not be entirely safe. 
You may choose to disable certain tools for your setup in your Serena project's
configuration file (`.yml`).

### Other Agent Frameworks

It should be straightforward to incorporate Serena into any
agent framework (like [pydantic-ai](https://ai.pydantic.dev/), [langgraph](https://langchain-ai.github.io/langgraph/tutorials/introduction/) or others).
Typically, you need only to write an adapter for Serena's tools to the tool representation in the framework of your choice, 
as was done by us for Agno with [SerenaAgnoToolkit](/src/serena/agno.py).


## Detailed Usage and Recommendations

### Tool Execution

Serena combines tools for semantic code retrieval with editing capabilities and shell execution.
Serena's behavior can be further customized through [Modes and Contexts](#modes-and-contexts).
Find the complete list of tools [below](#full-list-of-tools).

The use of all tools is generally recommended, as this allows Serena to provide the most value:
Only by executing shell commands (in particular, tests) can Serena identify and correct mistakes
autonomously.

#### Shell Execution and Editing Tools

However, it should be noted that the `execute_shell_command` tool allows for arbitrary code execution.
When using Serena as an MCP Server, clients will typically ask the user for permission
before executing a tool, so as long as the user inspects execution parameters beforehand,
this should not be a problem.
However, if you have concerns, you can choose to disable certain commands in your project's
.yml configuration file.
If you only want to use Serena purely for analyzing code and suggesting implementations
without modifying the codebase, you can enable read-only mode by setting `read_only: true` in your project configuration file.
This will automatically disable all editing tools and prevent any modifications to your codebase while still
allowing all analysis and exploration capabilities.

In general, be sure to back up your work and use a version control system in order to avoid
losing any work.


### Modes and Contexts

Serena's behavior and toolset can be adjusted using contexts and modes. 
These allow for a high degree of customization to best suit your workflow and the environment Serena is operating in.

#### Contexts

A context defines the general environment in which Serena is operating. 
It influences the initial system prompt and the set of available tools. 
A context is set at startup when launching Serena (e.g., via CLI options for an MCP server or in the agent script) and cannot be changed during an active session.

Serena comes with pre-defined contexts:
*   `desktop-app`: Tailored for use with desktop applications like Claude Desktop. This is the default.
*   `agent`: Designed for scenarios where Serena acts as a more autonomous agent, for example, when used with Agno.
*   `ide-assistant`: Optimized for integration into IDEs like VSCode, Cursor, or Cline, focusing on in-editor coding assistance.
Choose the context that best matches the type of integration you are using.

When launching Serena, specify the context using `--context <context-name>`.  
Note that for cases where parameter lists are specified (e.g. Claude Desktop), you must add two parameters to the list.

#### Modes

Modes further refine Serena's behavior for specific types of tasks or interaction styles. Multiple modes can be active simultaneously, allowing you to combine their effects. Modes influence the system prompt and can also alter the set of available tools by excluding certain ones.

Examples of built-in modes include:
*   `planning`: Focuses Serena on planning and analysis tasks.
*   `editing`: Optimizes Serena for direct code modification tasks.
*   `interactive`: Suitable for a conversational, back-and-forth interaction style.
*   `one-shot`: Configures Serena for tasks that should be completed in a single response, often used with `planning` for generating reports or initial plans.
*   `no-onboarding`: Skips the initial onboarding process if it's not needed for a particular session.
*   `onboarding`: (Usually triggered automatically) Focuses on the project onboarding process.

Modes can be set at startup (similar to contexts) but can also be *switched dynamically* during a session. You can instruct the LLM to use the `switch_modes` tool to activate a different set of modes (e.g., ""switch to planning and one-shot modes"").

When launching Serena, specify modes using `--mode <mode-name>`; multiple modes can be specified, e.g. `--mode planning --mode no-onboarding`.

:warning: **Mode Compatibility**: While you can combine modes, some may be semantically incompatible (e.g., `interactive` and `one-shot`). Serena currently does not prevent incompatible combinations; it is up to the user to choose sensible mode configurations.

#### Customization

You can create your own contexts and modes to precisely tailor Serena to your needs in two ways:
*  **Adding to Serena's configuration directory**: Create new `.yml` files in the `config/contexts/` or `config/modes/` directories within your local Serena repository. These custom contexts/modes will be automatically registered and available for use by their name (filename without the `.yml` extension). They will also appear in listings of available contexts/modes.
*  **Using external YAML files**: When starting Serena, you can provide an absolute path to a custom `.yml` file for a context or mode.

A context or mode YAML file typically defines:
*   `name`: (Optional if filename is used) The name of the context/mode.
*   `prompt`: A string that will be incorporated into Serena's system prompt.
*   `description`: (Optional) A brief description.
*   `excluded_tools`: A list of tool names (strings) to disable when this context/mode is active.

This customization allows for deep integration and adaptation of Serena to specific project requirements or personal preferences.


### Onboarding and Memories

By default, Serena will perform an **onboarding process** when
it is started for the first time for a project.
The goal of the onboarding is for Serena to get familiar with the project
and to store memories, which it can then draw upon in future interactions.
If an LLM should fail to complete the onboarding and does not actually write the
respective memories to disk, you may need to ask it to do so explicitly.

The onboarding will usually read a lot of content from the project, thus filling
up the context. It can therefore be advisable to switch to another conversation
once the onboarding is complete.
After the onboarding, we recommend that you have a quick look at the memories and,
if necessary, edit them or add additional ones.

**Memories** are files stored in `.serena/memories/` in the project directory,
which the agent can choose to read in subsequent interactions.
Feel free to read and adjust them as needed; you can also add new ones manually.
Every file in the `.serena/memories/` directory is a memory file.
Whenever Serena starts working on a project, the list of memories is
provided, and the agent can decide to read them.
We found that memories can significantly improve the user experience with Serena.


### Prepare Your Project

#### Structure Your Codebase

Serena uses the code structure for finding, reading and editing code. This means that it will
work well with well-structured code but may perform poorly on fully unstructured one (like a ""God class""
with enormous, non-modular functions).  
Furthermore, for languages that are not statically typed, type annotations are highly beneficial.

#### Start from a Clean State

It is best to start a code generation task from a clean git state. Not only will
this make it easier for you to inspect the changes, but also the model itself will
have a chance of seeing what it has changed by calling `git diff` and thereby
correct itself or continue working in a followup conversation if needed.

:warning: **Important**: since Serena will write to files using the system-native line endings
and it might want to look at the git diff, it is important to
set `git config core.autocrlf` to `true` on Windows.
With `git config core.autocrlf` set to `false` on Windows, you may end up with huge diffs
only due to line endings. It is generally a good idea to globally enable this git setting on Windows:

```shell
git config --global core.autocrlf true
```

#### Logging, Linting, and Automated Tests

Serena can successfully complete tasks in an _agent loop_, where it iteratively
acquires information, performs actions, and reflects on the results.
However, Serena cannot use a debugger; it must rely on the results of program executions,
linting results, and test results to assess the correctness of its actions.
Therefore, software that is designed to meaningful interpretable outputs (e.g. log messages)
and that has a good test coverage is much easier to work with for Serena.

We generally recommend to start an editing task from a state where all linting checks and tests pass.

### Prompting Strategies

We found that it is often a good idea to spend some time conceptualizing and planning a task
before actually implementing it, especially for non-trivial task. This helps both in achieving
better results and in increasing the feeling of control and staying in the loop. You can
make a detailed plan in one session, where Serena may read a lot of your code to build up the context,
and then continue with the implementation in another (potentially after creating suitable memories).

### Potential Issues in Code Editing

In our experience, LLMs are bad at counting, i.e. they have problems
inserting blocks of code in the right place. Most editing operations can be performed
at the symbolic level, allowing this problem is overcome. However, sometimes,
line-level insertions are useful.

Serena is instructed to double-check the line numbers and any code blocks that it will
edit, but you may find it useful to explicitly tell it how to edit code if you run into
problems.  
We are working on making Serena's editing capabilities more robust.

### Running Out of Context

For long and complicated tasks, or tasks where Serena has read a lot of content, you
may come close to the limits of context tokens. In that case, it is often a good idea to continue
in a new conversation. Serena has a dedicated tool to create a summary of the current state
of the progress and all relevant info for continuing it. You can request to create this summary and
write it to a memory. Then, in a new conversation, you can just ask Serena to read the memory and
continue with the task. In our experience, this worked really well. On the up-side, since in a 
single session there is no summarization involved, Serena does not usually get lost (unlike some
other agents that summarize under the hood), and it is also instructed to occasionally check whether
it's on the right track.

Moreover, Serena is instructed to be frugal with context 
(e.g., to not read bodies of code symbols unnecessarily),
but we found that Claude is not always very good in being frugal (Gemini seemed better at it).
You can explicitly instruct it to not read the bodies if you know that it's not needed.

### Combining Serena with Other MCP Servers

When using Serena through an MCP Client, you can use it together with other MCP servers.
However, beware of tool name collisions! See info on that above.

Currently, there is a collision with the popular Filesystem MCP Server. Since Serena also provides
filesystem operations, there is likely no need to ever enable these two simultaneously.

### Serena's Logs: The Dashboard and GUI Tool

Serena provides two convenient ways of accessing the logs of the current session:

  * via the **web-based dashboard** (enabled by default)
    
    This is supported on all platforms.
    By default, it will be accessible at `http://localhost:24282/dashboard/index.html`, 
    but a higher port may be used if the default port is unavailable/multiple instances are running.
    
  * via the **GUI tool** (disabled by default)

    This is mainly supported on Windows, but it may also work on Linux; macOS is unsupported.

Both can be enabled or disabled in Serena's configuration file (`serena_config.yml`, see above).
If enabled, they will automatically be opened as soon as the Serena agent/MCP server is started.

In addition to viewing logs, both tools allow to shut down the Serena agent.
This function is provided, because clients like Claude Desktop may fail to terminate the MCP server subprocess 
when they themselves are closed.

### Troubleshooting

Support for MCP Servers in Claude Desktop and the various MCP Server SDKs are relatively new developments and may display instabilities.

The working configuration of an MCP server may vary from platform to
platform and from client to client. We recommend always using absolute paths, as relative paths may be sources of
errors. The language server is running in a separate sub-process and is called with asyncio – sometimes
a client may make it crash. If you have Serena's log window enabled, and it disappears, you'll know what happened.

Some clients (like goose) may not properly terminate MCP servers,
look out for hanging python processes and terminate them
manually, if needed.

## Comparison with Other Coding Agents

To our knowledge, Serena is the first fully-featured coding agent where the
entire functionality
is available through an MCP server, thus not requiring API keys or
subscriptions.

### Subscription-Based Coding Agents

The most prominent subscription-based coding agents are parts of IDEs like
Windsurf, Cursor and VSCode.
Serena's functionality is similar to Cursor's Agent, Windsurf's Cascade or
VSCode's
upcoming [agent mode](https://code.visualstudio.com/blogs/2025/02/24/introducing-copilot-agent-mode).

Serena has the advantage of not requiring a subscription.
A potential disadvantage is that it
is not directly integrated into an IDE, so the inspection of newly written code
is not as seamless.

More technical differences are:
* Serena is not bound to a specific IDE.
  Serena's MCP server can be used with any MCP client (including some IDEs),
  and the Agno-based agent provides additional ways of applying its functionality.
* Serena is not bound to a specific large language model or API.
* Serena navigates and edits code using a language server, so it has a symbolic
  understanding of the code.
  IDE-based tools often use a RAG-based or purely text-based approach, which is often
  less powerful, especially for large codebases.
* Serena is open-source and has a small codebase, so it can be easily extended
  and modified.

### API-Based Coding Agents

An alternative to subscription-based agents are API-based agents like Claude
Code, Cline, Aider, Roo Code and others, where the usage costs map directly
to the API costs of the underlying LLM.
Some of them (like Cline) can even be included in IDEs as an extension.
They are often very powerful and their main downside are the (potentially very
high) API costs.

Serena itself can be used as an API-based agent (see the section on Agno above).
We have not yet written a CLI tool or a
dedicated IDE extension for Serena (and there is probably no need for the latter, as
Serena can already be used with any IDE that supports MCP servers).
If there is demand for a Serena as a CLI tool like Claude Code, we will
consider writing one.

The main difference between Serena and other API-based agents is that Serena can
also be used as an MCP server, thus not requiring
an API key and bypassing the API costs. This is a unique feature of Serena.

### Other MCP-Based Coding Agents

There are other MCP servers designed for coding, like [DesktopCommander](https://github.com/wonderwhy-er/DesktopCommanderMCP) and
[codemcp](https://github.com/ezyang/codemcp).
However, to the best of our knowledge, none of them provide semantic code
retrieval and editing tools; they rely purely on text-based analysis.
It is the integration of language servers and the MCP that makes Serena unique
and so powerful for challenging coding tasks, especially in the context of
larger codebases.


## Acknowledgements

We built Serena on top of multiple existing open-source technologies, the most important ones being:

1. [multilspy](https://github.com/microsoft/multilspy).
   A library which wraps language server implementations and adapts them for interaction via Python
   and which provided the basis for our library Solid-LSP (src/solidlsp). 
   Solid-LSP provides pure synchronous LSP calls and extends the original library with the symbolic logic 
   that Serena required.
2. [Python MCP SDK](https://github.com/modelcontextprotocol/python-sdk)
3. [Agno](https://github.com/agno-agi/agno) and
   the associated [agent-ui](https://github.com/agno-agi/agent-ui),
   which we use to allow Serena to work with any model, beyond the ones
   supporting the MCP.
4. All the language servers that we use through Solid-LSP.

Without these projects, Serena would not have been possible (or would have been significantly more difficult to build).


## Customizing and Extending Serena

It is straightforward to extend Serena's AI functionality with your own ideas. 
Simply implement a new tool by subclassing 
`serena.agent.Tool` and implement the `apply` method with a signature
that matches the tool's requirements. 
Once implemented, `SerenaAgent` will automatically have access to the new tool.

It is also relatively straightforward to add [support for a new programming language](/CONTRIBUTING.md#adding-a-new-supported-language). 

We look forward to seeing what the community will come up with! 
For details on contributing, see [here](/CONTRIBUTING.md).

## Full List of Tools

Here is the full list of Serena's tools with a short description (output of `uv run serena-list-tools`):

 * `activate_project`: Activates a project by name.
 * `check_onboarding_performed`: Checks whether project onboarding was already performed.
 * `create_text_file`: Creates/overwrites a file in the project directory.
 * `delete_lines`: Deletes a range of lines within a file.
 * `delete_memory`: Deletes a memory from Serena's project-specific memory store.
 * `execute_shell_command`: Executes a shell command.
 * `find_referencing_code_snippets`: Finds code snippets in which the symbol at the given location is referenced.
 * `find_referencing_symbols`: Finds symbols that reference the symbol at the given location (optionally filtered by type).
 * `find_symbol`: Performs a global (or local) search for symbols with/containing a given name/substring (optionally filtered by type).
 * `get_active_project`: Gets the name of the currently active project (if any) and lists existing projects
 * `get_current_config`: Prints the current configuration of the agent, including the active modes, tools, and context.
 * `get_symhttps://github.com/horw/esp-mcp,horw/esp-mcp,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)

### Goal
The goal of this MCP is to:
- Consolidate ESP-IDF and related project commands in one place.
- Simplify getting started using only LLM communication.

### How to contribute to the project

Simply find a command that is missing from this MCP and create a PR for it!

If you want someone to help you with this implementation, just open an issue.


### Notice
This project is currently a **Proof of Concept (PoC)** for an MCP server tailored for ESP-IDF workflows. 

**Current Capabilities:**
*   Supports basic ESP-IDF project build commands.
*   Flash built firmware to connected ESP devices with optional port specification.
*   Includes experimental support for automatic issue fixing based on build logs.

**Vision & Future Work:**
The long-term vision is to expand this MCP into a comprehensive toolkit for interacting with embedded devices, potentially integrating with home assistant platforms, and streamlining documentation access for ESP-IDF and related technologies. 

We envision features such as:
*   Broader ESP-IDF command support (e.g., `monitor`, `menuconfig` interaction if feasible).
*   Device management and information retrieval.
*   Integration with other embedded development tools and platforms.

Your ideas and contributions are welcome! Please feel free to discuss them by opening an issue.


### Install  

First, clone this MCP repository:  

```bash
<NAME_EMAIL>:horw/esp-mcp.git
```  

Then, configure it in your chatbot. 

The JSON snippet below is an example of how you might configure this `esp-mcp` server within a chatbot or an agent system that supports the Model Context Protocol (MCP). The exact configuration steps and format may vary depending on the specific chatbot system you are using. Refer to your chatbot's documentation for details on how to integrate MCP servers.

```json
{
    ""mcpServers"": {
        ""esp-run"": { // ""esp-run"" is an arbitrary name you can assign to this server configuration.
            ""command"": ""<path_to_uv_or_python_executable>"",
            ""args"": [
                ""--directory"",
                ""<path_to_cloned_esp-mcp_repository>"", // e.g., /path/to/your/cloned/esp-mcp
                ""run"",
                ""main.py"" // If using python directly, this might be just ""main.py"" and `command` would be your python interpreter
            ],
            ""env"": {
                ""IDF_PATH"": ""<path_to_your_esp-idf_directory>"" // e.g., ~/esp/esp-idf or C:\\Espressif\\frameworks\\esp-idf
            }
        }
    }
}
```

A few notes on the configuration:

*   **`command`**: This should be the full path to your `uv` executable if you are using it, or your Python interpreter (e.g., `/usr/bin/python3` or `C:\\Python39\\python.exe`) if you plan to run `main.py` directly.
*   **`args`**:
    *   The first argument to `--directory` should be the absolute path to where you cloned the `esp-mcp` repository.
    *   If you're using `uv`, the arguments `run main.py` are appropriate. If you're using Python directly, you might only need `main.py` in the `args` list, and ensure your `command` points to the Python executable.
*   **`IDF_PATH`**: This environment variable must point to the root directory of your ESP-IDF installation. ESP-IDF is Espressif's official IoT Development Framework. If you haven't installed it, please refer to the [official ESP-IDF documentation](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/index.html) for installation instructions.

### Usage

Once the `esp-mcp` server is configured and running, your LLM or chatbot can interact with it using the tools defined ihttps://github.com/horw/esp-mcp,horw/esp-mcp,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)

### Goal
The goal of this MCP is to:
- Consolidate ESP-IDF and related project commands in one place.
- Simplify getting started using only LLM communication.

### How to contribute to the project

Simply find a command that is missing from this MCP and create a PR for it!

If you want someone to help you with this implementation, just open an issue.


### Notice
This project is currently a **Proof of Concept (PoC)** for an MCP server tailored for ESP-IDF workflows. 

**Current Capabilities:**
*   Supports basic ESP-IDF project build commands.
*   Flash built firmware to connected ESP devices with optional port specification.
*   Includes experimental support for automatic issue fixing based on build logs.

**Vision & Future Work:**
The long-term vision is to expand this MCP into a comprehensive toolkit for interacting with embedded devices, potentially integrating with home assistant platforms, and streamlining documentation access for ESP-IDF and related technologies. 

We envision features such as:
*   Broader ESP-IDF command support (e.g., `monitor`, `menuconfig` interaction if feasible).
*   Device management and information retrieval.
*   Integration with other embedded development tools and platforms.

Your ideas and contributions are welcome! Please feel free to discuss them by opening an issue.


### Install  

First, clone this MCP repository:  

```bash
<NAME_EMAIL>:horw/esp-mcp.git
```  

Then, configure it in your chatbot. 

The JSON snippet below is an example of how you might configure this `esp-mcp` server within a chatbot or an agent system that supports the Model Context Protocol (MCP). The exact configuration steps and format may vary depending on the specific chatbot system you are using. Refer to your chatbot's documentation for details on how to integrate MCP servers.

```json
{
    ""mcpServers"": {
        ""esp-run"": { // ""esp-run"" is an arbitrary name you can assign to this server configuration.
            ""command"": ""<path_to_uv_or_python_executable>"",
            ""args"": [
                ""--directory"",
                ""<path_to_cloned_esp-mcp_repository>"", // e.g., /path/to/your/cloned/esp-mcp
                ""run"",
                ""main.py"" // If using python directly, this might be just ""main.py"" and `command` would be your python interpreter
            ],
            ""env"": {
                ""IDF_PATH"": ""<path_to_your_esp-idf_directory>"" // e.g., ~/esp/esp-idf or C:\\Espressif\\frameworks\\esp-idf
            }
        }
    }
}
```

A few notes on the configuration:

*   **`command`**: This should be the full path to your `uv` executable if you are using it, or your Python interpreter (e.g., `/usr/bin/python3` or `C:\\Python39\\python.exe`) if you plan to run `main.py` directly.
*   **`args`**:
    *   The first argument to `--directory` should be the absolute path to where you cloned the `esp-mcp` repository.
    *   If you're using `uv`, the arguments `run main.py` are appropriate. If you're using Python directly, you might only need `main.py` in the `args` list, and ensure your `command` points to the Python executable.
*   **`IDF_PATH`**: This environment variable must point to the root directory of your ESP-IDF installation. ESP-IDF is Espressif's official IoT Development Framework. If you haven't installed it, please refer to the [official ESP-IDF documentation](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/index.html) for installation instructions.

### Usage

Once the `esp-mcp` server is configured and running, your LLM or chatbot can interact with it using the tools defined in this MCP. For example, you could ask your chatbot to:

*   ""Build the project located at `/path/to/my/esp-project` using the `esp-mcp`.""
*   ""Clean the build files for the ESP32 project in the `examples/hello_world` directory.""
*   ""Flash the firmware to my connected ESP32 device for the project in `my_app`.""

The MCP server will then execute the corresponding ESP-IDF commands (like `idf.py build`, `idf.py fullclean`, `idf.py flash`) based on the tools implemented in `main.py`.

The `result.gif` below shows an example interaction:

![Result](./result.gif)


### Examples 


1. Build and Flash
<img src=""./examples/build-flash.png"">




","Star
 62",2025-06-24T08:43:21.243913,esp-mcp - MCP Server | Model Context Protocol Integration,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)

### Goal
The goal of this MCP is to:...","['mcp server', 'model context protocol', 'ai integration', 'esp-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'esp-mcp', 'description': '[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/horw-esp-mcp-badge.png)](https://mseep.ai/app/horw-esp-mcp)\n\n### Goal\nThe goal of this MCP is to:...', 'url': 'https://github.com/horw/esp-mcp', 'codeRepository': 'https://github.com/horw/esp-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Development,IoT,,"Examples, Goal, Install, Notice, Usage","{
    ""mcpServers"": {
        ""esp-run"": { // ""esp-run"" is an arbitrary name you can assign to this server configuration.
            ""command"": ""<path_to_uv_or_python_executable>"",
            ""args"": [
                ""--directory"",
                ""<path_to_cloned_esp-mcp_repository>"", // e.g., /path/to/your/cloned/esp-mcp
                ""run"",
                ""main.py"" // If using python directly, this might be just ""main.py"" and `command` would be your python interpreter
            ],
   ",,,2025-06-23,horw,The goal of this MCP is to:
https://github.com/pab1it0/chess-mcp,pab1it0/chess-mcp,"# Chess.com MCP Server

A [Model Context Protocol][mcp] (MCP) server for Chess.com's Published Data API.

This provides access to Chess.com player data, game records, and other public information through standardized MCP interfaces, allowing AI assistants to search and analyze chess information.

https://github.com/user-attachments/assets/3b33361b-b604-465c-9f6a-3699b6907757

[mcp]: https://modelcontextprotocol.io/introduction/introduction

## Features

- [x] Access player profiles, stats, and game records
- [x] Search games by date and player
- [x] Check player online status
- [x] Get information about clubs and titled players
- [x] No authentication required (uses Chess.com's public API)
- [x] Docker containerization support
- [x] Provide interactive tools for AI assistants

The list of tools is configurable, so you can choose which tools you want to make available to the MCP client.

## Usage

### Docker (Recommended)

The easiest way to run chess-mcp with [Claude Desktop](https://claude.ai/desktop) is using Docker. If you don't have Docker installed, you can get it from [Docker's official website](https://www.docker.com/get-started/).


Edit your Claude Desktop config file:
* Mac: `~/Library/Application Support/Claude/claude_desktop_config.json`
* Windows: `%APPDATA%/Claude/claude_desktop_config.json`
* Linux: `~/.config/Claude/claudehttps://github.com/ac3xx/mcp-servers-kagi,ac3xx/mcp-servers-kagi,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integration

This is a TypeScript-based MCP server that integrates the Kagi Search API. It demonstrates core MCP concepts by providing:

- Tools for performing web searches and other operations using Kagi's API (currently in private beta)

## Features

### Implemented Tools
- `kagi_search` - Perform web searches using Kagi
  - Takes a query string and optional limit as parameters
  - Returns search results from Kagi's API

### Planned Tools (Not Yet Implemented)
- `kagi_summarize` - Generate summaries of web pages or text
- `kagi_fastgpt` - Get quick responses using Kagi's FastGPT
- `kagi_enrich` - Fetch enriched news results on specific topics

## Development

Install dependencies:
```bash
npm install
```

Build the server:
```bash
npm run build
```

For development with auto-rebuild:
```bash
npm run watch
```

## Environment Setup

Create a `.env` file in the root directory with your Kagi API key:

```
KAGI_API_KEY=your_api_key_here
```

Make sure to add `.env` to your `.gitignore` file to keep your API key secure.

## Installation

### Installing via Smithery

To install Kagi Server for Claude Desktop automatically via [Smithery](https://smithery.ai/protocol/kagi-server):

```bash
npx @smithery/cli install kagi-server --client claude
```

To use with Claude Desktop, add the server config:

On MacOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
        ""KAGI_API_KEY"": ""your_api_key_here""
      }
    }
  }
}
```

### Debugging

Since MCP servers communicate over stdio, debugging can be challenging. We recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
npm run inspector
```

The Inspector will provide a URL to access debugging tools in your browser.

## Usage

Once the server is running and connected to Claude Desktop, you can use it to perform web searches. For example:

1. Ask Claude: ""Can you search for information about the latest advancements in quantum computing?""
2. Claude will use the `kagi_search` tool to fetch results from Kagi's API.
3. Claude will then summarize or analyze the search results for you.

Note: The planned tools (summarize, fastgpt, enrich) are not yet implemented and cannot be used.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. Some areas for contribution include:

- Implementing the planned tools (summarize, fastgpt, enrich)
- Improving error handling and input validation
- Enhancing documentation and usage examples

## License

This project is licensed under the MIT License.

## Roadmap

- Implement `kagi_summarize` tool for webpage and text summarization
- Implement `kagi_fastgpt` tool for quick responses
- Implement `kagi_enrich` tool for fetching enriched news results
- Improve error handling and add more robust input validation
- Add more comprehensive usage examples and documentation
- Publish the package to npm for easy installation and use with Claude Desktop and npx","Star
 33",2025-06-24T08:43:21.243899,mcp-servers-kagi - MCP Server | Model Context Protocol Integration,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integr...","['mcp server', 'model context protocol', 'ai integration', 'mcp-servers-kagi']","{'@context': 'https://schema.org', '@type': 'Softhttps://github.com/ac3xx/mcp-servers-kagi,ac3xx/mcp-servers-kagi,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integration

This is a TypeScript-based MCP server that integrates the Kagi Search API. It demonstrates core MCP concepts by providing:

- Tools for performing web searches and other operations using Kagi's API (currently in private beta)

## Features

### Implemented Tools
- `kagi_search` - Perform web searches using Kagi
  - Takes a query string and optional limit as parameters
  - Returns search results from Kagi's API

### Planned Tools (Not Yet Implemented)
- `kagi_summarize` - Generate summaries of web pages or text
- `kagi_fastgpt` - Get quick responses using Kagi's FastGPT
- `kagi_enrich` - Fetch enriched news results on specific topics

## Development

Install dependencies:
```bash
npm install
```

Build the server:
```bash
npm run build
```

For development with auto-rebuild:
```bash
npm run watch
```

## Environment Setup

Create a `.env` file in the root directory with your Kagi API key:

```
KAGI_API_KEY=your_api_key_here
```

Make sure to add `.env` to your `.gitignore` file to keep your API key secure.

## Installation

### Installing via Smithery

To install Kagi Server for Claude Desktop automatically via [Smithery](https://smithery.ai/protocol/kagi-server):

```bash
npx @smithery/cli install kagi-server --client claude
```

To use with Claude Desktop, add the server config:

On MacOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
        ""KAGI_API_KEY"": ""your_api_key_here""
      }
    }
  }
}
```

### Debugging

Since MCP servers communicate over stdio, debugging can be challenging. We recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
npm run inspector
```

The Inspector will provide a URL to access debugging tools in your browser.

## Usage

Once the server is running and connected to Claude Desktop, you can use it to perform web searches. For example:

1. Ask Claude: ""Can you search for information about the latest advancements in quantum computing?""
2. Claude will use the `kagi_search` tool to fetch results from Kagi's API.
3. Claude will then summarize or analyze the search results for you.

Note: The planned tools (summarize, fastgpt, enrich) are not yet implemented and cannot be used.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. Some areas for contribution include:

- Implementing the planned tools (summarize, fastgpt, enrich)
- Improving error handling and input validation
- Enhancing documentation and usage examples

## License

This project is licensed under the MIT License.

## Roadmap

- Implement `kagi_summarize` tool for webpage and text summarization
- Implement `kagi_fastgpt` tool for quick responses
- Implement `kagi_enrich` tool for fetching enriched news results
- Improve error handling and add more robust input validation
- Add more comprehensive usage examples and documentation
- Publish the package to npm for easy installation and use with Claude Desktop and npx","Star
 33",2025-06-24T08:43:21.243899,mcp-servers-kagi - MCP Server | Model Context Protocol Integration,"# kagi-server MCP Server

[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)
MCP server for Kagi API integr...","['mcp server', 'model context protocol', 'ai integration', 'mcp-servers-kagi']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-servers-kagi', 'description': '# kagi-server MCP Server\n\n[![smithery badge](https://smithery.ai/badge/kagi-server)](https://smithery.ai/protocol/kagi-server)\nMCP server for Kagi API integr...', 'url': 'https://github.com/ac3xx/mcp-servers-kagi', 'codeRepository': 'https://github.com/ac3xx/mcp-servers-kagi', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,AI Tools,Search Engine,npm install,Debugging,"{
  ""mcpServers"": {
    ""kagi-server"": {
      ""command"": ""/path/to/kagi-server/build/index.js"",
      ""env"": {
        ""KAGI_API_KEY"": ""your_api_key_here""
      }
    }
  }
}",,MIT License,2025-06-03,ac3xx,MCP server for Kagi API integration
https://github.com/0xshellming/mcp-summarizer,0xshellming/mcp-summarizer,"# MCP Content Summarizer Server

A Model Context Protocol (MCP) server that provides intelligent summarization capabilities for various types of content using Google's Gemini 1.5 Pro model. This server can help you generate concise summaries while maintaining key information from different content formats.

<a href=""https://3min.top""><img width=""380"" height=""200"" src=""/public/imgs/section1_en.jpg"" alt=""MCP Content Summarizer Server"" /></a>

## Powered by 3MinTop

The summarization service is powered by [3MinTop](https://3min.top), an AI-powered reading tool that helps you understand a chapter's contenthttps://github.com/ahnlabio/bicscan-mcp,ahnlabio/bicscan-mcp,"# BICScan MCP Server

A powerful and efficient Blockchain address risk scoring API MCP Server, leveraging the BICScan API to provide comprehensive risk assessments and asset information for blockchain addresses, domains, and decentralized applications (dApps).

🎉 We're listed on https://github.com/modelcontextprotocol/servers for official integration 🎉


https://github.com/user-attachments/assets/f9425429-1cb1-4508-b962-81351075258b

## Key Features
- **Risk Scoring**: Obtain risk scores for various blockchain entities, including crypto addresses, domain names, and decentralized application URLs, with scores ranging from 0 to 100, where 100 indicates high risk.
- **Asset Information**: Retrieve detailed asset holdings for specified crypto addresses, including cryptocurrencies and tokens, with support for multiple blockchain networks.
- **Real-time Scanning**: Utilize the BICScan API to perform real-time scans and receive up-to-date information on potential risks and asset holdings.
- **Secure and Reliable**: Built with robust error handling and logging to ensure secure and reliable operations.

## Example Output

## How to use.

You con either use Python with `uv` or `docker` depending on your preference.

Depending on your environment, you can choose to use either `uv`, `docker`, or `uvx`.

### 1. Running with `uv`

#### 1-1. Requirements
1. Python 3.10 or higher
2. uv 0.6.x
3. git

#### 1.2. Clone the repository
```sh
git clone https://github.com/ahnlabio/bicscan-mcp
```

#### 1.3. Config `claude_desktop_config.json`

Append following to `claude_desktop_config.json`.

Make sure to replace:
 - `YOUR_BICSCAN_REPO_DIR_HERE`: to something like `C:\\Users\\<USER>\\repo\\bicscan-mcp` or `/home/<USER>/repo/bicscan-mcp` similarly.
 - `YOUR_BICSCAN_API_KEY_HERE`: to free API key can be obtained from https://bicscan.io (details below)

```json
{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""YOUR_BICSCAN_REPO_DIR_HERE"",
        ""run"",
        ""bicscan-mcp""
      ],
      ""env"": {
        ""BICSCAN_API_KEY"": ""YOUR_BICSCAN_API_KEY_HERE""
      }
    }
  }
}
```

### 2. Running with `Docker`

#### 2.1. Requirements
1. Docker environment

#### 2.2. Clone the repository
```sh
git clone https://github.com/ahnlabio/bicscan-mcp
```

#### 2.3. Build Docker image.

Just run `make` in the repository directory to build docker image.

#### 2.4. Config
Append following to `claude_desktop_config.json`

Make sure to replace:
 - `YOUR_BICSCAN_API_KEY_HERE` to API key obtained from https://bicscan.io (details below)

```json
{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""--rm"",
        ""--interactive"",
        ""--env"", ""BICSCAN_API_KEY=YOUR_BICSCAN_API_KEY_HERE"",
        ""bicscan-mcp""
      ]
    }
  }
}
```

### 3. Running with `uvx`

#### 3.1. Requirements
1. Python 3.10 or higher
2. uv 0.6.x
3. git

#### 3.2. Config `claude_desktop_config.json`

Append following to `claude_desktop_config.json`.

Make sure to replace:
 - `YOUR_BICSCAN_API_KEY_HERE`: to free API key can be obtained from https://bicscan.io (details below)

```json
{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""uvx"",
      ""args"": [
        ""--from"",
        ""git+https://github.com/ahnlabio/bicscan-mcp"",
        ""bicscan-mcp""
      ],
      ""env"": {
        ""BICSCAN_API_KEY"": ""YOUR_BICSCAN_API_KEY_HERE""
      }
    }
  }
}
```

## How to obtain Free BICScan API Key?

1. Visit `https://bicscan.io` and register.
2. Go to profile and create ""Create App"https://github.com/ahnlabio/bicscan-mcp,ahnlabio/bicscan-mcp,"# BICScan MCP Server

A powerful and efficient Blockchain address risk scoring API MCP Server, leveraging the BICScan API to provide comprehensive risk assessments and asset information for blockchain addresses, domains, and decentralized applications (dApps).

🎉 We're listed on https://github.com/modelcontextprotocol/servers for official integration 🎉


https://github.com/user-attachments/assets/f9425429-1cb1-4508-b962-81351075258b

## Key Features
- **Risk Scoring**: Obtain risk scores for various blockchain entities, including crypto addresses, domain names, and decentralized application URLs, with scores ranging from 0 to 100, where 100 indicates high risk.
- **Asset Information**: Retrieve detailed asset holdings for specified crypto addresses, including cryptocurrencies and tokens, with support for multiple blockchain networks.
- **Real-time Scanning**: Utilize the BICScan API to perform real-time scans and receive up-to-date information on potential risks and asset holdings.
- **Secure and Reliable**: Built with robust error handling and logging to ensure secure and reliable operations.

## Example Output

## How to use.

You con either use Python with `uv` or `docker` depending on your preference.

Depending on your environment, you can choose to use either `uv`, `docker`, or `uvx`.

### 1. Running with `uv`

#### 1-1. Requirements
1. Python 3.10 or higher
2. uv 0.6.x
3. git

#### 1.2. Clone the repository
```sh
git clone https://github.com/ahnlabio/bicscan-mcp
```

#### 1.3. Config `claude_desktop_config.json`

Append following to `claude_desktop_config.json`.

Make sure to replace:
 - `YOUR_BICSCAN_REPO_DIR_HERE`: to something like `C:\\Users\\<USER>\\repo\\bicscan-mcp` or `/home/<USER>/repo/bicscan-mcp` similarly.
 - `YOUR_BICSCAN_API_KEY_HERE`: to free API key can be obtained from https://bicscan.io (details below)

```json
{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""YOUR_BICSCAN_REPO_DIR_HERE"",
        ""run"",
        ""bicscan-mcp""
      ],
      ""env"": {
        ""BICSCAN_API_KEY"": ""YOUR_BICSCAN_API_KEY_HERE""
      }
    }
  }
}
```

### 2. Running with `Docker`

#### 2.1. Requirements
1. Docker environment

#### 2.2. Clone the repository
```sh
git clone https://github.com/ahnlabio/bicscan-mcp
```

#### 2.3. Build Docker image.

Just run `make` in the repository directory to build docker image.

#### 2.4. Config
Append following to `claude_desktop_config.json`

Make sure to replace:
 - `YOUR_BICSCAN_API_KEY_HERE` to API key obtained from https://bicscan.io (details below)

```json
{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""--rm"",
        ""--interactive"",
        ""--env"", ""BICSCAN_API_KEY=YOUR_BICSCAN_API_KEY_HERE"",
        ""bicscan-mcp""
      ]
    }
  }
}
```

### 3. Running with `uvx`

#### 3.1. Requirements
1. Python 3.10 or higher
2. uv 0.6.x
3. git

#### 3.2. Config `claude_desktop_config.json`

Append following to `claude_desktop_config.json`.

Make sure to replace:
 - `YOUR_BICSCAN_API_KEY_HERE`: to free API key can be obtained from https://bicscan.io (details below)

```json
{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""uvx"",
      ""args"": [
        ""--from"",
        ""git+https://github.com/ahnlabio/bicscan-mcp"",
        ""bicscan-mcp""
      ],
      ""env"": {
        ""BICSCAN_API_KEY"": ""YOUR_BICSCAN_API_KEY_HERE""
      }
    }
  }
}
```

## How to obtain Free BICScan API Key?

1. Visit `https://bicscan.io` and register.
2. Go to profile and create ""Create App""
3. Enter name and description on your choice.
4. Replace `YOUR_BICSCAN_API_KEY_HERE` part from above config to your newly obtained key.
5. restart the Claude Desktop.
","Star
 4",2025-06-24T08:43:21.243886,bicscan-mcp - MCP Server | Model Context Protocol Integration,"# BICScan MCP Server

A powerful and efficient Blockchain address risk scoring API MCP Server, leveraging the BICScan API to provide comprehensive risk asses...","['mcp server', 'model context protocol', 'ai integration', 'bicscan-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'bicscan-mcp', 'description': '# BICScan MCP Server\n\nA powerful and efficient Blockchain address risk scoring API MCP Server, leveraging the BICScan API to provide comprehensive risk asses...', 'url': 'https://github.com/ahnlabio/bicscan-mcp', 'codeRepository': 'https://github.com/ahnlabio/bicscan-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,AI Tools,Blockchain,,,"{
  ""mcpServers"": {
    ... some other mcp servers ...,
    ""bicscan"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""YOUR_BICSCAN_REPO_DIR_HERE"",
        ""run"",
        ""bicscan-mcp""
      ],
      ""env"": {
        ""BICSCAN_API_KEY"": ""YOUR_BICSCAN_API_KEY_HERE""
      }
    }
  }
}",,MIT License,2025-06-18,ahnlabio,"A powerful and efficient Blockchain address risk scoring API MCP Server, leveraging the BICScan API to provide comprehensive risk assessments and asset information for blockchain addresses, domains, a..."
https://github.com/MarketplaceAdPros/amazon-ads-mcp-server,MarketplaceAdPros/amazon-ads-mcp-server,"# amazon-ads-mcp-server

Connect to your Amazon Advertising Data by integrating your account with [MarketplaceAdPros](https://marketplaceadpros.com).

Provides access to:

- Advertising Resources in Sponsored Products, Sponsored Brands and Sponsored Display, like Campaigns, Ad Groups, Keywords, Product Ads, Targeting
- Reports and ability to query them with plain english.
- Marketplace Ad Pros Recommendations, Experiments and more with purchased subscription plan

Also available as a Streamable HTTP MCP Server by connecting to `https://app.marketplaceadpros.com/mcp`

## Inhttps://github.com/Dumpling-AI/mcp-server-dumplingai,Dumpling-AI/mcp-server-dumplingai,"# Dumpling AI MCP Server

A Model Context Protocol (MCP) server implementation that integrates with Dumpling AI for data scraping, content processing, knowledge management, AI agents, and code execution capabilities.

[![smithery badge](https://smithery.ai/badge/@Dumpling-AI/mcp-server-dumplingai)](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai)

## Features

- Complete integration with all Dumpling AI API endpoints
- Data APIs for YouTube transcripts, search, autocomplete, maps, places, news, and reviews
- Web scraping with support for scraping, crawling, screenshots, and structured data extraction
- Document conversion tools for text extraction, PDF operations, video processing
- Extract data from documents, images, audio, and video
- AI capabilities including agent completions, knowledge base management, and image generation
- Developer tools for running JavaScript and Python code in a secure environment
- Automatic error handling and detailed response formatting

## Installation

### Installing via Smithery

To install mcp-server-dumplingai for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai):

```bash
npx -y @smithery/cli install @Dumpling-AI/mcp-server-dumplingai --client claude
```

### Running with npx

```bash
env DUMPLING_API_KEY=your_api_key npx -y mcp-server-dumplingai
```

### Manual Installation

```bash
npm install -g mcp-server-dumplingai
```

### Running on Cursor

Configuring Cursor 🖥️ Note: Requires Cursor version 0.45.6+

To configure Dumpling AI MCP in Cursor:

1. Open Cursor Settings
2. Go to Features > MCP Servers
3. Click ""+ Add New MCP Server""
4. Enter the following:

```
{
  ""mcpServers"": {
    ""dumplingai"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-server-dumplingai""],
      ""env"": {
        ""DUMPLING_API_KEY"": ""<your-api-key>""
      }
    }
  }
}
```

> If you are using Windows and are running into issues, try `cmd /c ""set DUMPLING_API_KEY=your-api-key && npx -y mcp-server-dumplingai""`

Replace `your-api-key` with your Dumpling AI API key.

## Configuration

### Environment Variables

- `DUMPLING_API_KEY`: Your Dumpling AI API key (required)

## Available Tools

### Data APIs

#### 1. Get YouTube Transcript (`get-youtube-transcript`)

Extract transcripts from YouTube videos with optional timestamps.

```json
{
  ""name"": ""get-youtube-transcript"",
  ""arguments"": {
    ""videoUrl"": ""https://www.youtube.com/watch?v=dQw4w9WgXcQ"",
    ""includeTimestamps"": true,
    ""timestampsToCombine"": 3,
    ""preferredLanguage"": ""en""
  }
}
```

#### 2. Search (`search`)

Perform Google web searches and optionally scrape content from results.

```json
{
  ""name"": ""search"",
  ""arguments"": {
    ""query"": ""machine learning basics"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastMonth"",
    ""scrapeResults"": true,
    ""numResultsToScrape"": 3,
    ""scrapeOptions"": {
      ""format"": ""markdown"",
      ""cleaned"": true
    }
  }
}
```

#### 3. Get Autocomplete (`get-autocomplete`)

Get Google search autocomplete suggestions for a query.

```json
{
  ""name"": ""get-autocomplete"",
  ""arguments"": {
    ""query"": ""how to learn"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""location"": ""New York""
  }
}
```

#### 4. Search Maps (`search-maps`)

Search Google Maps for locations and businesses.

```json
{
  ""name"": ""search-maps"",
  ""arguments"": {
    ""query"": ""coffee shops"",
    ""gpsPositionZoom"": ""37.7749,-122.4194,14z"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 5. Search Places (`search-places`)

Search for places with more detailed informhttps://github.com/Dumpling-AI/mcp-server-dumplingai,Dumpling-AI/mcp-server-dumplingai,"# Dumpling AI MCP Server

A Model Context Protocol (MCP) server implementation that integrates with Dumpling AI for data scraping, content processing, knowledge management, AI agents, and code execution capabilities.

[![smithery badge](https://smithery.ai/badge/@Dumpling-AI/mcp-server-dumplingai)](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai)

## Features

- Complete integration with all Dumpling AI API endpoints
- Data APIs for YouTube transcripts, search, autocomplete, maps, places, news, and reviews
- Web scraping with support for scraping, crawling, screenshots, and structured data extraction
- Document conversion tools for text extraction, PDF operations, video processing
- Extract data from documents, images, audio, and video
- AI capabilities including agent completions, knowledge base management, and image generation
- Developer tools for running JavaScript and Python code in a secure environment
- Automatic error handling and detailed response formatting

## Installation

### Installing via Smithery

To install mcp-server-dumplingai for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@Dumpling-AI/mcp-server-dumplingai):

```bash
npx -y @smithery/cli install @Dumpling-AI/mcp-server-dumplingai --client claude
```

### Running with npx

```bash
env DUMPLING_API_KEY=your_api_key npx -y mcp-server-dumplingai
```

### Manual Installation

```bash
npm install -g mcp-server-dumplingai
```

### Running on Cursor

Configuring Cursor 🖥️ Note: Requires Cursor version 0.45.6+

To configure Dumpling AI MCP in Cursor:

1. Open Cursor Settings
2. Go to Features > MCP Servers
3. Click ""+ Add New MCP Server""
4. Enter the following:

```
{
  ""mcpServers"": {
    ""dumplingai"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""mcp-server-dumplingai""],
      ""env"": {
        ""DUMPLING_API_KEY"": ""<your-api-key>""
      }
    }
  }
}
```

> If you are using Windows and are running into issues, try `cmd /c ""set DUMPLING_API_KEY=your-api-key && npx -y mcp-server-dumplingai""`

Replace `your-api-key` with your Dumpling AI API key.

## Configuration

### Environment Variables

- `DUMPLING_API_KEY`: Your Dumpling AI API key (required)

## Available Tools

### Data APIs

#### 1. Get YouTube Transcript (`get-youtube-transcript`)

Extract transcripts from YouTube videos with optional timestamps.

```json
{
  ""name"": ""get-youtube-transcript"",
  ""arguments"": {
    ""videoUrl"": ""https://www.youtube.com/watch?v=dQw4w9WgXcQ"",
    ""includeTimestamps"": true,
    ""timestampsToCombine"": 3,
    ""preferredLanguage"": ""en""
  }
}
```

#### 2. Search (`search`)

Perform Google web searches and optionally scrape content from results.

```json
{
  ""name"": ""search"",
  ""arguments"": {
    ""query"": ""machine learning basics"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastMonth"",
    ""scrapeResults"": true,
    ""numResultsToScrape"": 3,
    ""scrapeOptions"": {
      ""format"": ""markdown"",
      ""cleaned"": true
    }
  }
}
```

#### 3. Get Autocomplete (`get-autocomplete`)

Get Google search autocomplete suggestions for a query.

```json
{
  ""name"": ""get-autocomplete"",
  ""arguments"": {
    ""query"": ""how to learn"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""location"": ""New York""
  }
}
```

#### 4. Search Maps (`search-maps`)

Search Google Maps for locations and businesses.

```json
{
  ""name"": ""search-maps"",
  ""arguments"": {
    ""query"": ""coffee shops"",
    ""gpsPositionZoom"": ""37.7749,-122.4194,14z"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 5. Search Places (`search-places`)

Search for places with more detailed information.

```json
{
  ""name"": ""search-places"",
  ""arguments"": {
    ""query"": ""hotels in paris"",
    ""country"": ""fr"",
    ""language"": ""en"",
    ""page"": 1
  }
}
```

#### 6. Search News (`search-news`)

Search for news articles with customizable parameters.

```json
{
  ""name"": ""search-news"",
  ""arguments"": {
    ""query"": ""climate change"",
    ""country"": ""us"",
    ""language"": ""en"",
    ""dateRange"": ""pastWeek""
  }
}
```

#### 7. Get Google Reviews (`get-google-reviews`)

Retrieve Google reviews for businesses or places.

```json
{
  ""name"": ""get-google-reviews"",
  ""arguments"": {
    ""businessName"": ""Eiffel Tower"",
    ""location"": ""Paris, France"",
    ""limit"": 10,
    ""sortBy"": ""relevance""
  }
}
```

### Web Scraping

#### 8. Scrape (`scrape`)

Extract content from a web page with formatting options.

```json
{
  ""name"": ""scrape"",
  ""arguments"": {
    ""url"": ""https://example.com"",
    ""format"": ""markdown"",
    ""cleaned"": true,
    ""renderJs"": true
  }
}
```

#### 9. Crawl (`crawl`)

Recursively crawl websites and extract content with customizable parameters.

```json
{
  ""name"": ""crawl"",
  ""arguments"": {
    ""baseUrl"": ""https://example.com"",
    ""maxPages"": 10,
    ""crawlBeyondBaseUrl"": false,
    ""depth"": 2,
    ""scrapeOptions"": {
      ""format"": ""markdown"",
      ""cleaned"": true,
      ""renderJs"": true
    }
  }
}
```

#### 10. Screenshot (`screenshot`)

Capture screenshots of web pages with customizable viewport and format options.

```json
{
  ""name"": ""screenshot"",
  ""arguments"": {
    ""url"": ""https://example.com"",
    ""width"": 1280,
    ""height"": 800,
    ""fullPage"": true,
    ""format"": ""png"",
    ""waitFor"": 1000
  }
}
```

#### 11. Extract (`extract`)

Extract structured data from web pages using AI-powered instructions.

```json
{
  ""name"": ""extract"",
  ""arguments"": {
    ""url"": ""https://example.com/products"",
    ""instructions"": ""Extract all product names, prices, and descriptions from this page"",
    ""schema"": {
      ""products"": [
        {
          ""name"": ""string"",
          ""price"": ""number"",
          ""description"": ""string""
        }
      ]
    },
    ""renderJs"": true
  }
}
```

### Document Conversion

#### 12. Doc to Text (`doc-to-text`)

Convert documents to plaintext with optional OCR.

```json
{
  ""name"": ""doc-to-text"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""options"": {
      ""ocr"": true,
      ""language"": ""en""
    }
  }
}
```

#### 13. Convert to PDF (`convert-to-pdf`)

Convert various file formats to PDF.

```json
{
  ""name"": ""convert-to-pdf"",
  ""arguments"": {
    ""url"": ""https://example.com/document.docx"",
    ""format"": ""docx"",
    ""options"": {
      ""quality"": 90,
      ""pageSize"": ""A4"",
      ""margin"": 10
    }
  }
}
```

#### 14. Merge PDFs (`merge-pdfs`)

Combine multiple PDFs into a single document.

```json
{
  ""name"": ""merge-pdfs"",
  ""arguments"": {
    ""urls"": [""https://example.com/doc1.pdf"", ""https://example.com/doc2.pdf""],
    ""options"": {
      ""addPageNumbers"": true,
      ""addTableOfContents"": true
    }
  }
}
```

#### 15. Trim Video (`trim-video`)

Extract a specific clip from a video.

```json
{
  ""name"": ""trim-video"",
  ""arguments"": {
    ""url"": ""https://example.com/video.mp4"",
    ""startTime"": 30,
    ""endTime"": 60,
    ""output"": ""mp4"",
    ""options"": {
      ""quality"": 720,
      ""fps"": 30
    }
  }
}
```

#### 16. Extract Document (`extract-document`)

Extract specific content from documents in various formats.

```json
{
  ""name"": ""extract-document"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""format"": ""structured"",
    ""options"": {
      ""ocr"": true,
      ""language"": ""en"",
      ""includeMetadata"": true
    }
  }
}
```

#### 17. Extract Image (`extract-image`)

Extract text and information from images.

```json
{
  ""name"": ""extract-image"",
  ""arguments"": {
    ""url"": ""https://example.com/image.jpg"",
    ""extractionType"": ""text"",
    ""options"": {
      ""language"": ""en"",
      ""detectOrientation"": true
    }
  }
}
```

#### 18. Extract Audio (`extract-audio`)

Transcribe and extract information from audio files.

```json
{
  ""name"": ""extract-audio"",
  ""arguments"": {
    ""url"": ""https://example.com/audio.mp3"",
    ""language"": ""en"",
    ""options"": {
      ""model"": ""enhanced"",
      ""speakerDiarization"": true,
      ""wordTimestamps"": true
    }
  }
}
```

#### 19. Extract Video (`extract-video`)

Extract content from videos including transcripts, scenes, and objects.

```json
{
  ""name"": ""extract-video"",
  ""arguments"": {
    ""url"": ""https://example.com/video.mp4"",
    ""extractionType"": ""transcript"",
    ""options"": {
      ""language"": ""en"",
      ""speakerDiarization"": true
    }
  }
}
```

#### 20. Read PDF Metadata (`read-pdf-metadata`)

Extract metadata from PDF files.

```json
{
  ""name"": ""read-pdf-metadata"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""includeExtended"": true
  }
}
```

#### 21. Write PDF Metadata (`write-pdf-metadata`)

Update metadata in PDF files.

```json
{
  ""name"": ""write-pdf-metadata"",
  ""arguments"": {
    ""url"": ""https://example.com/document.pdf"",
    ""metadata"": {
      ""title"": ""New Title"",
      ""author"": ""John Doe"",
      ""keywords"": [""keyword1"", ""keyword2""]
    }
  }
}
```

### AI

#### 22. Generate Agent Completion (`generate-agent-completion`)

Get AI agent completions with optional tool definitions.

```json
{
  ""name"": ""generate-agent-completion"",
  ""arguments"": {
    ""prompt"": ""How can I improve my website's SEO?"",
    ""model"": ""gpt-4"",
    ""temperature"": 0.7,
    ""maxTokens"": 500,
    ""context"": [""The website is an e-commerce store selling handmade crafts.""]
  }
}
```

#### 23. Search Knowledge Base (`search-knowledge-base`)

Search a knowledge base for relevant information.

```json
{
  ""name"": ""search-knowledge-base"",
  ""arguments"": {
    ""kbId"": ""kb_12345"",
    ""query"": ""How to optimize database performance"",
    ""limit"": 5,
    ""similarityThreshold"": 0.7
  }
}
```

#### 24. Add to Knowledge Base (`add-to-knowledge-base`)

Add entries to a knowledge base.

```json
{
  ""name"": ""https://github.com/mberg/kokoro-tts-mcp,mberg/kokoro-tts-mcp,"## Kokoro Text to Speech (TTS) MCP Server

Kokoro Text to Speech MCP server that generates .mp3 files with option to upload to S3.

Uses: https://huggingface.co/spaces/hexgrad/Kokoro-TTS

## Configuration

* Clone to a local repo.
* Download the [Kokoro Onnx Weights](https://github.com/thewh1teagle/kokoro-onnx) for [kokoro-v1.0.onnx](https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/kokoro-v1.0.onnx) and [voices-v1.0.bin](https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/voices-v1.0.bin) and store in the same repo.

Add the following to your MCP configs. Update with your own values.

```
  ""kokoro-tts-mcp"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""/path/toyourlocal/kokoro-tts-mcp"",
        ""run"",
        ""mcp-tts.py""
      ],
      ""env"": {
        ""TTS_VOICE"": ""af_heart"",
        ""TTS_SPEED"": ""1.0"",
        ""TTS_LANGUAGE"": ""en-us"",
        ""AWS_ACCESS_KEY_ID"": """",
        ""AWS_SECRET_ACCESS_KEY"": """",
        ""AWS_REGION"": ""us-east-1"",
        ""AWS_S3_FOLDER"": ""mp3"",
        ""S3_ENABLED"": ""true"",
        ""MP3_FOLDER"": ""/path/to/mp3""
      } 
    }
```

### Install ffmmeg

This is needed to convert .wav to .mp3 files

For mac:

``` 
brew install ffmpeg
```

To run locally add these to your .env file.  See env.example and copy to .env and modify with your own values.

### Supported Environment Variables

- `AWS_ACCESS_KEY_ID`: Your AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: Your AWS secret access key
- `AWS_S3_BUCKET_NAME`: S3 bucket name
- `AWS_S3_REGION`: S3 region (e.g., us-east-1)
- `AWS_S3_FOLDER`: Folder path within the S3 bucket
- `AWS_S3_ENDPOINT_URL`: Optional custom endpoint URL for S3-compatible storage
- `MCP_HOST`: Host to bind the server to (default: 0.0.0.0)
- `MCP_PORT`: Port to listen on (default: 9876)
- `MCP_CLIENT_HOST`: Hostname for client connections to the server (default: localhost)
- `DEBUG`: Enable debug mode (set to ""true"" or ""1"")
- `S3_ENABLED`: Enable S3 uploads (set to ""true"" or ""1"")
- `MP3_FOLDER`: Path to store MP3 files (default is 'mp3' folder in script directory)
- `MP3_RETENTION_DAYS`: Number of days to keep MP3 files before automatic deletion
- `DELETE_LOCAL_AFTER_S3_UPLOAD`: Whether to delete local MP3 files after successful S3 upload (set to ""true"" or ""1"")
- `TTS_VOICE`: Default voice for the TTS client (default: af_heart)
- `TTS_SPEED`: Default speed for the TTS client (default: 1.0)
- `TTS_LANGUAGE`: Default language for the TTS client (default: en-us)

## Running the Server Locally

Preferred method use UV 
```
uv run mcp-tts.py
```


## Using the TTS Client

The `mcp_client.py` script allows you to send TTS requests to the server. It can be used as follows:

### Connection Settings

When running the server and client on the same machine:
- Server should bind to `0.0.0.0` (all interfaces) or `127.0.0.1` (localhost only)
- Client should connect to `localhost` or `127.0.0.1`


### Basic Usage

```bash
python mcp_client.py --text ""Hello, world!""
```

### Reading Text from a File

```bash
python mcp_client.py --file my_text.txt
```

### Customizing Voice and Speed

```bash
python mcp_client.py --text ""Hello, world!"" --voice ""en_female"" --speed 1.2
```

### Disabling S3 Upload

```bash
python mcp_client.py --text ""Hello, world!"" --no-s3
```

### Command-line Options

```bash
python mcp_client.py --help
```

## MP3 File Management

The TTS server generates MP3 files that are stored locally and optionally uploaded to S3. You can configure how these files are managed:

### Local Storage

- Set `MP3_FOLDER` in your `.env` file to specify where MP3 files are stored
- Files are kept in this fhttps://github.com/mberg/kokoro-tts-mcp,mberg/kokoro-tts-mcp,"## Kokoro Text to Speech (TTS) MCP Server

Kokoro Text to Speech MCP server that generates .mp3 files with option to upload to S3.

Uses: https://huggingface.co/spaces/hexgrad/Kokoro-TTS

## Configuration

* Clone to a local repo.
* Download the [Kokoro Onnx Weights](https://github.com/thewh1teagle/kokoro-onnx) for [kokoro-v1.0.onnx](https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/kokoro-v1.0.onnx) and [voices-v1.0.bin](https://github.com/thewh1teagle/kokoro-onnx/releases/download/model-files-v1.0/voices-v1.0.bin) and store in the same repo.

Add the following to your MCP configs. Update with your own values.

```
  ""kokoro-tts-mcp"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""/path/toyourlocal/kokoro-tts-mcp"",
        ""run"",
        ""mcp-tts.py""
      ],
      ""env"": {
        ""TTS_VOICE"": ""af_heart"",
        ""TTS_SPEED"": ""1.0"",
        ""TTS_LANGUAGE"": ""en-us"",
        ""AWS_ACCESS_KEY_ID"": """",
        ""AWS_SECRET_ACCESS_KEY"": """",
        ""AWS_REGION"": ""us-east-1"",
        ""AWS_S3_FOLDER"": ""mp3"",
        ""S3_ENABLED"": ""true"",
        ""MP3_FOLDER"": ""/path/to/mp3""
      } 
    }
```

### Install ffmmeg

This is needed to convert .wav to .mp3 files

For mac:

``` 
brew install ffmpeg
```

To run locally add these to your .env file.  See env.example and copy to .env and modify wihttps://github.com/last9/last9-mcp-server,last9/last9-mcp-server,"# Last9 MCP Server

![last9 mcp demo](mcp-demo.gif)

A [Model Context Protocol](https://modelcontextprotocol.io/) server
implementation for [Last9](https://last9.io/mcp/) that enables AI agents to
seamlessly bring real-time production context — logs, metrics, and traces — into
your local environment to auto-fix code faster.

- [View demo](https://www.youtube.com/watch?v=AQH5xq6qzjI)
- Read our
  [announcement blog post](https://last9.io/blog/launching-last9-mcp-server/)

## Status

Works with Claude desktop app, or Cursor, Windsurf, and VSCode (Github Copilot)
IDEs. Implements the following MCP
[tools](https://modelcontextprotocol.io/docs/concepts/tools):

- `get_exceptions`: Get the list of exceptions.
- `get_service_graph`: Get service graph for an endpoint from the exception.
- `get_logs`: Get logs filtered by service name and/or severity level.
- `get_drop_rules`: Get drop rules for logs that determine what logs get
  filtered out at [Last9 Control Plane](https://last9.io/control-plane)
- `add_drop_rule`: Create a drop rule for logs at
  [Last9 Control Plane](https://last9.io/control-plane)

## Tools Documentation

### get_exceptions

Retrieves server-side exceptions over a specified time range.

Parameters:

- `limit` (integer, optional): Maximum number of exceptions to return.
  Default: 20.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.
- `end_time_iso` (string, optional): End time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to default to current time.
- `span_name` (string, optional): Name of the span to filter by.

### get_service_graph

Gets the upstream and downstream services for a given span name, along with the
throughput for each service.

Parameters:

- `span_name` (string, required): Name of the span to get dependencies for.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.

### get_logs

Gets logs filtered by optional service name and/or severity level within a
specified time range.

Parameters:

- `service` (string, optional): Name of the service to get logs for.
- `severity` (string, optional): Severity of the logs to get.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.
- `end_time_iso` (string, optional): End time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to default to current time.
- `limit` (integer, optional): Maximum number of logs to return. Default: 20.

### get_drop_rules

Gets drop rules for logs, which determine what logs get filtered out from
reaching Last9.

### add_drop_rule

Adds a new drop rule to filter out specific logs at
[Last9 Control Plane](https://last9.io/control-plane)

Parameters:

- `name` (string, required): Name of the drop rule.
- `filters` (array, required): List of filter conditions to apply. Each filter
  has:
  - `key` (string, required): The key to filter on. Only attributes and
    resource.attributes keys are supported. For resource attributes, use format:
    resource.attributes[key_name] and for log attributes, use format:
    attributes[key_name] Double quotes in key names must be escaped.
  - `value` (string, required): The value to filter against.
  - `operator` (string, required): The operator used for filterhttps://github.com/last9/last9-mcp-server,last9/last9-mcp-server,"# Last9 MCP Server

![last9 mcp demo](mcp-demo.gif)

A [Model Context Protocol](https://modelcontextprotocol.io/) server
implementation for [Last9](https://last9.io/mcp/) that enables AI agents to
seamlessly bring real-time production context — logs, metrics, and traces — into
your local environment to auto-fix code faster.

- [View demo](https://www.youtube.com/watch?v=AQH5xq6qzjI)
- Read our
  [announcement blog post](https://last9.io/blog/launching-last9-mcp-server/)

## Status

Works with Claude desktop app, or Cursor, Windsurf, and VSCode (Github Copilot)
IDEs. Implements the following MCP
[tools](https://modelcontextprotocol.io/docs/concepts/tools):

- `get_exceptions`: Get the list of exceptions.
- `get_service_graph`: Get service graph for an endpoint from the exception.
- `get_logs`: Get logs filtered by service name and/or severity level.
- `get_drop_rules`: Get drop rules for logs that determine what logs get
  filtered out at [Last9 Control Plane](https://last9.io/control-plane)
- `add_drop_rule`: Create a drop rule for logs at
  [Last9 Control Plane](https://last9.io/control-plane)

## Tools Documentation

### get_exceptions

Retrieves server-side exceptions over a specified time range.

Parameters:

- `limit` (integer, optional): Maximum number of exceptions to return.
  Default: 20.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.
- `end_time_iso` (string, optional): End time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to default to current time.
- `span_name` (string, optional): Name of the span to filter by.

### get_service_graph

Gets the upstream and downstream services for a given span name, along with the
throughput for each service.

Parameters:

- `span_name` (string, required): Name of the span to get dependencies for.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.

### get_logs

Gets logs filtered by optional service name and/or severity level within a
specified time range.

Parameters:

- `service` (string, optional): Name of the service to get logs for.
- `severity` (string, optional): Severity of the logs to get.
- `lookback_minutes` (integer, recommended): Number of minutes to look back from
  now. Default: 60. Examples: 60, 30, 15.
- `start_time_iso` (string, optional): Start time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to use lookback_minutes.
- `end_time_iso` (string, optional): End time in ISO format (YYYY-MM-DD
  HH:MM:SS). Leave empty to default to current time.
- `limit` (integer, optional): Maximum number of logs to return. Default: 20.

### get_drop_rules

Gets drop rules for logs, which determine what logs get filtered out from
reaching Last9.

### add_drop_rule

Adds a new drop rule to filter out specific logs at
[Last9 Control Plane](https://last9.io/control-plane)

Parameters:

- `name` (string, required): Name of the drop rule.
- `filters` (array, required): List of filter conditions to apply. Each filter
  has:
  - `key` (string, required): The key to filter on. Only attributes and
    resource.attributes keys are supported. For resource attributes, use format:
    resource.attributes[key_name] and for log attributes, use format:
    attributes[key_name] Double quotes in key names must be escaped.
  - `value` (string, required): The value to filter against.
  - `operator` (string, required): The operator used for filtering. Valid
    values:
    - ""equals""
    - ""not_equals""
  - `conjunction` (string, required): The logical conjunction between filters.
    Valid values:
    - ""and""

## Installation

You can install the Last9 Observability MCP server using either:

### Homebrew

```
# Add the Last9 tap
brew tap last9/tap

# Install the Last9 MCP CLI
brew install last9-mcp
```

### NPM

```bash
# Install globally
npm install -g @last9/mcp-server

# Or run directly with npx
npx @last9/mcp-server
```

## Configuration

### Environment Variables

The Last9 MCP server requires the following environment variables:

- `LAST9_BASE_URL`: (required) Last9 API URL from
  [OTel integration](https://app.last9.io/integrations?integration=OpenTelemetry)
- `LAST9_AUTH_TOKEN`: (required) Authentication token for Last9 MCP server from
  [OTel integration](https://app.last9.io/integrations?integration=OpenTelemetry)
- `LAST9_REFRESH_TOKEN`: (required) Refresh Token with Write permissions, needed
  for accessing control plane APIs from
  [API Access](https://app.last9.io/settings/api-access)

## Usage with Claude Desktop

Configure the Claude app to use the MCP server:

1. Open the Claude Desktop app, go to Settings, then Developer
2. Click Edit Config
3. Open the `claude_desktop_config.json` file
4. Copy and paste the server config to your existing file, then save
5. Restart Claude

```json
{
  ""mcpServers"": {
    ""last9"": {
      ""command"https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server,hannesrudolph/imessage-query-fastmcp-mcp-server,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-imessage-query-fastmcp-mcp-server)

# iMessage Query MCP Server

An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to query and analyze iMessage conversations with proper phone number validation and automatic macOS permission handling.

## 📋 System Requirements

- macOS (required for iMessage database access)
- Python 3.12+ (required for modern type hints)
- **uv** (modern Python package manager)
- **Full Disk Access permission** for your MCP client (Claude Desktop, Cursor, VS Code, etc.)

## 📦 Dependencies

### Install uv (Required)

This project uses `uv` for fast, reliable Python package management. Install it first:

```bash
# Install uv using Homebrew (recommended)
brew install uv

# Or install using the official installer
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Python Dependencies

The script automatically manages its dependencies using the embedded metadata. No separate installation needed! Dependencies include:

- **fastmcp**: Framework for building Model Context Protocol servers
- **imessagedb**: Python library for accessing and querying the macOS Messages database
- **phonenumbers**: Google's phone number handling library for proper number validation and formatting

All dependencies are automatically installed when the script runs via `uv`.

## 📑 Table of Contents
- [System Requirements](#-system-requirements)
- [Dependencies](#-dependencies)
- [MCP Tools](#%EF%B8%8F-mcp-tools)
- [Getting Started](#-getting-started)
- [Installation Options](#-installation-options)
  - [Claude Desktop](#option-1-install-for-claude-desktop)
  - [Cline VSCode Plugin](#option-2-install-for-cline-vscode-plugin)
- [macOS Permissions Setup](#-macos-permissions-setup)
- [Safety Features](#-safety-features)
- [Development Documentation](#-development-documentation)
- [Environment Variables](#%EF%B8%8F-environment-variables)

## 🛠️ MCP Tools

The server exposes the following tools to LLMs:

### get_chat_transcript
Retrieve message history for a specific phone number with optional date filtering.

**Parameters:**
- `phone_number` (required): Phone number in any format (E.164 format preferred)
- `start_date` (optional): Start date in ISO format (YYYY-MM-DD)
- `end_date` (optional): End date in ISO format (YYYY-MM-DD)

**Features:**
- Automatic phone number validation and formatting
- Message text and timestamps
- Attachment information with missing file detection
- Date range filtering (defaults to last 7 days if no dates specified)
- Sender identification (is_from_me flag)

## 🚀 Getting Started

Clone the repository:

```bash
git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server.git
cd imessage-query-fastmcp-mcp-server
```

## 📦 Installation Options

You can install this MCP server in Claude Desktop, Cline VSCode plugin, or any other MCP client. Choose the option that best suits your needs.

### Option 1: Claude Desktop

1. **Find your Claude Desktop config file:**
   - **Location**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Create the file if it doesn't exist

2. **Add the server configuration:**

```json
{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}
```

3. **Replace the path** with the full path to your cloned repository (e.g., `/Users/<USER>/Projects/imessage-query-fastmcp-mcp-server/imehttps://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server,hannesrudolph/imessage-query-fastmcp-mcp-server,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-imessage-query-fastmcp-mcp-server)

# iMessage Query MCP Server

An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to query and analyze iMessage conversations with proper phone number validation and automatic macOS permission handling.

## 📋 System Requirements

- macOS (required for iMessage database access)
- Python 3.12+ (required for modern type hints)
- **uv** (modern Python package manager)
- **Full Disk Access permission** for your MCP client (Claude Desktop, Cursor, VS Code, etc.)

## 📦 Dependencies

### Install uv (Required)

This project uses `uv` for fast, reliable Python package management. Install it first:

```bash
# Install uv using Homebrew (recommended)
brew install uv

# Or install using the official installer
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Python Dependencies

The script automatically manages its dependencies using the embedded metadata. No separate installation needed! Dependencies include:

- **fastmcp**: Framework for building Model Context Protocol servers
- **imessagedb**: Python library for accessing and querying the macOS Messages database
- **phonenumbers**: Google's phone number handling library for proper number validation and formatting

All dependencies are automatically installed when the script runs via `uv`.

## 📑 Table of Contents
- [System Requirements](#-system-requirements)
- [Dependencies](#-dependencies)
- [MCP Tools](#%EF%B8%8F-mcp-tools)
- [Getting Started](#-getting-started)
- [Installation Options](#-installation-options)
  - [Claude Desktop](#option-1-install-for-claude-desktop)
  - [Cline VSCode Plugin](#option-2-install-for-cline-vscode-plugin)
- [macOS Permissions Setup](#-macos-permissions-setup)
- [Safety Features](#-safety-features)
- [Development Documentation](#-development-documentation)
- [Environment Variables](#%EF%B8%8F-environment-variables)

## 🛠️ MCP Tools

The server exposes the following tools to LLMs:

### get_chat_transcript
Retrieve message history for a specific phone number with optional date filtering.

**Parameters:**
- `phone_number` (required): Phone number in any format (E.164 format preferred)
- `start_date` (optional): Start date in ISO format (YYYY-MM-DD)
- `end_date` (optional): End date in ISO format (YYYY-MM-DD)

**Features:**
- Automatic phone number validation and formatting
- Message text and timestamps
- Attachment information with missing file detection
- Date range filtering (defaults to last 7 days if no dates specified)
- Sender identification (is_from_me flag)

## 🚀 Getting Started

Clone the repository:

```bash
git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server.git
cd imessage-query-fastmcp-mcp-server
```

## 📦 Installation Options

You can install this MCP server in Claude Desktop, Cline VSCode plugin, or any other MCP client. Choose the option that best suits your needs.

### Option 1: Claude Desktop

1. **Find your Claude Desktop config file:**
   - **Location**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Create the file if it doesn't exist

2. **Add the server configuration:**

```json
{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}
```

3. **Replace the path** with the full path to your cloned repository (e.g., `/Users/<USER>/Projects/imessage-query-fastmcp-mcp-server/imessage-query-server.py`)

4. **Restart Claude Desktop** completely (Cmd+Q, then relaunch)

### Option 2: Cline VSCode Plugin

To use this server with the [Cline VSCode plugin](http://cline.bot):

1. In VSCode, click the server icon (☰) in the Cline plugin sidebar
2. Click the ""Edit MCP Settings"" button (✎)
3. Add the following configuration to the settings file:

```json
{
  ""imessage-query"": {
    ""command"": ""/full/path/to/imessage-query-server.py""
  }
}
```

4. **Replace the path** with the full path to your cloned repository

### Option 3: Other MCP Clients

For other MCP clients, use the direct script path as the command:

```
/full/path/to/imessage-query-server.py
```

The script's shebang (`#!/usr/bin/env -S uv run --script`) handles dependency management automatically.

> **Note**: This simplified configuration replaces the previous FastMCP installation method. The script is now self-contained and manages its own dependencies through `uv`.

## 🔐 macOS Permissions Setup

This server requires **Full Disk Access** permission to read the iMessage database. The server includes intelligent permission detection and will guide you through the setup process.

### Automatic Permission Detection

When you first use the server, it will:
1. **Detect your MCP client** (Claude Desktop, Cursor, VS Code, etc.)
2. **Check for Full Disk Access** permission
3. **Automatically open System Preferences** to the correct settings panel
4. **Provide step-by-step instructions** specific to your application

### Manual Permission Setup

If automatic detection doesn't work, follow these steps:

1. **Open System Preferences** → **Privacy & Security** → **Full Disk Access**
2. **Click the lock icon** and enter your password to make changes
3. **Click the '+' button** to add an application
4. **Navigate to and select your MCP client:**
   - **Claude Desktop**: `/Applications/Claude.app`
   - **Cursor**: `/Applications/Cursor.app`
   - **VS Code**: `/Applications/Visual Studio Code.app`
5. **Restart your MCP client** completely (Cmd+Q, then relaunch)

### Common Issues

- **Permission denied errors**: Make sure you've restarted your MCP client after granting permission
- **""uv"" instead of app name**: The server will auto-detect your actual MCP client and provide correct instructions
- **Database not found**: Ensure you've used the Messages app and iMessage is enabled

### Security Note

This server only requires **read access** to your iMessage database. It cannot modify, delete, or send messages.

## 🔒 Safety Features

- **Read-only access** to the iMessage database (cannot modify, delete, or send messages)
- **Phone number validation** using Google's phonenumbers library with proper E.164 formatting
- **Safe attachment handling** with missing file detection and metadata extraction
- **Date range validation** to prevent invalid queries
- **Progress output suppression** for clean JSON responses in MCP protocol
- **Intelligent permission dhttps://github.com/mobile-next/mobile-mcp,mobile-next/mobile-mcp,"# Mobile Next - MCP server for Mobile Development and Automation  | iOS, Android, Simulator, Emulator, and physical devices

This is a [Model Context Protocol (MCP) server](https://github.com/modelcontextprotocol) that enables scalable mobile automation, development through a platform-agnostic interface, eliminating the need for distinct iOS or Android knowledge. You can run it on emulators, simulators, and physical devices (iOS and Android).
This server allows Agents and LLMs to interact with native iOS/Android applications and devices through structured accessibility snapshots or coordinate-based taps based on screenshots.

<h4 align=""center"">
<a href=""https://github.com/mobile-next/mobile-mcp"">
    <img src=""https://img.shields.io/github/stars/mobile-next/mobile-mcp"" alt=""Mobile Next Stars"" />
  </a>
 <a href=""https://github.com/mobile-next/mobile-mcp"">
    <img src=""https://img.shields.io/github/contributors/mobile-next/mobile-mcp?color=green"" alt=""Mobile Next Downloads"" />
  </a>
  <a href=""https://www.npmjs.com/package/@mobilenext/mobile-mcp"">
    <img src=""https://img.shields.io/npm/dm/@mobilenext/mobile-mcp?logo=npm&style=flat&color=red"" alt=""npm"">
  </a>
<a href=""https://github.com/mobile-next/mobile-mcp/releases"">
    <img src=""https://img.shields.io/github/release/mobile-next/mobile-mcp"">
  </a>
<a href=""https://github.com/mobile-next/mobile-mcp/blob/main/LICENSE"">
    <img src=""https://img.shields.io/badge/license-Apache 2.0-blue.svg"" alt=""Mobile MCP is released under the Apache-2.0 License"">
  </a>

</p>

<h4 align=""center"">
<a href=""http://mobilenexthq.com/join-slack"">
    <img src=""https://img.shields.io/badge/join-Slack-blueviolet?logo=slack&style=flat"" alt=""Slack community channel"" />
</a>
</p>

https://github.com/user-attachments/assets/c4e89c4f-cc71-4424-8184-bdbc8c638fa1

<p align=""center"">
    <a href=""https://github.com/mobile-next/"">
        <img alt=""mobile-mcp"" src=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-banner.png"" width=""600"">
    </a>
</p>

### 🚀 Mobile MCP Roadmap: Building the Future of Mobile

Join us on our journey as we continuously enhance Mobile MCP!
Check out our detailed roadmap to see upcoming features, improvements, and milestones. Your feedback is invaluable in shaping the future of mobile automation.

👉 [Explore the Roadmap](https://github.com/orgs/mobile-next/projects/3)


### Main use cases

How we help to scale mobile automation:

- 📲 Native app automation (iOS and Android) for testing or data-entry scenarios.
- 📝 Scripted flows and form interactions without manually controlling simulators/emulators or physical devices (iPhone, Samsung, Google Pixel etc)
- 🧭 Automating multi-step user journeys driven by an LLM
- 👆 General-purpose mobile application interaction for agent-based frameworks
- 🤖 Enables agent-to-agent communication for mobile automation usecases, data extraction

## Main Features

- 🚀 **Fast and lightweight**: Uses native accessibility trees for most interactions, or screenshot based coordinates where a11y labels are not available.
- 🤖 **LLM-friendly**: No computer vision model required in Accessibility (Snapshot).
- 🧿 **Visual Sense**: Evaluates and analyses what’s actually rendered on screen to decide the next action. If accessibility data or view-hierarchy coordinates are unavailable, it falls back to screenshot-based analysis.
- 📊 **Deterministic tool application**: Reduces ambiguity found in purely screenshot-based approaches by relying on structured data whenever possible.
- 📺 **Extract structured data**: Enables you to extract structred data from anything visible on screen.

## �https://github.com/mobile-next/mobile-mcp,mobile-next/mobile-mcp,"# Mobile Next - MCP server for Mobile Development and Automation  | iOS, Android, Simulator, Emulator, and physical devices

This is a [Model Context Protocol (MCP) server](https://github.com/modelcontextprotocol) that enables scalable mobile automation, development through a platform-agnostic interface, eliminating the need for distinct iOS or Android knowledge. You can run it on emulators, simulators, and physical devices (iOS and Android).
This server allows Agents and LLMs to interact with native iOS/Android applications and devices through structured accessibility snapshots or coordinate-based taps based on screenshots.

<h4 align=""center"">
<a href=""https://github.com/mobile-next/mobile-mcp"">
    <img src=""https://img.shields.io/github/stars/mobile-next/mobile-mcp"" alt=""Mobile Next Stars"" />
  </a>
 <a href=""https://github.com/mobile-next/mobile-mcp"">
    <img src=""https://img.shields.io/github/contributors/mobile-next/mobile-mcp?color=green"" alt=""Mobile Next Downloads"" />
  </a>
  <a href=""https://www.npmjs.com/package/@mobilenext/mobile-mcp"">
    <img src=""https://img.shields.io/npm/dm/@mobilenext/mobile-mcp?logo=npm&style=flat&color=red"" alt=""npm"">
  </a>
<a href=""https://github.com/mobile-next/mobile-mcp/releases"">
    <img src=""https://img.shields.io/github/release/mobile-next/mobile-mcp"">
  </a>
<a href=""https://github.com/mobile-next/mobile-mcp/blob/main/LICENSE"">
    <img src=""https://img.shields.io/badge/license-Apache 2.0-blue.svg"" alt=""Mobile MCP is released under the Apache-2.0 License"">
  </a>

</p>

<h4 align=""center"">
<a href=""http://mobilenexthq.com/join-slack"">
    <img src=""https://img.shields.io/badge/join-Slack-blueviolet?logo=slack&style=flat"" alt=""Slack community channel"" />
</a>
</p>

https://github.com/user-attachments/assets/c4e89c4f-cc71-4424-8184-bdbc8c638fa1

<p align=""center"">
    <a href=""https://github.com/mobile-next/"">
        <img alt=""mobile-mcp"" src=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-banner.png"" width=""600"">
    </a>
</p>

### 🚀 Mobile MCP Roadmap: Building the Future of Mobile

Join us on our journey as we continuously enhance Mobile MCP!
Check out our detailed roadmap to see upcoming features, improvements, and milestones. Your feedback is invaluable in shaping the future of mobile automation.

👉 [Explore the Roadmap](https://github.com/orgs/mobile-next/projects/3)


### Main use cases

How we help to scale mobile automation:

- 📲 Native app automation (iOS and Android) for testing or data-entry scenarios.
- 📝 Scripted flows and form interactions without manually controlling simulators/emulators or physical devices (iPhone, Samsung, Google Pixel etc)
- 🧭 Automating multi-step user journeys driven by an LLM
- 👆 General-purpose mobile application interaction for agent-based frameworks
- 🤖 Enables agent-to-agent communication for mobile automation usecases, data extraction

## Main Features

- 🚀 **Fast and lightweight**: Uses native accessibility trees for most interactions, or screenshot based coordinates where a11y labels are not available.
- 🤖 **LLM-friendly**: No computer vision model required in Accessibility (Snapshot).
- 🧿 **Visual Sense**: Evaluates and analyses what’s actually rendered on screen to decide the next action. If accessibility data or view-hierarchy coordinates are unavailable, it falls back to screenshot-based analysis.
- 📊 **Deterministic tool application**: Reduces ambiguity found in purely screenshot-based approaches by relying on structured data whenever possible.
- 📺 **Extract structured data**: Enables you to extract structred data from anything visible on screen.

## 🏗️ Mobile MCP Architecture

<p align=""center"">
    <a href=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-arch-1.png"">
        <img alt=""mobile-mcp"" src=""https://raw.githubusercontent.com/mobile-next/mobile-next-assets/refs/heads/main/mobile-mcp-arch-1.png"" width=""600"">
    </a>
</p>


## 📚 Wiki page

More details in our [wiki page](https://github.com/mobile-next/mobile-mcp/wiki) for setup, configuration and debugging related questions.


## Installation and configuration

Setup our MCP with Cline, Cursor, Claude, VS Code, Github Copilot:

```json
{
  ""mcpServers"": {
    ""mobile-mcp"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@mobilenext/mobile-mcp@latest""]
    }
  }
}

```
[Cline:](https://docs.cline.bot/mcp/configuring-mcp-servers) To setup Cline, just add the json above to your MCP settings file.
[More in our wiki](https://github.com/mobile-next/mobile-mcp/wiki/Cline)

[Claude Code:](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview)

```
claude mcp add mobile -- npx -y @mobilenext/mobile-mcp@latest
```

[Read more in our wiki](https://github.com/mobile-next/mobile-mcp/wiki)! 🚀


### 🛠️ How to Use 📝

After adding the MCP server to your IDE/Client, you can instruct your AI assistant to use the available tools.
For example, in Cursor's agent mode, you could use the prompts below to quickly validate, test and iterate on UI intereactions, read information from screen, go through complex workflows.
Be descriptive, straight to the point.

### ✨ Example Prompts

#### Workflows

You can specifiy detailed workflows in a single prompt, verify business logic, setup automations. You can go crazy:

**Search for a video, comment, like and share it.**
```
Find the video called "" Beginner Recipe for Tonkotsu Ramen"" by Way of
Ramen, click on like video, after liking write a comment "" this was
delicious, will make it next Friday"", share the video with the first
contact in your whatsapp list.
```

**Download a successful step counter app, register, setup workout and 5-star the app**
```
Find and Download a free ""Pomodoro"" app that has more than 1k stars.
Launch the app, register with my email, after registration find how to
start a pomodoro timer. When the pomodoro timer started, go back to the
app store and rate the app 5 stars, and leave a comment how useful the
app is.
```

**Search in Substack, read, highlight, comment and save an article**
```
Open Substack website, search for ""Latest trends in AI automation 2025"",
open the first article, highlight the section titled ""Emerging AI trends"",
and save article to reading list for later review, comment a random
paragraph summary.
```

**Reserve a workout class, set timer**
```
Open ClassPass, search for yoga classes tomorrow morning within 2 miles,
book the highest-rated class at 7 AM, confirm reservation,
setup a timer for the booked slot in the phone
```

**Find a local event, setup calendar event**
```
Open Eventbrite, search for AI startup meetup events happening this
weekend in ""Austin,https://github.com/seekrays/mcp-monitor,seekrays/mcp-monitor,"# MCP System Monitor

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)

A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.

![](./doc/snapshot-1.png)

## Features

This tool provides the following monitoring capabilities:

- **CPU Information**: Usage percentage, core count, and detailed CPU info
- **Memory Information**: Virtual and swap memory usage
- **Disk Information**: Disk usage, partitions, and I/O statistics
- **Network Information**: Network interfaces, connections, and traffic statistics
- **Host Information**: System details, uptime, boot time, and users
- **Process Information**: Process listing, sorting, and detailed per-process statistics


## Available Tools

### 1. CPU Information

```
Tool: get_cpu_info
Description: Get CPU information and usage
Parameters:
  - per_cpu (boolean, default: false): Whether to return data for each core
```

### 2. Memory Information

```
Tool: get_memory_info
Description: Get system memory usage information
Parameters: None
```

### 3. Disk Information

```
Tool: get_disk_info
Description: Get disk usage information
Parameters:
  - path (string, default: ""/""): Specify the disk path to query
  - all_partitions (boolean, default: false): Whether to return information for all partitions
```

### 4. Network Information

```
Tool: get_network_info
Description: Get network interface and traffic information
Parameters:
  - interface (string, optional): Specify the network interface name to query
```

### 5. Host Information

```
Tool: get_host_info
Description: Get host system information
Parameters: None
```

### 6. Process Information

```
Tool: get_process_info
Description: Get process information
Parameters:
  - pid (number, optional): Process ID to get detailed information for a specific process
  - limit (number, default: 10): Limit the number of processes returned
  - sort_by (string, default: ""cpu""): Sort field (cpu, memory, pid, name)
```


## Installation

```bash
git clone https://github.com/seekrays/mcp-monitor.git
cd mcp-monitor
make build
```

## Usage

Run the compiled binary:

```bash
./mcp-monitor
```

The server starts in stdio mode, ready to communicate with an MCP-compatible LLM client.


## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. ","Star
 42",2025-06-24 07:59:40.920526,mcp-monitor - MCP Server | Model Context Protocol Integration,"# MCP System Monitor

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)

A system moni...","['mcp server', 'model context protocol', 'ai integration', 'mcp-monitor']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-monitor', 'description': '# MCP System Monitor\n\n[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)\n\nA system moni...', 'url': 'https://github.com/seekrays/mcp-monitor', 'codeRepository': 'https://github.com/seekrays/mcp-monitor', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,Automation,Time Management,,,,,,,seekrays,A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.
https://github.com/jjsantos01/jupyter-notebook-mcp,jjsantos01/jupyter-notebook-mcp,"# JupyterMCP - Jupyter Notebook Model Context Protocol Integration

JupyterMCP connects [Jupyter Notebook](https://jupyter.org/) to [Chttps://github.com/seekrays/mcp-monitor,seekrays/mcp-monitor,"# MCP System Monitor

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)

A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.

![](./doc/snapshot-1.png)

## Features

This tool provides the following monitoring capabilities:

- **CPU Information**: Usage percentage, core count, and detailed CPU info
- **Memory Information**: Virtual and swap memory usage
- **Disk Information**: Disk usage, partitions, and I/O statistics
- **Network Information**: Network interfaces, connections, and traffic statistics
- **Host Information**: System details, uptime, boot time, and users
- **Process Information**: Process listing, sorting, and detailed per-process statistics


## Available Tools

### 1. CPU Information

```
Tool: get_cpu_info
Description: Get CPU information and usage
Parameters:
  - per_cpu (boolean, default: false): Whether to return data for each core
```

### 2. Memory Information

```
Tool: get_memory_info
Description: Get system memory usage information
Parameters: None
```

### 3. Disk Information

```
Tool: get_disk_info
Description: Get disk usage information
Parameters:
  - path (string, default: ""/""): Specify the disk path to query
  - all_partitions (boolean, default: false): Whether to return information for all partitions
```

### 4. Network Information

```
Tool: get_network_info
Description: Get network interface and traffic information
Parameters:
  - interface (string, optional): Specify the network interface name to query
```

### 5. Host Information

```
Tool: get_host_info
Description: Get host system information
Parameters: None
```

### 6. Process Information

```
Tool: get_process_info
Description: Get process information
Parameters:
  - pid (number, optional): Process ID to get detailed information for a specific process
  - limit (number, default: 10): Limit the number of processes returned
  - sort_by (string, default: ""cpu""): Sort field (cpu, memory, pid, name)
```


## Installation

```bash
git clone https://github.com/seekrays/mcp-monitor.git
cd mcp-monitor
make build
```

## Usage

Run the compiled binary:

```bash
./mcp-monitor
```

The server starts in stdio mode, ready to communicate with an MCP-compatible LLM client.


## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. ","Star
 42",2025-06-24 07:59:40.920526,mcp-monitor - MCP Server | Model Context Protocol Integration,"# MCP System Monitor

[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)

A system moni...","['mcp server', 'model context protocol', 'ai integration', 'mcp-monitor']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-monitor', 'description': '# MCP System Monitor\n\n[![Discord](https://img.shields.io/badge/Discord-Join%20Chat-blue?style=flat&logo=discord)](https://discord.gg/kbMJ9Qpf)\n\nA system moni...', 'url': 'https://github.com/seekrays/mcp-monitor', 'codeRepository': 'https://github.com/seekrays/mcp-monitor', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",TypeScript,Automation,Time Management,,,,,,,seekrays,A system monitoring tool that exposes system metrics via the Model Context Protocol (MCP). This tool allows LLMs to retrieve real-time system information through an MCP-compatible interface.
https://github.com/jjsantos01/jupyter-notebook-mcp,jjsantos01/jupyter-notebook-mcp,"# JupyterMCP - Jupyter Notebook Model Context Protocol Integration

JupyterMCP connects [Jupyter Notebook](https://jupyter.org/) to [Claude AI](https://claude.ai/chat) through the Model Context Protocol (MCP), allowing Claude to directly interact with and control Jupyter Notebooks. This integration enables AI-assisted code execution, data analysis, visualization, and more.

## ⚠️ Compatibility Warning

**This tool is compatible ONLY with Jupyter Notebook version 6.x.**

It does NOT work with:

- Jupyter Lab
- Jupyter Notebook v7.x
- VS Code Notebooks
- Google Colab
- Any other notebook interfaces

## Features

- **Two-way communication**: Connect Claude AI to Jupyter Notebook through a WebSocket-based server
- **Cell manipulation**: Insert, execute, and manage notebook cells
- **Notebook management**: Save notebooks and retrieve notebook information
- **Cell execution**: Run specific cells or execute all cells in a notebook
- **Output retrieval**: Get output content from executed cells with text limitation options

## Components

The system consists of three main components:

1. **WebSocket Server (`jupyter_ws_server.py`)**: Sets up a WebSocket server inside Jupyter that bridges communication between notebook and external clients
2. **Client JavaScript (`client.js`)**: Runs in the notebook to handle operations (inserting cells, executing code, etc.)
3. **MCP Server (`jupyter_mcp_server.py`)**: Implements the Model Context Protocol and connects to the WebSocket server

## Installation

### Prerequisites

- [Python 3.12 or newer](https://www.python.org/downloads/) (probably also work with older versions, but not tested)
- [`uv` package manager](/README.md#installing-uv)
- [Claude AI desktop application](https://claude.ai/download)

#### Installing uv

If you're on Mac:

```bash
brew install uv
```

On Windows (PowerShell):

```bash
powershell -ExecutionPolicy ByPass -c ""irm https://astral.sh/uv/install.ps1 | iex""
```

For other platforms, see the [uv installation guide](https://docs.astral.sh/uv/getting-started/installation/).

### Setup

1. Clone or download this repository to your computer:

   ```bash
   git clone https://github.com/jjsantos01/jupyter-notebook-mcp.git
   ```

2. Create virtual environment with required packages an install `jupyter-mcp` kernel, so it can be recognized by your jupyter installation, if you had one before.

   ```bash
   uv run python -m ipykernel install --name jupyter-mcp
   ```

3. (optional) Install additional Python packages for your analysis:

   ```bash
   uv pip install seaborn
   ```

4. Configure Claude desktop integration:
   Go to `Claude` > `Settings` > `Developer` > `Edit Config` > `claude_desktop_config.json` to include the following:

   ```json
      {
       ""mcpServers"": {
           ""jupyter"": {
               ""command"": ""uv"",
               ""args"": [
                   ""--directory"",
                   ""/ABSOLUTE/PATH/TO/PARENT/REPO/FOLDER/src"",
                   ""run"",
                   ""jupyter_mcp_server.py""
               ]
           }
       }
   }
   ```

   Replace `/ABSOLUTE/PATH/TO/` with the actual path to the `src` folder on your system. For example:
   - Windows: `""C:\\Users\\<USER>\\GitHub\\jupyter-notebook-mcp\\src\\""`
   - Mac: `/Users/<USER>/GitHub/jupyter-notebook-mcp/src/`

   If you had previously opened Claude, then `File` > `Exit` and open it again.

## Usage

### Starting the Connection

1. Start your Jupyter Notebook (version 6.x) server:

   ```bash
   uv run jupyter nbclassic
   ```

2. Create a new Jupyter Notebook and make sure that you choose the `jupyter-mcp` kernel: `kernel` -> `change kernel` -> `jupyter-mcp`

3. In a notebook cell, run the following code to initialize the WebSocket server:

   ```python
   import sys
   sys.path.append('/path/to/jupyter-notebook-mcp/src')  # Add the path to where the scripts are located
   
   from jupyter_ws_server import setup_jupyter_mcp_integration
   
   # Start the WebSocket server inside Jupyter
   server, port = setup_jupyter_mcp_integration()
   ```

   Don't forget to replace here `'/path/to/jupyter-notebook-mcp/src'` with `src` folder on your system. For example:
   - Windows: `""C:\\Users\\<USER>\\GitHub\\jupyter-notebook-mcp\\src\\""`
   - Mac: `/Users/<USER>/GitHub/jupyter-notebook-mcp/src/`

   ![Notebook setup](/assets/img/notebook-setup.png)

4. Launch Claude desktop with MCP enabled.

### Using with Claude

Once connected, Claude will have access to the following tools:

- `ping` - Check server connectivity
- `insert_and_execute_cell` - Insert a cell at the specified position and execute it
- `save_notebook` - Save the current Jupyter notebook
- `get_cells_info` - Get information about all cells in the notebook
- `get_notebook_info` - Get information about the current notebook
- `run_cell` - Run a specific cell by its index
- `run_all_cells` - Run all cells in the notebook
- `get_cell_text_output` - Get the output content of a specific cell
- `get_image_output` - Get the images output of a specific cell
- `edit_cell_content` - Edit the content of an existing cell
- `set_slideshow_type`- Set the slide show type for cell

## ⚠️ DISCLAIMER

This is an experimental project and should be used with caution. This tool runs arbitrary Python code in your computer, which could potentially modify or delete data if not used carefully. Always back up your important projects and data.

## Example Prompts

Ask Claude to perform notebook operations:

### Python example

You can check the [example notebook](/notebooks/example_notebook.ipynb) and the [video demo](https://x.com/jjsantoso/status/1906780778807390562)

```plain
You have access to a Jupyter Notebook server.

I need to create a presentation about Python's Seaborn library.  
The content is as follows:

- What is Seaborn?
- Long vs. Wide data format
- Advantages of Seaborn over Matplotlib
- Commonly used Seaborn functions
- Live demonstration (comparison of Seaborn vs. Matplotlib)
  - Bar plot
  - Line plot
  - Scatter plot

For each concept, I want the main explanations provided in markdown cells, followed by one or more Python code cells demonstrating its usage. Keep the text concise—the cells shouldn't exceed 10 lines each.

Use appropriate slideshow types for each cell to make the presentation visually appealing.
```

[Check Here the full conversation](https://claude.ai/share/420b6aa6-b84b-437f-a6a6-89d310c36d52)

### Stata example

For this example, you need the [Stata Software](https://www.stata.com/) (v17 or later), which is not open source. If you already have Stata, you need to install the [`stata-setup`](https://pypi.org/project/stata-setup/) package:

```bash
uv pip install stata-setup
```

Then, at the begining of your notebook, you need to additionally include:

```python
import stata_setup
stata_setup.config('your_stata_installation_directory', 'your_stata_edition')
```

You can check the [example notebook](/notebooks/stata_example.ipynb) and the [video demo](https://x.com/jjsantoso/status/1906780784800731251)

This exercise comes from [Professor John Robert Warren webpage](https://www.rob-warren.com/soc3811_stata_exercises.html)

```plain
You have access to a Jupyter Notebook server. By default it runs Python, but you can run Stata (v18) code in this server using the %%stata magic, for example:

%%stata
display ""hello world""

Run the available tools to solve the exercise, execute the code, and interpret the results.

**EXERCISE:**

In this exercise, you will use data from the American Community Survey (ACS). The ACS is a product of the U.S. Census Bureau and involves interviewing millions of Americans each year. For an introduction to the ACS, visit the ACS website (here).

For this exercise, I have created a data file containing two variables collected from respondents of the 2010 ACS who lived in one of two metropolitan areas: Minneapolis/St Paul and Duluth/Superior. The two variables are: (1) People's poverty status and (2) the time it takes people to commute to work.

Use STATA syntax files you already have (from the first assignment or class examples) and modify them to accomplish the following goals.

1. Read the data file (`""./stata_assignment_2.dat""`) for this assignment into STATA.
2. Be sure to declare ""zero"" as a missing value for `TRANTIME`, the commuting time variable.
3. Create a new dichotomous poverty variable that equals ""1"" if a person's income-to-poverty-line ratio (`POVRATIO`) is less than 100, and ""0"" otherwise; see the bottom of the assignment for an example of how to do this in STATA.
4. Separately for Minneapolis/St Paul and Duluth/Superior, produce:
   - a histogram of the commuting time (`TRANTIME`) variable.
   - measures of central tendency and spread for commuting time.
   - a frequency distribution for the poverty status (0 vs 1) variable.
5. Separately for Minneapolis/St Paul and Duluth/Superior, use STATA code to produce:
   - a 95% confidence interval for the mean commuting time.
   - a 95% confidence interval for the proportion of people who are poor. See below for an example of how to do this in STATA.

Use the results from step #4 above to:

6. Separately for Minneapolis/St Paul and Duluth/Superior, manually calculate:
   - a 95% confidence interval for the mean commuting time.
   - a 95% confidence interval for the proportion of people who are poor.
7. Confirm that your answers from steps #5 and #6 match.

Based on the results above, answer this question:

8. How do you interpret the confidence intervals calculated in steps #5 and #6 above?

9. Finally, create a do fhttps://github.com/scrapeless-ai/scrapeless-mcp-server,scrapeless-ai/scrapeless-mcp-server,"![preview](./banner.png)

# Scrapeless Mcp Server

Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. MCP provides a standardized way to connect LLM with the required context, helping you efficiently enhance chat interfaces, build AI-driven IDEs, or create custom AI workflows.

Seamlessly integrate real-time Google SERP(Google Search, Google Flight, Google Map, Google Jobs....) results into your LLM applications using the Scrapeless MCP server. This server acts as a bridge between LLMs (like ChatGPT, Claude, etc.) and Scrapeless's Google SERP, enabling dynamic context retrieval for AI workflows, chatbots, and research tools.

👉 Live MCP Endpoint: 
- [mcp.so](https://mcp.so/server/scrapelessMcpServer/scrapeless-ai)
- [glama.ai](https://glama.ai/mcp/servers/@scrapeless-ai/scrapeless-mcp-server)

📦 NPM Package: [scrapeless-mcp-server](https://www.npmjs.com/package/scrapeless-mcp-server)

## Overview

This project provides several MCP servers that enable AI assistants like Claude to perform various search operations and retrieve data from:

- Google Search

## Tools

### 1. Search Tool
- Name: `google-search`
- Description: Search the web using Scrapeless
- Parameters:
    * `query` (required): Parameter defines the query you want to search. You can use anything that you would use in a regular Google search. e.g. inurl:, site:, intitle:.
    * `gl` (optional, default: ""us""): Parameter defines the country to use for the Google search. It's a two-letter country code. (e.g., us for the United States, uk for United Kingdom, or fr for France).
    * `hl` (optional, default: ""en""): Parameter defines the language to use for the Google search. It's a two-letter language code. (e.g., en for English, es for Spanish, or fr for French).


## Setup Guide

### 1. Get Scrapeless Key
1. Register at [Scrapeless](https://app.scrapeless.com/passport/register?utm_source=github&utm_medium=mcp)
2. [Get your free trial](https://app.scrapeless.com/landing/guide?utm_source=github&utm_medium=mcp)
3. [Generate API Key](https://app.scrapeless.com/dashboard/settings/api-key?utm_source=github&utm_medium=mcp)


### 2. Configure

```json
{
  ""mcpServers"": {
    ""scrapelessMcpServer"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""scrapeless-mcp-server""],
      ""env"": {
        ""SCRAPELESS_KEY"": ""YOUR_SCRAPELESS_KEY""
      }
    }
  }
}
```


## Example Queries

Here are some examples of how to use these servers with Claude Desktop:

### Google Search
```
Please search for ""climate change solutions"" and summarize the top results.
```


## Installation

### Prerequisites

- Node.js 22 or higher
- NPM or Yarn

### Install from Source

1. Clone the repository:
```bash
git clone https://github.com/scrapeless-ai/scrapeless-mcp-server.git
cd scrapeless-mcp-server
```

2. Install dependencies:
```bash
npm install
```


3. Build the server:
```bash
npm run build
```


## Community
- [MCP Server Discord](https://backend.scrapeless.com/app/api/v1/public/links/discord)","Star
 27",2025-06-24 07:59:40.920526,scrapeless-mcp-server - MCP Server | Model Context Protocol Integration,"![preview](./banner.png)

# Scrapeless Mcp Server

Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM application...","['mcp server', 'model context protocol', 'ai integration', 'scrapeless-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'scrapeless-mcp-server', 'description': '![preview](./banner.png)\n\n# Scrapeless Mcp Server\n\nModel Context Protocol (MCP) is an open protocol that enables seamless integration betwhttps://github.com/scrapeless-ai/scrapeless-mcp-server,scrapeless-ai/scrapeless-mcp-server,"![preview](./banner.png)

# Scrapeless Mcp Server

Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. MCP provides a standardized way to connect LLM with the required context, helping you efficiently enhance chat interfaces, build AI-driven IDEs, or create custom AI workflows.

Seamlessly integrate real-time Google SERP(Google Search, Google Flight, Google Map, Google Jobs....) results into your LLM applications using the Scrapeless MCP server. This server acts as a bridge between LLMs (like ChatGPT, Claude, etc.) and Scrapeless's Google SERP, enabling dynamic context retrieval for AI workflows, chatbots, and research tools.

👉 Live MCP Endpoint: 
- [mcp.so](https://mcp.so/server/scrapelessMcpServer/scrapeless-ai)
- [glama.ai](https://glama.ai/mcp/servers/@scrapeless-ai/scrapeless-mcp-server)

📦 NPM Package: [scrapeless-mcp-server](https://www.npmjs.com/package/scrapeless-mcp-server)

## Overview

This project provides several MCP servers that enable AI assistants like Claude to perform various search operations and retrieve data from:

- Google Search

## Tools

### 1. Search Tool
- Name: `google-search`
- Description: Search the web using Scrapeless
- Parameters:
    * `query` (required): Parameter defines the query you want to search. You can use anything that you would use in a regular Google search. e.g. inurl:, site:, intitle:.
    * `gl` (optional, default: ""us""): Parameter defines the country to use for the Google search. It's a two-letter country code. (e.g., us for the United States, uk for United Kingdom, or fr for France).
    * `hl` (optional, default: ""en""): Parameter defines the language to use for the Google search. It's a two-letter language code. (e.g., en for English, es for Spanish, or fr for French).


## Setup Guide

### 1. Get Scrapeless Key
1. Register at [Scrapeless](https://app.scrapeless.com/passport/register?utm_source=github&utm_medium=mcp)
2. [Get your free trial](https://app.scrapeless.com/landing/guide?utm_source=github&utm_medium=mcp)
3. [Generate API Key](https://app.scrapeless.com/dashboard/settings/api-key?utm_source=github&utm_medium=mcp)


### 2. Configure

```json
{
  ""mcpServers"": {
    ""scrapelessMcpServer"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""scrapeless-mcp-server""],
      ""env"": {
        ""SCRAPELESS_KEY"": ""YOUR_SCRAPELESS_KEY""
      }
    }
  }
}
```


## Example Queries

Here are some examples of how to use these servers with Claude Desktop:

### Google Search
```
Please search for ""climate change solutions"" and summarize the top results.
```


## Installation

### Prerequisites

- Node.js 22 or higher
- NPM or Yarn

### Install from Source

1. Clone the repository:
```bash
git clone https://github.com/scrapeless-ai/scrapeless-mcp-server.git
cd scrapeless-mcp-server
```

2. Install dependencies:
```bash
npm install
```


3. Build the server:
```bash
npm run build
```


## Community
- [MCP Server Discord](https://backend.scrapeless.com/app/api/v1/public/links/discord)","Star
 27",2025-06-24 07:59:40.920526,scrapeless-mcp-server - MCP Server | Model Context Protocol Integration,"![preview](./banner.png)

# Scrapeless Mcp Server

Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM application...","['mcp server', 'model context protocol', 'ai integration', 'scrapeless-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'scrapeless-mcp-server', 'description': '![preview](./banner.png)\n\n# Scrapeless Mcp Server\n\nModel Context Protocol (MCP) is an open protocol that enables seamless integration between LLM application...', 'url': 'https://github.com/scrapeless-ai/scrapeless-mcp-server', 'codeRepository': 'https://github.com/scrapeless-ai/scrapeless-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,AI Tools,Search Engine,npm install,Prerequisites,"{
  ""mcpServers"": {
    ""scrapelessMcpServer"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""scrapeless-mcp-server""],
      ""env"": {
        ""SCRAPELESS_KEY"": ""YOUR_SCRAPELESS_KEY""
      }
    }
  }
}",,,,scrapeless-ai,Model Context Protocol (MCP) is an open protocol that enables seamless integration between LLM applications and external data sources and tools. MCP provides a standardized way to connect LLM with the...
https://github.com/KyrieTangSheng/mcp-server-nationalparks,KyrieTangSheng/mcp-server-nationalparks,"# National Parks MCP Server
[![smithery badge](https://smithery.ai/badge/@KyrieTangSheng/mcp-server-nationalparks)](https://smithery.ai/server/@KyrieTangSheng/mcp-server-nationalparks)
[![Verified on MseeP](https://mseep.ai/badge.svg)](https://mseep.ai/app/8c07fa61-fd4b-4662-8356-908408e45e44)

MCP Server for the National Park Service (NPS) API, providing real-time information about U.S. National Parks, including park details, alerts, and activities.

## Tools

1. `findParks`
   - Search for national parks based on various criteria
   - Inputs:
     - `stateCode` (optional string): Filter parks by state code (e.g., ""CA"" for California). Multiple states can be comma-separated (e.g., ""CA,OR,WA"")
     - `q` (optional string): Search term to filter parks by name or description
     - `limit` (optional number): Maximum number of parks to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `activities` (optional string): Filter by available activities (e.g., ""hiking,camping"")
   - Returns: Matching parks with detailed information

2. `getParkDetails`
   - Get comprehensive information about a specific national park
   - Inputs:
     - `parkCode` (string): The park code of the national park (e.g., ""yose"" for Yosemite, ""grca"" for Grand Canyon)
   - Returns: Detailed park information including descriptions, hours, fees, contacts, and activities

3. `getAlerts`
   - Get current alerts for national parks including closures, hazards, and important information
   - Inputs:
     - `parkCode` (optional string): Filter alerts by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of alerts to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `q` (optional string): Search term to filter alerts by title or description
   - Returns: Current alerts organized by park

4. `getVisitorCenters`
   - Get information about visitor centers and their operating hours
   - Inputs:
     - `parkCode` (optional string): Filter visitor centers by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of visitor centers to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `q` (optional string): Search term to filter visitor centers by name or description
   - Returns: Visitor center information including location, hours, and contact details

5. `getCampgrounds`
   - Get information about available campgrounds and their amenities
   - Inputs:
     - `parkCode` (optional string): Filter campgrounds by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of campgrounds to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for pagination)
     - `q` (optional string): Search term to filter campgrounds by name or description
   - Returns: Campground information including amenities, fees, and reservation details

6. `getEvents`
   - Find upcoming events at parks
   - Inputs:
     - `parkCode` (optional string): Filter events by park code (e.g., ""yose"" for Yosemite). Multiple parks can be comma-separated (e.g., ""yose,grca"")
     - `limit` (optional number): Maximum number of events to return (default: 10, max: 50)
     - `start` (optional number): Start position for results (useful for paginathttps://github.com/IvanMurzak/Unity-MCP,IvanMurzak/Unity-MCP,"# Unity MCP (Server + Plugin)

[![openupm](https://img.shields.io/npm/v/com.ivanmurzak.unity.mcp?label=openupm&registry_uri=https://package.openupm.com)](https://openupm.com/packages/com.ivanmurzak.unity.mcp/) ![License](https://img.shields.io/github/license/IvanMurzak/Unity-MCP) [![Stand With Ukraine](https://raw.githubusercontent.com/vshymanskyy/StandWithUkraine/main/badges/StandWithUkraine.svg)](https://stand-with-ukraine.pp.ua)

![image](https://github.com/user-attachments/assets/8f595879-a578-421a-a06d-8c194af874f7)

| Unity Version | Editmode | Playmode | Standalone |
|---------------|----------|----------|------------|
| 2022.3.61f1   | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_editmode.yml?label=2022.3.61f1-editmode) | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_playmode.yml?label=2022.3.61f1-playmode) | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_standalone.yml?label=2022.3.61f1-standalone) |
| 2023.2.20f1   | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_editmode.yml?label=2023.2.20f1-editmode) | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_playmode.yml?label=2023.2.20f1-playmode) | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_standalone.yml?label=2023.2.20f1-standalone) |
| 6000.0.46f1   | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_editmode.yml?label=6000.0.46f1-editmode) | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_playmode.yml?label=6000.0.46f1-playmode) | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_standalone.yml?label=6000.0.46f1-standalone) |

**[Unity-MCP](https://github.com/IvanMurzak/Unity-MCP)** is a bridge between LLM and Unity. It exposes and explains to LLM Unity's tools. LLM understands the interface and utilizes the tools in the way a user asks.

Connect **[Unity-MCP](https://github.com/IvanMurzak/Unity-MCP)** to LLM client such as [Claude](https://claude.ai/download) or [Cursor](https://www.cursor.com/) using integrated `AI Connector` window. Custom clients are supported as well.

The project is designed to let developers to add custom tools soon. After that the next goal is to enable the same features in player's build. For not it works only in Unity Editor.

The system is extensible: you can define custom `tool`s directly in your Unity project codebase, exposing new capabilities to the AI or automation clients. This makes Unity-MCP a flexible foundation for building advanced workflows, rapid prototyping, or integrating AI-driven features into your development process.

## AI Tools

<table>
<tr>
<td valign=""top"">

### GameObject

- ✅ Create
- ✅ Destroy
- ✅ Find
- ✅ Modify (tag, layer, name, static)
- ✅ Set parent
- ✅ Duplicate

##### GameObject.Components

- ✅ Add Component
- ✅ Get Components
- ✅ Modify Component
- - ✅ `Field` set value
- - ✅ `Property` set value
- - ✅ `Reference` link set
- ✅ Destroy Component
- 🔲 Remove missing components

### Editor

- ✅ State (Playmode)
  - ✅ Get
  - ✅ Set
- ✅ Get Windows
- ✅ Layer
  - ✅ Get All
  - ✅ Add
  - ✅ Remove
- ✅ Tag
  - ✅ Get All
  - ✅ Add
  - ✅ Remove
- ✅ Execute `MenuItem`
- 🔲 Run Tests

#### Editor.Selection

- ✅ Get selection
- ✅ Set selection

### Prefabs

- ✅ Instantiate
- 🔲 Create
- ✅ Open
- ✅ Modify (GameObject.Modify)https://github.com/IvanMurzak/Unity-MCP,IvanMurzak/Unity-MCP,"# Unity MCP (Server + Plugin)

[![openupm](https://img.shields.io/npm/v/com.ivanmurzak.unity.mcp?label=openupm&registry_uri=https://package.openupm.com)](https://openupm.com/packages/com.ivanmurzak.unity.mcp/) ![License](https://img.shields.io/github/license/IvanMurzak/Unity-MCP) [![Stand With Ukraine](https://raw.githubusercontent.com/vshymanskyy/StandWithUkraine/main/badges/StandWithUkraine.svg)](https://stand-with-ukraine.pp.ua)

![image](https://github.com/user-attachments/assets/8f595879-a578-421a-a06d-8c194af874f7)

| Unity Version | Editmode | Playmode | Standalone |
|---------------|----------|----------|------------|
| 2022.3.61f1   | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_editmode.yml?label=2022.3.61f1-editmode) | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_playmode.yml?label=2022.3.61f1-playmode) | ![2022.3.61f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2022.3.61f1_standalone.yml?label=2022.3.61f1-standalone) |
| 2023.2.20f1   | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_editmode.yml?label=2023.2.20f1-editmode) | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_playmode.yml?label=2023.2.20f1-playmode) | ![2023.2.20f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/2023.2.20f1_standalone.yml?label=2023.2.20f1-standalone) |
| 6000.0.46f1   | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_editmode.yml?label=6000.0.46f1-editmode) | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_playmode.yml?label=6000.0.46f1-playmode) | ![6000.0.46f1](https://img.shields.io/github/actions/workflow/status/IvanMurzak/Unity-MCP/6000.0.46f1_standalone.yml?label=6000.0.46f1-standalone) |

**[Unity-MCP](https://github.com/IvanMurzak/Unity-MCP)** is a bridge between LLM and Unity. It exposes and explains to LLM Unity's tools. LLM understands the interface and utilizes the tools in the way a user asks.

Connect **[Unity-MCP](https://github.com/IvanMurzak/Unity-MCP)** to LLM client such as [Claude](https://claude.ai/download) or [Cursor](https://www.cursor.com/) using integrated `AI Connector` window. Custom clients are supported as well.

The project is designed to let developers to add custom tools soon. After that the next goal is to enable the same features in player's build. For not it works only in Unity Editor.

The system is extensible: you can define custom `tool`s directly in your Unity project codebase, exposing new capabilities to the AI or automation clients. This makes Unity-MCP a flexible foundation for building advanced workflows, rapid prototyping, or integrating AI-driven features into your development process.

## AI Tools

<table>
<tr>
<td valign=""top"">

### GameObject

- ✅ Create
- ✅ Destroy
- ✅ Find
- ✅ Modify (tag, layer, name, static)
- ✅ Set parent
- ✅ Duplicate

##### GameObject.Components

- ✅ Add Component
- ✅ Get Components
- ✅ Modify Component
- - ✅ `Field` set value
- - ✅ `Property` set value
- - ✅ `Reference` link set
- ✅ Destroy Component
- 🔲 Remove missing components

### Editor

- ✅ State (Playmode)
  - ✅ Get
  - ✅ Set
- ✅ Get Windows
- ✅ Layer
  - ✅ Get All
  - ✅ Add
  - ✅ Remove
- ✅ Tag
  - ✅ Get All
  - ✅ Add
  - ✅ Remove
- ✅ Execute `MenuItem`
- 🔲 Run Tests

#### Editor.Selection

- ✅ Get selection
- ✅ Set selection

### Prefabs

- ✅ Instantiate
- 🔲 Create
- ✅ Open
- ✅ Modify (GameObject.Modify)
- ✅ Save
- ✅ Close

### Package

- 🔲 Get installed
- 🔲 Install
- 🔲 Remove
- 🔲 Update

</td>
<td valign=""top"">

### Assets

- ✅ Create
- ✅ Find
- ✅ Refresh
- ✅ Read
- ✅ Modify
- ✅ Rename
- ✅ Delete
- ✅ Move
- ✅ Create folder

### Scene

- ✅ Create
- ✅ Save
- ✅ Load
- ✅ Unload
- ✅ Get Loaded
- ✅ Get hierarchy
- 🔲 Search (editor)
- 🔲 Raycast (understand volume)

### Materials

- ✅ Create
- ✅ Modify (Assets.Modify)
- ✅ Read (Assets.Read)
- ✅ Assign to a Component on a GameObject

### Shader

- ✅ List All

### Scripts

- ✅ Read
- ✅ Update or Create
- ✅ Delete

### Scriptable Object

- 🔲 Create
- 🔲 Read
- 🔲 Modify
- 🔲 Remove

### Debug

- 🔲 Read logs (console)

### Component

- ✅ Get All

</td>
</tr>
</table>

> **Legend:**
> ✅ = Implemented & available, 🔲 = Planned / Not yet implemented

# Installation

1. [Install .NET 9.0](https://dotnet.microsoft.com/en-us/download)
2. [Install OpenUPM-CLI](https://github.com/openupm/openupm-cli#installation)

- Open command line in Unity project folder
- Run the command

```bash
openupm add com.ivanmurzak.unity.mcp
```https://github.com/browsermcp/mcp,browsermcp/mcp,"<a href=""https://browsermcp.io"">
  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">
</a>

<h3 align=""center"">Browser MCP</h3>

<p align=""center"">
  Automate your browser with AI.
  <br />
  <a href=""https://browsermcp.io""><strong>Website</strong></a> 
  •
  <a href=""https://docs.browsermcp.io""><strong>Docs</strong></a>
</p>

## About

Browser MCP is an MCP server + Chrome extension that allows you to automate your browser using AI applications like VS Code, Claude, Cursor, and Windsurf.

## Features

- ⚡ Fast: Automation happens locally on your machine, resulting in better performance without network latency.
- 🔒 Private: Since automation happens locally, your browser activity stays on your device and isn't sent to remote servers.
- 👤 Logged In: Uses your existing browser profile, keeping you logged into all your services.
- 🥷🏼 Stealth: Avoids basic bot detection and CAPTCHAs by using your real browser fingerprint.

## Contributing

This repo contains all the core MCP code for Browser MCP, but currently cannot yet be built on its own due to dependencies on utils and types from the monorepo where it's developed.

## Credits

Browser MCP was adapted from the [Playwright MCP server](https://github.com/microsoft/playwright-mcp) in order to automate the user's browser rather than creating new browser instances. This allows using the user's existing browser profile to use logged-in sessions and avoid bot detection mechanisms that commonly block automated browser use.
","Star
 2.5k",2025-06-24 07:57:58.098202,mcp - MCP Server | Model Context Protocol Integration,"<a href=""https://browsermcp.io"">
  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">
</a>

<h3 align=""center"">Browser MCP</h3>

<p align=""cent...","['mcp server', 'model context protocol', 'ai integration', 'mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp', 'description': '<a href=""https://browsermcp.io"">\n  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">\n</a>\n\n<h3 align=""center"">Browser MCP</h3>\n\n<p align=""cent...', 'url': 'https://github.com/browsermcp/mcp', 'codeRepository': 'https://github.com/browsermcp/mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",C#,Development,Browser Automation,,,,,,,browsermcp,"<a href=""https://browsermcp.io"">"
https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server,hannesrudolph/imessage-query-fastmcp-mcp-server,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-imessage-query-fastmcp-mcp-server)

# iMessage Query MCP Server

An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to query and analyze iMessage conversations with proper phone number validation and automatic macOS permission handling.

## 📋 System Requirements

- macOS (required for iMessage database access)
- Python 3.12+ (required for modern type hints)
- **uv** (modern Python package manager)
- **Full Disk Access permission** for your MCP client (Claude Desktop, Cursor, VS Code, etc.)

## 📦 Dependencies

### Install uv (Required)

This project uses `uv` for fast, reliable Python package management. Install it first:

```bash
# Install uv using Homebrew (recommended)
brew install uv

# Or install using the official installer
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Python Dependencies

The script automatically manages its dependencies using the embedded metadata. No separate installation needed! Dependenchttps://github.com/browsermcp/mcp,browsermcp/mcp,"<a href=""https://browsermcp.io"">
  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">
</a>

<h3 align=""center"">Browser MCP</h3>

<p align=""center"">
  Automate your browser with AI.
  <br />
  <a href=""https://browsermcp.io""><strong>Website</strong></a> 
  •
  <a href=""https://docs.browsermcp.io""><strong>Docs</strong></a>
</p>

## About

Browser MCP is an MCP server + Chrome extension that allows you to automate your browser using AI applications like VS Code, Claude, Cursor, and Windsurf.

## Features

- ⚡ Fast: Automation happens locally on your machine, resulting in better performance without network latency.
- 🔒 Private: Since automation happens locally, your browser activity stays on your device and isn't sent to remote servers.
- 👤 Logged In: Uses your existing browser profile, keeping you logged into all your services.
- 🥷🏼 Stealth: Avoids basic bot detection and CAPTCHAs by using your real browser fingerprint.

## Contributing

This repo contains all the core MCP code for Browser MCP, but currently cannot yet be built on its own due to dependencies on utils and types from the monorepo where it's developed.

## Credits

Browser MCP was adapted from the [Playwright MCP server](https://github.com/microsoft/playwright-mcp) in order to automate the user's browser rather than creating new browser instances. This allows using the user's existing browser profile to use logged-in sessions and avoid bot detection mechanisms that commonly block automated browser use.
","Star
 2.5k",2025-06-24 07:57:58.098202,mcp - MCP Server | Model Context Protocol Integration,"<a href=""https://browsermcp.io"">
  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">
</a>

<h3 align=""center"">Browser MCP</h3>

<p align=""cent...","['mcp server', 'model context protocol', 'ai integration', 'mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp', 'description': '<a href=""https://browsermcp.io"">\n  <img src=""./.github/images/banner.png"" alt=""Browser MCP banner"">\n</a>\n\n<h3 align=""center"">Browser MCP</h3>\n\n<p align=""cent...', 'url': 'https://github.com/browsermcp/mcp', 'codeRepository': 'https://github.com/browsermcp/mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",C#,Development,Browser Automation,,,,,,,browsermcp,"<a href=""https://browsermcp.io"">"
https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server,hannesrudolph/imessage-query-fastmcp-mcp-server,"[![MseeP.ai Security Assessment Badge](https://mseep.net/pr/hannesrudolph-imessage-query-fastmcp-mcp-server-badge.png)](https://mseep.ai/app/hannesrudolph-imessage-query-fastmcp-mcp-server)

# iMessage Query MCP Server

An MCP server that provides safe access to your iMessage database through Model Context Protocol (MCP). This server is built with the FastMCP framework and the imessagedb library, enabling LLMs to query and analyze iMessage conversations with proper phone number validation and automatic macOS permission handling.

## 📋 System Requirements

- macOS (required for iMessage database access)
- Python 3.12+ (required for modern type hints)
- **uv** (modern Python package manager)
- **Full Disk Access permission** for your MCP client (Claude Desktop, Cursor, VS Code, etc.)

## 📦 Dependencies

### Install uv (Required)

This project uses `uv` for fast, reliable Python package management. Install it first:

```bash
# Install uv using Homebrew (recommended)
brew install uv

# Or install using the official installer
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Python Dependencies

The script automatically manages its dependencies using the embedded metadata. No separate installation needed! Dependencies include:

- **fastmcp**: Framework for building Model Context Protocol servers
- **imessagedb**: Python library for accessing and querying the macOS Messages database
- **phonenumbers**: Google's phone number handling library for proper number validation and formatting

All dependencies are automatically installed when the script runs via `uv`.

## 📑 Table of Contents
- [System Requirements](#-system-requirements)
- [Dependencies](#-dependencies)
- [MCP Tools](#%EF%B8%8F-mcp-tools)
- [Getting Started](#-getting-started)
- [Installation Options](#-installation-options)
  - [Claude Desktop](#option-1-install-for-claude-desktop)
  - [Cline VSCode Plugin](#option-2-install-for-cline-vscode-plugin)
- [macOS Permissions Setup](#-macos-permissions-setup)
- [Safety Features](#-safety-features)
- [Development Documentation](#-development-documentation)
- [Environment Variables](#%EF%B8%8F-environment-variables)

## 🛠️ MCP Tools

The server exposes the following tools to LLMs:

### get_chat_transcript
Retrieve message history for a specific phone number with optional date filtering.

**Parameters:**
- `phone_number` (required): Phone number in any format (E.164 format preferred)
- `start_date` (optional): Start date in ISO format (YYYY-MM-DD)
- `end_date` (optional): End date in ISO format (YYYY-MM-DD)

**Features:**
- Automatic phone number validation and formatting
- Message text and timestamps
- Attachment information with missing file detection
- Date range filtering (defaults to last 7 days if no dates specified)
- Sender identification (is_from_me flag)

## 🚀 Getting Started

Clone the repository:

```bash
git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server.git
cd imessage-query-fastmcp-mcp-server
```

## 📦 Installation Options

You can install this MCP server in Claude Desktop, Cline VSCode plugin, or any other MCP client. Choose the option that best suits your needs.

### Option 1: Claude Desktop

1. **Find your Claude Desktop config file:**
   - **Location**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Create the file if it doesn't exist

2. **Add the server configuration:**

```json
{
  ""mcpServers"": {
    ""imessage-query"": {
      ""command"": ""/full/path/to/imessage-query-server.py""
    }
  }
}
```

3. **Replace the path** with the full path to your cloned repository (e.g., `/Users/<USER>/Projects/imessage-query-fastmcp-mcp-server/imessage-query-server.py`)

4. **Restart Claude Desktop** completely (Cmd+Q, then relaunch)

### Option 2: Cline VSCode Plugin

To use this server with the [Cline VSCode plugin](http://cline.bot):

1. In VSCode, click the server icon (☰) in the Cline plugin sidebar
2. Click the ""Edit MCP Settings"" button (✎)
3. Add the following configuration to the settings file:

```json
{
  ""imessage-query"": {
    ""command"": ""/full/path/to/imessage-query-server.py""
  }
}
```

4. **Replace the path** with the full path to your cloned repository

### Option 3: Other MCP Clients

For other MCP clients, use the direct script path as the command:

```
/full/path/to/imessage-query-server.py
```

The script's shebang (`#!/usr/bin/env -S uv run --script`) handles dependency management automatically.

> **Note**: This simplified configuration replaces the previous FastMCP installation method. The script is now self-contained and manages its own dependencies through `uv`.

## 🔐 macOS Permissions Setup

This server requires **Full Disk Access** permission to read the iMessage database. The server includes intelligent permission detection and will guide you through the setup process.

### Automatic Permission Detection

When you first use the server, it will:
1. **Detect your MCP client** (Claude Desktop, Cursor, VS Code, etc.)
2. **Check for Full Disk Access** permission
3. **Automatically open System Preferences** to the correct settings panel
4. **Provide step-by-step instructions** specific to your application

### Manual Permission Setup

If automatic detection doesn't work, follow these steps:

1. **Open System Preferences** → **Privacy & Security** → **Full Disk Access**
2. **Click the lock icon** and enter your password to make changes
3. **Click the '+' button** to add an application
4. **Navigate to and select your MCP client:**
   - **Claude Desktop**: `/Applications/Claude.app`
   - **Cursor**: `/Applications/Cursor.app`
   - **VS Code**: `/Applications/Visual Studio Code.app`
5. **Restart your MCP client** completely (Cmd+Q, then relaunch)

### Common Issues

- **Permission denied errors**: Make sure you've restarted your MCP client after granting permission
- **""uv"" instead of app name**: The server will auto-detect your actual MCP client and provide correct instructions
- **Database not found**: Ensure you've used the Messages app and iMessage is enabled

### Security Note

This server only requires **read access** to your iMessage database. It cannot modify, delete, or send messages.

## 🔒 Safety Features

- **Read-only access** to the iMessage database (cannot modify, delete, or send messages)
- **Phone number validation** using Google's phonenumbers library with proper E.164 formatting
- **Safe attachment handling** with missing file detection and metadata extraction
- **Date range validation** to prevent invalid queries
- **Progress output suppression** for clean JSON responses in MCP protocol
- **Intelligent permission dhttps://github.com/oschina/gitee,oschina/gitee,,,2025-06-24 07:57:58.098202,gitee - MCP Server | Model Context Protocol Integration,,"['mcp server', 'model context protocol', 'ai integration', 'gitee']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'gitee', 'description': '', 'url': 'https://github.com/oschina/gitee', 'codeRepository': 'https://github.com/oschina/gitee', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Unknown,Other,,,,,,,,oschina,
https://github.com/nick1udwig/ws-mcp,nick1udwig/ws-mcp,"# ws-mcp

Wrap MCP stdio servers with a WebSocket.
For use with [kibitz](https://github.com/nick1udwig/kibitz).

## Quickstart

### Prerequisites

Install [uv](https://github.com/astral-sh/uv):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Configuration

The config file specifies which MCP servers to run.
The default config (no `--config` or `--command` args provided) includes:
- [`wcgw`](https://github.com/rusiaaman/wcgw): For general system operations and file management
- [`fetch`](https://github.com/modelcontextprotocol/servers/tree/main/src/fetch): For making HTTP requests

To make a configuration file:

1. Create your configuration file:
   ```bash
   cp sample.config.json config.json
   ```
2. Modify `config.json` to add or remove servers based on your needs.
3. Run with `--config path/to/config.json` to use the new config file.

### Running ws-mcp

Basic usage with default config file (no `--config` or `--command` provided) and port:
```bash
uvx --refresh ws-mcp@latest
```

This will start all configured servers on the default port (`10125`).

To use a config file and port:
```bash
uvx --refresh ws-mcp@latest --config path/to/config --port 10125
```

## Detailed Usage

```bash
# Example using fetch
uvx --refresh ws-mcp --command ""uvx mcp-server-fetch"" --port 3002

# Example using wcgw
## On macOS
uvx --refresh ws-mcp --command ""uvx --from wcgw@latest --python 3.12 wcgw_mcp"" --port 3001

## On Linux (or if you have issues on macOS with wcgw)
cd /tmp
git clone https://github.com/nick1udwig/wcgw.git
cd wcgw
git submodule update --init --recursive
git checkout hf/fix-wcgw-on-ubuntu
cd ..
uvx --refresh ws-mcp --command ""uvx --from /tmp/wcgw --with /tmp/wcgw/src/mcp_wcgw --python 3.12 wcgw_mcp"" --port 3001

# Example using Brave search
export BRAVE_API_KEY=YOUR_API_KEY_HERE
uvx --refresh ws-mcp --env BRAVE_API_KEY=$BRAVE_API_KEY --command ""npx -y @modelcontextprotocol/server-brave-search"" --port 3003

# Or, with a .env file:
uvx --refresh ws-mcp --env-file path/to/.env --command ""npx -y @modelcontextprotocol/server-brave-search"" --port 3003

# `--command` can be supplied multiple times!
#  Example serving multiple servers at once:
uvx --refresh ws-mcp --env-file path/to/.env --command ""npx -y @modelcontextprotocol/server-brave-search"" --command ""uvx mcp-server-fetch"" --port 3004

# Servers can also be specified in a `.json` file following [the standard MCP format](https://modelcontextprotocol.io/quickstart/user#2-add-the-filesystem-mcp-server)
uvx --refresh ws-mcp --env-file path/to/.env --config path/to/config.json --port 3005
```
","Star
 19",2025-06-24 07:57:58.098202,ws-mcp - MCP Server | Model Context Protocol Integration,"# ws-mcp

Wrap MCP stdio servers with a WebSocket.
For use with [kibitz](https://github.com/nick1udwig/kibitz).

## Quickstart

### Prerequisites

Install [u...","['mcp server', 'model context protocol', 'ai integration', 'ws-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'ws-mcp', 'description': '# ws-mcp\n\nWrap MCP stdio servers with a WebSocket.\nFor use with [kibitz](https://github.com/nick1udwig/kibitz).\n\n## Quickstart\n\n### Prerequisites\n\nInstall [u...', 'url': 'https://github.com/nick1https://github.com/oschina/gitee,oschina/gitee,,,2025-06-24 07:57:58.098202,gitee - MCP Server | Model Context Protocol Integration,,"['mcp server', 'model context protocol', 'ai integration', 'gitee']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'gitee', 'description': '', 'url': 'https://github.com/oschina/gitee', 'codeRepository': 'https://github.com/oschina/gitee', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Unknown,Other,,,,,,,,oschina,
https://github.com/nick1udwig/ws-mcp,nick1udwig/ws-mcp,"# ws-mcp

Wrap MCP stdio servers with a WebSocket.
For use with [kibitz](https://github.com/nick1udwig/kibitz).

## Quickstart

### Prerequisites

Install [uv](https://github.com/astral-sh/uv):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Configuration

The config file specifies which MCP servers to run.
The default config (no `--config` or `--command` args provided) includes:
- [`wcgw`](https://github.com/rusiaaman/wcgw): For general system operations and file management
- [`fetch`](https://github.com/modelcontextprotocol/servers/tree/main/src/fetch): For making HTTP requests

To make a configuration file:

1. Create your configuration file:
   ```bash
   cp sample.config.json config.json
   ```
2. Modify `config.json` to add or remove servers based on your needs.
3. Run with `--config path/to/config.json` to use the new config file.

### Running ws-mcp

Basic usage with default config file (no `--config` or `--command` provided) and port:
```bash
uvx --refresh ws-mcp@latest
```

This will start all configured servers on the default port (`10125`).

To use a config file and port:
```bash
uvx --refresh ws-mcp@latest --config path/to/config --port 10125
```

## Detailed Usage

```bash
# Example using fetch
uvx --refresh ws-mcp --command ""uvx mcp-server-fetch"" --port 3002

# Example using wcgw
## On macOS
uvx --refresh ws-mcp --command ""uvx --from wcgw@latest --python 3.12 wcgw_mcp"" --port 3001

## On Linux (or if you have issues on macOS with wcgw)
cd /tmp
git clone https://github.com/nick1udwig/wcgw.git
cd wcgw
git submodule update --init --recursive
git checkout hf/fix-wcgw-on-ubuntu
cd ..
uvx --refresh ws-mcp --command ""uvx --from /tmp/wcgw --with /tmp/wcgw/src/mcp_wcgw --python 3.12 wcgw_mcp"" --port 3001

# Example using Brave search
export BRAVE_API_KEY=YOUR_API_KEY_HERE
uvx --refresh ws-mcp --env BRAVE_API_KEY=$BRAVE_API_KEY --command ""npx -y @modelcontextprotocol/server-brave-search"" --port 3003

# Or, with a .env file:
uvx --refresh ws-mcp --env-file path/to/.env --command ""npx -y @modelcontextprotocol/server-brave-search"" --port 3003

# `--command` can be supplied multiple times!
#  Example serving multiple servers at once:
uvx --refresh ws-mcp --env-file path/to/.env --command ""npx -y @modelcontextprotocol/server-brave-search"" --command ""uvx mcp-server-fetch"" --port 3004

# Servers can also be specified in a `.json` file following [the standard MCP format](https://modelcontextprotocol.io/quickstart/user#2-add-the-filesystem-mcp-server)
uvx --refresh ws-mcp --env-file path/to/.env --config path/to/config.json --port 3005
```
","Star
 19",2025-06-24 07:57:58.098202,ws-mcp - MCP Server | Model Context Protocol Integration,"# ws-mcp

Wrap MCP stdio servers with a WebSocket.
For use with [kibitz](https://github.com/nick1udwig/kibitz).

## Quickstart

### Prerequisites

Install [u...","['mcp server', 'model context protocol', 'ai integration', 'ws-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'ws-mcp', 'description': '# ws-mcp\n\nWrap MCP stdio servers with a WebSocket.\nFor use with [kibitz](https://github.com/nick1udwig/kibitz).\n\n## Quickstart\n\n### Prerequisites\n\nInstall [u...', 'url': 'https://github.com/nick1udwig/ws-mcp', 'codeRepository': 'https://github.com/nick1udwig/ws-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,Development,Text Processing,,"Configuration, Prerequisites",,,,,nick1udwig,Wrap MCP stdio servers with a WebSocket.
https://github.com/pab1it0/adx-mcp-server,pab1it0/adx-mcp-server,"# Azure Data Explorer MCP Server

<a href=""https://glama.ai/mcp/servers/1yysyd147h"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/1yysyd147h/badge"" />
</a>

A [Model Context Protocol][mcp] (MCP) server for Azure Data Explorer/Eventhouse in Microsoft Fabric.

This provides access to your Azure Data Explorer/Eventhouse clusters and databases through standardized MCP interfaces, allowing AI assistants to execute KQL queries and explore your data.

[mcp]: https://modelcontextprotocol.io

## Features

- [x] Execute KQL queries against Azure Data Explorer
- [x] Discover and explore database resources
  - [x] List tables in the configured database
  - [x] View table schemas
  - [x] Sample data from tables
  - [x] Get table statistics/details

- [x] Authentication support
  - [x] Token credential support (Azure CLI, MSI, etc.)
  - [x] Workload Identity credential support for AKS
- [x] Docker containerization support

- [x] Provide interactive tools for AI assistants

The list of tools is configurable, so you can choose which tools you want to make available to the MCP client.
This is useful if you don't use certain functionality or if you don't want to take up too much of the context window.

## Usage

1. Login to your Azure account which has the permission to the ADX cluster using Azure CLI.

2. Configure the environment variables for your ADX cluster, either through a `.env` file or system environment variables:

```env
# Required: Azure Data Explorer configuration
ADX_CLUSTER_URL=https://yourcluster.region.kusto.windows.net
ADX_DATABASE=your_database

# Optional: Azure Workload Identity credentials 
# AZURE_TENANT_ID=your-tenant-id
# AZURE_CLIENT_ID=your-client-id 
# ADX_TOKEN_FILE_PATH=/var/run/secrets/azure/tokens/azure-identity-token
```

#### Azure Workload Identity Support

The server now uses WorkloadIdentityCredential by default when running in Azure Kubernetes Service (AKS) environments with workload identity configured. It prioritizes the use of WorkloadIdentityCredential whenever the necessary environment variables are present.

For AKS with Azure Workload Identity, you only need to:
1. Make sure the pod has `AZURE_TENANT_ID` and `AZURE_CLIENT_ID` environment variables set
2. Ensure the token file is mounted at the default path or specify a custom path with `ADX_TOKEN_FILE_PATH`

If these environment variables are not present, the server will automatically fall back to DefaultAzureCredential, which tries multiple authentication methods in sequence.

3. Add the server configuration to your client configuration file. For example, for Claude Desktop:

```json
{
  ""mcpServers"": {
    ""adx"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""<full path to adx-mcp-server directory>"",
        ""run"",
        ""src/adx_mcp_server/main.py""
      ],
      ""env"": {
        ""ADX_CLUSTER_URL"": ""https://yourcluster.region.kusto.windows.net"",
        ""ADX_DATABASE"": ""your_database""
      }
    }
  }
}
```

> Note: if you see `Error: spawn uv ENOENT` in Claude Desktop, you may need to specify the full path to `uv` or set the environment variable `NO_UV=1` in the configuration.

## Docker Usage

This project includes Docker support for easy deployment and isolation.

### Building the Docker Image

Build the Docker image using:

```bash
docker build -t adx-mcp-server .
```

### Running with Docker

You can run the server using Docker in several ways:

#### Using docker run directly:

```bash
docker run -it --rm \
  -e ADX_CLUSTER_URL=https://yourcluster.region.kusto.windows.net \
  -e ADX_DATABASE=your_database \
  -e AZURE_TENANT_ID=your_tenant_id \
  -e AZURE_CLIENT_ID=your_client_id \
  adx-mcp-server
```

#### Using docker-compose:

Create a `.env` file with your Azure Data Explorer credentials and then run:

```bash
docker-compose up
```

### Running with Docker in Claude Desktop

To use the containerized server with Claude Desktop, update the configuration to use Docker with the environment variables:

```json
{
  ""mcpServers"": {
    ""adx"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""--rm"",
        ""-i"",
        ""-e"", ""ADX_CLUSTER_URL"",
        ""-e"", ""ADX_DATABASE"",
        ""-e"", ""AZURE_TENANT_ID"",
        ""-e"", ""AZURE_CLIENT_ID"",
        ""-e"", ""ADX_TOKEN_FILE_PATH"",
        ""adx-mcp-server""
      ],
      ""env"": {
        ""ADX_CLUSTER_URL"": ""https://yourcluster.region.kusto.windows.net"",
        ""ADX_DATABASE"": ""your_database"",
        ""AZURE_TENANT_ID"": ""your_tenant_id"",
        ""AZURE_CLIENT_ID"": ""your_client_id"",
        ""ADX_TOKEN_FILE_PATH"": ""/var/run/secrets/azure/tokens/azure-identity-token""
      }
    }
  }
}
```

This configuration passes the environment variables from Claude Desktop to the Docker container by using the `-e` flag with just the variable name, and providing the actual values in the `env` object.

## Using as a Dev Container / GitHub Codespace

This repositohttps://github.com/integromat/make-mcp-server,integromat/make-mcp-server,"# Make MCP Server (legacy)

**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version](https://developers.make.com/mcp-server).**

A Model Context Protocol server that enables Make scenarios to be utilized as tools by AI assistants. This integration allows AI systems to trigger and interact with your Make automation workflows.

## How It Works

The MCP server:

-   Connects to your Make account and identifies all scenarios configured with ""On-Demand"" scheduling
-   Parses and resolves input parameters for each scenario, providing AI assistants with meaningful parameter descriptions
-   Allows AI assistants to invoke scenarios with appropriate parameters
-   Returns scenario output as structured JSON, enabling AI assistants to properly interpret the results

## Benefits

-   Turn your Make scenarios into callable tools for AI assistants
-   Maintain complex automation logic in Make while exposing functionality to AI systems
-   Create bidirectional communication between your AI assistants and your existing automation workflows

## Usage with Claude Desktop

### Prerequisites

-   NodeJS
-   MCP Client (like Claude Desktop App)
-   Make API Key with `scenarios:read` and `scenarios:run` scopes

### Installation

To use this server with the Claude Desktop app, add the following configuration to the ""mcpServers"" section of your `claude_desktop_config.json`:

```json
{
    ""mcpServers"": {
        ""make"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@makehq/mcp-server""],
            ""env"": {
                ""MAKE_API_KEY"": ""<your-api-key>"",
                ""MAKE_ZONE"": ""<your-zone>"",
                ""MAKE_TEAM"": ""<your-team-id>""
            }
        }
    }
}
```

-   `MAKE_API_KEY` - You can generate an API key in your Make profile.
-   `MAKE_ZONE` - The zone your organization is hosted in (e.g., `eu2.make.com`).
-   `MAKE_TEAM` - You can find the ID in the URL of the Team page.
","Star
 107",2025-06-24 07:57:58.098202,make-mcp-server - MCP Server | Model Context Protocol Integration,"# Make MCP Server (legacy)

**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version...","['mcp server', 'model context protocol', 'ai integration', 'make-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'make-mcp-server', 'description': '# Make MCP Server (legacy)\n\n**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version...', 'url': 'https://github.com/integromat/make-mcp-server', 'codeRepository': 'https://github.com/integromat/make-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,AI Tools,Claude Desktop,,"Installation, Prerequisites","{
    ""mcpServers"": {
        ""make"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@makehq/mcp-server""],
            ""env"": {
                ""MAKE_API_KEY"": ""<your-api-key>"",
                ""MAKE_ZONE"": ""<your-zone>"",
                ""MAKE_TEAM"": ""<your-team-id>""
            }
        }
    }
}",,,,integromat,"A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using this new version."
https://github.com/wong2/mcp-cli,wong2/mcp-cli,"# mcp-cli

A CLI inspector for the Model Context Protocol

https://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183

## Features

- Run MCP servers from various sources
- List Tools, Resources, Prompts
- Call Tools, Read Resources, Read Prompts
- OAuth support for SSE and https://github.com/integromat/make-mcp-server,integromat/make-mcp-server,"# Make MCP Server (legacy)

**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version](https://developers.make.com/mcp-server).**

A Model Context Protocol server that enables Make scenarios to be utilized as tools by AI assistants. This integration allows AI systems to trigger and interact with your Make automation workflows.

## How It Works

The MCP server:

-   Connects to your Make account and identifies all scenarios configured with ""On-Demand"" scheduling
-   Parses and resolves input parameters for each scenario, providing AI assistants with meaningful parameter descriptions
-   Allows AI assistants to invoke scenarios with appropriate parameters
-   Returns scenario output as structured JSON, enabling AI assistants to properly interpret the results

## Benefits

-   Turn your Make scenarios into callable tools for AI assistants
-   Maintain complex automation logic in Make while exposing functionality to AI systems
-   Create bidirectional communication between your AI assistants and your existing automation workflows

## Usage with Claude Desktop

### Prerequisites

-   NodeJS
-   MCP Client (like Claude Desktop App)
-   Make API Key with `scenarios:read` and `scenarios:run` scopes

### Installation

To use this server with the Claude Desktop app, add the following configuration to the ""mcpServers"" section of your `claude_desktop_config.json`:

```json
{
    ""mcpServers"": {
        ""make"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@makehq/mcp-server""],
            ""env"": {
                ""MAKE_API_KEY"": ""<your-api-key>"",
                ""MAKE_ZONE"": ""<your-zone>"",
                ""MAKE_TEAM"": ""<your-team-id>""
            }
        }
    }
}
```

-   `MAKE_API_KEY` - You can generate an API key in your Make profile.
-   `MAKE_ZONE` - The zone your organization is hosted in (e.g., `eu2.make.com`).
-   `MAKE_TEAM` - You can find the ID in the URL of the Team page.
","Star
 107",2025-06-24 07:57:58.098202,make-mcp-server - MCP Server | Model Context Protocol Integration,"# Make MCP Server (legacy)

**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version...","['mcp server', 'model context protocol', 'ai integration', 'make-mcp-server']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'make-mcp-server', 'description': '# Make MCP Server (legacy)\n\n**A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using [this new version...', 'url': 'https://github.com/integromat/make-mcp-server', 'codeRepository': 'https://github.com/integromat/make-mcp-server', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Rust,AI Tools,Claude Desktop,,"Installation, Prerequisites","{
    ""mcpServers"": {
        ""make"": {
            ""command"": ""npx"",
            ""args"": [""-y"", ""@makehq/mcp-server""],
            ""env"": {
                ""MAKE_API_KEY"": ""<your-api-key>"",
                ""MAKE_ZONE"": ""<your-zone>"",
                ""MAKE_TEAM"": ""<your-team-id>""
            }
        }
    }
}",,,,integromat,"A modern, cloud-based version of the Make MCP Server is now available. For most use cases, we recommend using this new version."
https://github.com/wong2/mcp-cli,wong2/mcp-cli,"# mcp-cli

A CLI inspector for the Model Context Protocol

https://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183

## Features

- Run MCP servers from various sources
- List Tools, Resources, Prompts
- Call Tools, Read Resources, Read Prompts
- OAuth support for SSE and Streamable HTTP servers

## Usage

### Run without arguments

```bash
npx @wong2/mcp-cli
```

This will use the config file of Claude Desktop.

### Run with a config file

```bash
npx @wong2/mcp-cli -c config.json
```

The config file has the same format as the Claude Desktop config file.

### Run servers from NPM

```bash
npx @wong2/mcp-cli npx <package-name> <args>
```

### Run locally developed server

```bash
npx @wong2/mcp-cli node path/to/server/index.js args...
```

### Connect to a running server over Streamable HTTP

```bash
npx @wong2/mcp-cli --url http://localhost:8000/mcp
```

### Connect to a running server over SSE

```bash
npx @wong2/mcp-cli --sse http://localhost:8000/sse
```

### Purge stored data (OAuth tokens, etc.)

```bash
npx @wong2/mcp-cli purge
```

## Related

- [mcpservers.org](https://mcpservers.org) - A curated list of MCP servers
","Star
 344",2025-06-24 07:57:58.098202,mcp-cli - MCP Server | Model Context Protocol Integration,"# mcp-cli

A CLI inspector for the Model Context Protocol

https://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183

## Features

- Ru...","['mcp server', 'model context protocol', 'ai integration', 'mcp-cli']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'mcp-cli', 'description': '# mcp-cli\n\nA CLI inspector for the Model Context Protocol\n\nhttps://github.com/user-attachments/assets/4cd113e9-f097-4c9d-b391-045c5f213183\n\n## Features\n\n- Ru...', 'url': 'https://github.com/wong2/mcp-cli', 'codeRepository': 'https://github.com/wong2/mcp-cli', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",JavaScript,File Management,Claude Desktop,,,,,,,wong2,A CLI inspector for the Model Context Protocol
https://github.com/r-huijts/oorlogsbronnen-mcp,r-huijts/oorlogsbronnen-mcp,"# Oorlogsbronnen MCP Server

A Model Context Protocol (MCP) server that provides AI-powered access to the Oorlogsbronnen (War Sources) database. This server enables natural language interactions with historical World War II archives from the Netherlands.

## Natural Language Interaction Examples

Ask your AI assistant questions like these to explore Dutch WWII history:

- **""What happened during the bombing of Rotterdam in May 1940?""**
- **""Tell me about Anne Frank's life in hiding based on historical records.""**
- **""Show me photographs of the Dutch Hunger Winter of 1944-1945.""**
- **""Were any of my ancestors imprisoned in Camp Vught during the war?""**
- **""I'm visiting Arnhem next week. What historical sites related to Operation Market Garden should I see?""**
- **""Find information about resistance activities in Utrecht during the Nazi occupation.""**
- **""What was daily life like for Jewish families in Amsterdam before deportations began?""**
- **""Show me firsthand accounts from people who witnessed the liberation of the Netherlands in 1945.""**
- **""What records exist about children who were hidden by Dutch families during the war?""**
- **""I'm researching the impact of WWII on Dutch infrastructure. Can you find documents about the reconstruction of bridges and railways?""**

## Features

- 🔍 Natural language search across the Oorlogsbronnen database
- 🏷️ Filter results by content type (person, photo, article, etc.)
- 📊 Control the number of results returned
- 🤖 AI-friendly JSON responses for further processing

## Installation

You can install this server in two ways:

### 1. Using Claude Desktop with NPX Package

Update your Claude configuration file (`~/Library/Application Support/Claude/claude_desktop_config.json`):

```json
{
  ""mcpServers"": {
    ""oorlogsbronnen-server"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""oorlogsbronnen-mcp""
      ]
    }
  }
}
```

After updating the configuration, restart Claude Desktop for the changes to take effect.

### 2. From Source

1. Clone this repository:
```bash
git clone https://github.com/r-huijts/oorlogsbronnen-mcp.git
cd oorlogsbronnen-mcp
```

2. Install dependencies:
```bash
npm install
```

3. Build the project:
```bash
npm run build
```

4. Configure Claude Desktop by updating your configuration file (located at `~/Library/Application Support/Claude/claude_desktop_config.json`):

```json
{
  ""mcpServers"": {
    ""oorlogsbronnen-server"": {
      ""command"": ""node"",
      ""args"": [
        ""/absolute/path/to/oorlogsbronnen-mcp/dist/mcp-server.js""
      ]
    }
  }
}
```

Replace `/absolute/path/to/oorlogsbronnen-mcp` with the actual path to your installation.

## Usage Examples

The MCP server understands natural language queries and can help you explore World War II archives. Here are some example queries you can use with Claude:

### Basic Searches

- ""Use search_ww2_nl_archives to find documents about the resistance movement in Amsterdam""
- ""Search the Dutch WW2 archives for information about Jewish refugees in 1942""
- ""Look through the Netherlands war archives for records of Allied bombing raids""

### Filtering by Type

- ""Use search_ww2_nl_archives to show me photographs of the liberation of Rotterdam""
- ""Find personal accounts in the Dutch WW2 archives about life in concentration camps""
- ""Search the Netherlands war archives for newspaper articles about food shortages""

### Specific Queries

- ""Search the Dutch WW2 archives for documents about Anne Frank's time in Amsterdam""
- ""Use search_ww2_nl_archives to find records of the February Strike of 1941""
- ""Look through the Netherlands war archives for information about Operation Market Garden""

### Research Examples

1. **Personal History Research**:
   ```
   Use search_ww2_nl_archives to find any records or documents about the Rosenberg family in Amsterdam between 1940-1945
   ```

2. **Local History**:
   ```
   Search the Dutch WW2 archives for photographs and documents about daily life in Utrecht during the occupation
   ```

3. **Military Operations**:
   ```
   Use search_ww2_nl_archives to find firsthand accounts and official reports about the Battle of the Scheldt
   ```

### Advanced Usage

You can combine different search criteria:
```
Search the Netherlands WW2 archives for photographs and personal accounts of the Dutch famine in 1944-1945, limit to 20 results
```

## API Reference

The server exposes the following MCP tool:

### search_ww2_nl_archives

A powerful search tool designed to query the Oorlogsbronnen (War Sources) database for World War II related content in the Netherlands. This tool can be used to find historical documents, photographs, personal accounts, and other archival materials from 1940-1945.

**When to use this tool:**
- Searching for specific historical events during WWII in the Netherlands
- Finding information about people, places, or organizations during the war
- Locating photographs or documents from specific time periods or locations
- Researching personal or family history related to WWII
- Finding primary sources about the Dutch resistance, occupation, or liberation
- Discovering materials about Jewish life and persecution during the war
- Researching military operations that took place in the Netherlands

Parameters:
- `query` (required): 
  - Type: string
  - Description: The main search term or phrase to look for in the archives
  - Can include: names, places, dates, events, or descriptive terms
  - Examples:
    - ""Anne Frank""
    - ""Rotterdam bombing 1940""
    - ""Dutch resistance Amsterdam""
    - ""Jewish deportation Westerbork""
    - ""Operation Market Garden""

- `type` (optional):
  - Type: string
  - Description: Filter results by specific content type
  - Available types:
    - ""person"": Individual biographttps://github.com/r-huijts/firstcycling-mcp,r-huijts/firstcycling-mcp,"# FirstCycling MCP Server

This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve comprehensive information about professional cyclists, race results, race details, and historical cycling data.

## Features

This MCP server offers rich access to professional cycling data, providing tools for:

- Finding information about professional cyclists
- Retrieving race results and details
- Exploring historical race data
- Analyzing rider performance and career progression
- Accessing information about cycling teams and competitions

## Real-World Use Cases

With this MCP server, you can use Claude to:

### Rider Analysis

- **Performance Tracking**: ""How has Tadej Pogačar performed in the Tour de France over the years?""
- **Career Progression**: ""Show me the team history and career progression of Wout van Aert.""
- **Specialization Analysis**: ""What are Mathieu van der Poel's results in Monument classics?""
- **Victory Analysis**: ""List all WorldTour victories for Jonas Vingegaard.""
- **Historical Comparison**: ""Compare the Grand Tour results of Primož Roglič and Jonas Vingegaard.""

### Race Research

- **Recent Results**: ""Show me the results of the 2023 Paris-Roubaix.""
- **Historical Context**: ""Who are the youngest and oldest winners of the Tour of Flanders?""
- **Team Analysis**: ""Get the startlist for the 2023 Tour de France with detailed team information.""
- **Race Statistics**: ""Show me the victory table for Liège-Bastogne-Liège. Who has won it the most times?""
- **Stage Information**: ""Can you show me the stage profiles for the 2023 Giro d'Italia?""

### Sports Journalism

- ""Create a detailed profile of Remco Evenepoel for a cycling magazine article.""
- ""Write a preview for the upcoming Tour de France based on the recent results of top contenders like Tadej Pogačar and Jonas Vingegaard.""
- ""Analyze the evolution of Tom Pidcock's career based on his race results and team history.""

### Cycling Education

- ""Explain what makes the Monument classics special using data about their history and winners.""
- ""Create an educational summary about Grand Tours and their significance in professional cycling.""
- ""Describe the typical career progression of a professional cyclist using examples from the data.""

## Requirements

- Python 3.10 or higher
- `uv` package manager (recommended)
- Dependencies as listed in `pyproject.toml`, including:
  - mcp
  - beautifulsoup4
  - lxml
  - pandas
  - slumber
  - and other packages for web scraping and data processing

## Setup

1. Clone this repository
2. Create and activate a virtual environment:
   ```
   uv venv
   source .venv/bin/activate  # On macOS/Linux
   # or
   .venv\Scripts\activate  # On Windows
   ```
3. Install dependencies:
   ```
   uv pip install -e .
   ```

## FirstCycling API

This server uses the [FirstCycling API](https://github.com/baronet2/FirstCyclingAPI), which has been integrated directly into the project. The API provides methods to fetch data from the FirstCycling website through web scraping.

## MCP Tools

The server exposes the following tools through the Model Context Protocol:

### Rider Information

| Tool | Description |
|------|-------------|
| `get_rider_info` | Get basic biographical information about a rider including nationality, birthdate, weight, height, and current team |
| `get_rider_best_results` | Retrieve a rider's best career results, sorted by importance |
| `get_rider_grand_tour_results` | Get a rider's results in Grand Tours (Tour de France, Giro d'Italia, Vuelta a España) |
| `get_rider_monument_results` | Retrieve a rider's results in cycling's Monument classics |
| `get_rider_team_https://github.com/r-huijts/firstcycling-mcp,r-huijts/firstcycling-mcp,"# FirstCycling MCP Server

This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve comprehensive information about professional cyclists, race results, race details, and historical cycling data.

## Features

This MCP server offers rich access to professional cycling data, providing tools for:

- Finding information about professional cyclists
- Retrieving race results and details
- Exploring historical race data
- Analyzing rider performance and career progression
- Accessing information about cycling teams and competitions

## Real-World Use Cases

With this MCP server, you can use Claude to:

### Rider Analysis

- **Performance Tracking**: ""How has Tadej Pogačar performed in the Tour de France over the years?""
- **Career Progression**: ""Show me the team history and career progression of Wout van Aert.""
- **Specialization Analysis**: ""What are Mathieu van der Poel's results in Monument classics?""
- **Victory Analysis**: ""List all WorldTour victories for Jonas Vingegaard.""
- **Historical Comparison**: ""Compare the Grand Tour results of Primož Roglič and Jonas Vingegaard.""

### Race Research

- **Recent Results**: ""Show me the results of the 2023 Paris-Roubaix.""
- **Historical Context**: ""Who are the youngest and oldest winners of the Tour of Flanders?""
- **Team Analysis**: ""Get the startlist for the 2023 Tour de France with detailed team information.""
- **Race Statistics**: ""Show me the victory table for Liège-Bastogne-Liège. Who has won it the most times?""
- **Stage Information**: ""Can you show me the stage profiles for the 2023 Giro d'Italia?""

### Sports Journalism

- ""Create a detailed profile of Remco Evenepoel for a cycling magazine article.""
- ""Write a preview for the upcoming Tour de France based on the recent results of top contenders like Tadej Pogačar and Jonas Vingegaard.""
- ""Analyze the evolution of Tom Pidcock's career based on his race results and team history.""

### Cycling Education

- ""Explain what makes the Monument classics special using data about their history and winners.""
- ""Create an educational summary about Grand Tours and their significance in professional cycling.""
- ""Describe the typical career progression of a professional cyclist using examples from the data.""

## Requirements

- Python 3.10 or higher
- `uv` package manager (recommended)
- Dependencies as listed in `pyproject.toml`, including:
  - mcp
  - beautifulsoup4
  - lxml
  - pandas
  - slumber
  - and other packages for web scraping and data processing

## Setup

1. Clone this repository
2. Create and activate a virtual environment:
   ```
   uv venv
   source .venv/bin/activate  # On macOS/Linux
   # or
   .venv\Scripts\activate  # On Windows
   ```
3. Install dependencies:
   ```
   uv pip install -e .
   ```

## FirstCycling API

This server uses the [FirstCycling API](https://github.com/baronet2/FirstCyclingAPI), which has been integrated directly into the project. The API provides methods to fetch data from the FirstCycling website through web scraping.

## MCP Tools

The server exposes the following tools through the Model Context Protocol:

### Rider Information

| Tool | Description |
|------|-------------|
| `get_rider_info` | Get basic biographical information about a rider including nationality, birthdate, weight, height, and current team |
| `get_rider_best_results` | Retrieve a rider's best career results, sorted by importance |
| `get_rider_grand_tour_results` | Get a rider's results in Grand Tours (Tour de France, Giro d'Italia, Vuelta a España) |
| `get_rider_monument_results` | Retrieve a rider's results in cycling's Monument classics |
| `get_rider_team_and_ranking` | Get a rider's team history and UCI ranking evolution over time |
| `get_rider_race_history` | Retrieve a rider's complete race participation history, optionally filtered by year |
| `get_rider_one_day_races` | Get a rider's results in one-day races, optionally filtered by year |
| `get_rider_stage_races` | Get a rider's results in multi-day stage races, optionally filtered by year |
| `get_rider_teams` | Retrieve the complete team history of a rider throughout their career |
| `get_rider_victories` | Get a list of a rider's career victories, with optional filters for WorldTour or UCI races |

### Race Information

| Tool | Description |
|------|-------------|
| `get_race_results` | Retrieve results for a specific race edition by race ID and year |
| `get_race_overview` | Get general information about a race including history, records, and past winners |
| `get_race_stage_profiles` | Retrieve stage profiles and details for multi-stage races |
| `get_race_startlist` | Get the startlist for a specific race edition with detailed or basic team information |
| `get_race_victory_table` | Retrieve the all-time victory table for a race showing riders with most wins |
| `get_race_year_by_year` | Get year-by-year results for a race with optional classification filter |
| `get_race_youngest_oldest_winners` | Retrieve information about the youngest and oldest winners of a race |
| `get_race_stage_victories` | Get information about stage victories in multi-stage races |

### Search Tools

| Tool | Description |
|------|-------------|
| `search_rider` | Search for riders by name, returning their IDs and basic information |
| `search_race` | Search for races by name, returning their IDs and basic information |

## Usage

### Development Mode

You can test the server with MCP Inspector by running:

```
uv run mcp dev firstcycling.py
```

This will start the server and open the MCP Inspector in your browser, allowing you to test the available tools.

### Integration with Claude for Desktop

To integrate this server with Claude for Desktop:

1. Edit the Claude for Desktop config file, located at:
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. Add the server to your configuration:
   ```json
   {
     ""mcpServers"": {
       ""firstcycling"": {
         ""command"": ""uv"",
         ""args"": [""--directory"", ""/path/to/server/directory"", ""run"", ""firstcycling.py""]
       }
     }
   }
   ```

3. Restart Claude for Desktop

## License

MIT
","Star
 9",2025-06-24 07:57:58.098202,firstcycling-mcp - MCP Server | Model Context Protocol Integration,"# FirstCycling MCP Server

This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve...","['mcp server', 'model context protocol', 'ai integration', 'firstcycling-mcp']","{'@context': 'https://schema.org', '@type': 'SoftwareApplication', 'name': 'firstcycling-mcp', 'description': '# FirstCycling MCP Server\n\nThis is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve...', 'url': 'https://github.com/r-huijts/firstcycling-mcp', 'codeRepository': 'https://github.com/r-huijts/firstcycling-mcp', 'keywords': ['mcp server', 'model context protocol', 'ai integration']}",Python,Development,Search Engine,pip install -e .,,"""firstcycling"": {
         ""command"": ""uv"",
         ""args"": [""--directory"", ""/path/to/server/directory"", ""run"", ""firstcycling.py""]",-e,MIT,,r-huijts,"This is a Model Context Protocol (MCP) server that provides professional cycling data from FirstCycling. It allows you to retrieve comprehensive information about professional cyclists, race results, ..."
