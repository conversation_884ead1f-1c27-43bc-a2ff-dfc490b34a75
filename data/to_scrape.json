[{"url": "https://github.com/Dumpling-AI/mcp-server-dumplingai", "stored_at": "2025-06-24T08:43:26.213665"}, {"url": "https://github.com/MarketplaceAdPros/amazon-ads-mcp-server", "stored_at": "2025-06-24T08:43:26.213718"}, {"url": "https://github.com/0xshellming/mcp-summarizer", "stored_at": "2025-06-24T08:43:26.213721"}, {"url": "https://github.com/ahnlabio/bicscan-mcp", "stored_at": "2025-06-24T08:43:26.213723"}, {"url": "https://github.com/ac3xx/mcp-servers-kagi", "stored_at": "2025-06-24T08:43:26.213725"}, {"url": "https://github.com/pab1it0/chess-mcp", "stored_at": "2025-06-24T08:43:26.213727"}, {"url": "https://github.com/horw/esp-mcp", "stored_at": "2025-06-24T08:43:26.213729"}, {"url": "https://github.com/oraios/serena", "stored_at": "2025-06-24T08:43:26.213731"}, {"url": "https://github.com/micl2e2/code-to-tree", "stored_at": "2025-06-24T08:43:26.213733"}, {"url": "https://github.com/SaintDoresh/Crypto-Trader-MCP-ClaudeDesktop.git", "stored_at": "2025-06-24T08:43:26.213735"}, {"url": "https://github.com/MarkusPfundstein/mcp-obsidian", "stored_at": "2025-06-24T08:43:26.213737"}, {"url": "https://github.com/benborla/mcp-server-mysql", "stored_at": "2025-06-24T08:43:26.213739"}, {"url": "https://github.com/mem0ai/mem0-mcp", "stored_at": "2025-06-24T08:43:26.213741"}, {"url": "https://github.com/rad-security/mcp-server", "stored_at": "2025-06-24T08:43:26.213743"}, {"url": "https://github.com/Gaffx/volatility-mcp", "stored_at": "2025-06-24T08:43:26.213745"}, {"url": "https://github.com/ananddtyagi/webpage-screenshot-mcp", "stored_at": "2025-06-24T08:43:26.213747"}, {"url": "https://github.com/kiwamizamurai/mcp-kibela-server", "stored_at": "2025-06-24T08:43:26.213749"}, {"url": "https://github.com/JoshuaRileyDev/simulator-mcp-server", "stored_at": "2025-06-24T08:43:26.213751"}, {"url": "https://github.com/olalonde/mcp-human", "stored_at": "2025-06-24T08:43:26.213753"}, {"url": "https://github.com/zcaceres/gtasks-mcp", "stored_at": "2025-06-24T08:43:26.213755"}, {"url": "https://github.com/Flux159/mcp-server-kubernetes", "stored_at": "2025-06-24T08:43:26.213757"}, {"url": "https://github.com/hbg/mcp-paperswithcode", "stored_at": "2025-06-24T08:43:26.213759"}, {"url": "https://github.com/OctagonAI/octagon-mcp-server", "stored_at": "2025-06-24T08:43:26.213761"}, {"url": "https://github.com/StacklokLabs/osv-mcp", "stored_at": "2025-06-24T08:43:26.213763"}, {"url": "https://github.com/jwaxman19/qlik-mcp", "stored_at": "2025-06-24T08:43:26.213765"}, {"url": "https://github.com/andybrandt/mcp-simple-timeserver", "stored_at": "2025-06-24T08:43:26.213767"}, {"url": "https://github.com/zcaceres/markdownify-mcp", "stored_at": "2025-06-24T08:43:26.213769"}, {"url": "https://github.com/neondatabase/mcp-server-neon", "stored_at": "2025-06-24T08:43:26.213771"}, {"url": "https://github.com/getalby/nwc-mcp-server", "stored_at": "2025-06-24T08:43:26.213773"}, {"url": "https://github.com/magarcia/mcp-server-giphy", "stored_at": "2025-06-24T08:43:26.213775"}, {"url": "https://github.com/fosdickio/binary_ninja_mcp", "stored_at": "2025-06-24T08:43:26.213777"}, {"url": "https://github.com/crystaldba/postgres-mcp", "stored_at": "2025-06-24T08:43:26.213779"}, {"url": "https://github.com/pulumi/mcp-server", "stored_at": "2025-06-24T08:43:26.213780"}, {"url": "https://github.com/kukapay/crypto-indicators-mcp", "stored_at": "2025-06-24T08:43:26.213782"}, {"url": "https://github.com/nickpending/mcp-recon", "stored_at": "2025-06-24T08:43:26.213784"}, {"url": "https://github.com/antvis/mcp-server-chart", "stored_at": "2025-06-24T08:43:26.213786"}, {"url": "https://github.com/kagisearch/kagimcp", "stored_at": "2025-06-24T08:43:26.213788"}, {"url": "https://github.com/stass/lldb-mcp", "stored_at": "2025-06-24T08:43:26.213790"}, {"url": "https://github.com/ConechoAI/openai-websearch-mcp", "stored_at": "2025-06-24T08:43:26.213792"}, {"url": "https://github.com/snaggle-ai/openapi-mcp-server", "stored_at": "2025-06-24T08:43:26.213794"}, {"url": "https://github.com/suekou/mcp-notion-server", "stored_at": "2025-06-24T08:43:26.213796"}, {"url": "https://github.com/jasonjmcghee/claude-debugs-for-you", "stored_at": "2025-06-24T08:43:26.213853"}, {"url": "https://github.com/SaintDoresh/YFinance-Trader-MCP-ClaudeDesktop.git", "stored_at": "2025-06-24T08:43:26.213860"}, {"url": "https://github.com/g0t4/mcp-server-commands", "stored_at": "2025-06-24T08:43:26.213863"}, {"url": "https://github.com/ergut/mcp-bigquery-server", "stored_at": "2025-06-24T08:43:26.213865"}, {"url": "https://github.com/wonderwhy-er/DesktopCommanderMCP", "stored_at": "2025-06-24T08:43:26.213867"}, {"url": "https://github.com/carterlasalle/mac_messages_mcp", "stored_at": "2025-06-24T08:43:26.213869"}, {"url": "https://github.com/tanigami/mcp-server-perplexity", "stored_at": "2025-06-24T08:43:26.213872"}, {"url": "https://github.com/kukapay/crypto-whitepapers-mcp", "stored_at": "2025-06-24T08:43:26.213874"}, {"url": "https://github.com/kaliaboi/mcp-zotero", "stored_at": "2025-06-24T08:43:26.213876"}, {"url": "https://github.com/ChristianHinge/dicom-mcp", "stored_at": "2025-06-24T08:43:26.213878"}, {"url": "https://github.com/neo4j-contrib/mcp-neo4j", "stored_at": "2025-06-24T08:43:26.213880"}, {"url": "https://github.com/kukapay/pancakeswap-poolspy-mcp", "stored_at": "2025-06-24T08:43:26.213882"}, {"url": "https://github.com/minhyeoky/mcp-server-ledger", "stored_at": "2025-06-24T08:43:26.213884"}, {"url": "https://github.com/qianniuspace/mcp-security-audit", "stored_at": "2025-06-24T08:43:26.213886"}, {"url": "https://github.com/kukapay/blockbeats-mcp", "stored_at": "2025-06-24T08:43:26.213888"}, {"url": "https://github.com/Rootly-AI-Labs/Rootly-MCP-server", "stored_at": "2025-06-24T08:43:26.213890"}, {"url": "https://github.com/KS-GEN-AI/confluence-mcp-server", "stored_at": "2025-06-24T08:43:26.213892"}, {"url": "https://github.com/yikakia/godoc-mcp-server", "stored_at": "2025-06-24T08:43:26.213894"}, {"url": "https://github.com/arpitbatra123/mcp-googletasks", "stored_at": "2025-06-24T08:43:26.213896"}, {"url": "https://github.com/atomicchonk/roadrecon_mcp_server", "stored_at": "2025-06-24T08:43:26.213898"}, {"url": "https://github.com/azer/react-analyzer-mcp", "stored_at": "2025-06-24T08:43:26.213900"}, {"url": "https://github.com/luminati-io/brightdata-mcp", "stored_at": "2025-06-24T08:43:26.213902"}, {"url": "https://github.com/kaiyuanxiaobing/atomgit-mcp-server", "stored_at": "2025-06-24T08:43:26.213903"}, {"url": "https://github.com/macrocosm-os/macrocosmos-mcp", "stored_at": "2025-06-24T08:43:26.213906"}, {"url": "https://github.com/willvelida/mcp-afl-server", "stored_at": "2025-06-24T08:43:26.213908"}, {"url": "https://github.com/Bankless/onchain-mcp", "stored_at": "2025-06-24T08:43:26.213910"}, {"url": "https://github.com/jedisct1/fastly-openapi-schema", "stored_at": "2025-06-24T08:43:26.213912"}, {"url": "https://github.com/Bigsy/Clojars-MCP-Server", "stored_at": "2025-06-24T08:43:26.213914"}, {"url": "https://github.com/narumiruna/gitingest-mcp", "stored_at": "2025-06-24T08:43:26.213916"}, {"url": "https://github.com/emicklei/melrose-mcp", "stored_at": "2025-06-24T08:43:26.213918"}, {"url": "https://github.com/punkpeye/fastmcp", "stored_at": "2025-06-24T08:43:26.213920"}, {"url": "https://github.com/exa-labs/exa-mcp-server", "stored_at": "2025-06-24T08:43:26.213922"}, {"url": "https://github.com/nguyenvanduocit/jira-mcp", "stored_at": "2025-06-24T08:43:26.213924"}, {"url": "https://github.com/ihor-sokoliuk/mcp-searxng", "stored_at": "2025-06-24T08:43:26.213926"}, {"url": "https://github.com/HenryH<PERSON>son/Yuque-MCP-Server", "stored_at": "2025-06-24T08:43:26.213928"}, {"url": "https://github.com/BurtTheCoder/mcp-virustotal", "stored_at": "2025-06-24T08:43:26.213931"}, {"url": "https://github.com/burningion/video-editing-mcp", "stored_at": "2025-06-24T08:43:26.213933"}, {"url": "https://github.com/xzq-xu/jvm-mcp-server", "stored_at": "2025-06-24T08:43:26.213935"}, {"url": "https://github.com/apinetwork/piapi-mcp-server", "stored_at": "2025-06-24T08:43:26.213936"}, {"url": "https://github.com/redis/mcp-redis", "stored_at": "2025-06-24T08:43:26.213938"}, {"url": "https://github.com/InhiblabCore/mcp-image-compression", "stored_at": "2025-06-24T08:43:26.213940"}, {"url": "https://github.com/heurist-network/heurist-mesh-mcp-server", "stored_at": "2025-06-24T08:43:26.213942"}, {"url": "https://github.com/zinja-coder/apktool-mcp-server", "stored_at": "2025-06-24T08:43:26.213945"}, {"url": "https://github.com/YuChenSSR/multi-ai-advisor-mcp", "stored_at": "2025-06-24T08:43:26.213947"}, {"url": "https://github.com/pollinations/chucknorris-mcp", "stored_at": "2025-06-24T08:43:26.213949"}, {"url": "https://github.com/XGenerationLab/xiyan_mcp_server", "stored_at": "2025-06-24T08:43:26.213951"}, {"url": "https://github.com/jinzcdev/markmap-mcp-server", "stored_at": "2025-06-24T08:43:26.213953"}, {"url": "https://github.com/ZeparHyfar/mcp-datetime", "stored_at": "2025-06-24T08:43:26.213955"}, {"url": "https://github.com/kukapay/uniswap-poolspy-mcp", "stored_at": "2025-06-24T08:43:26.213957"}, {"url": "https://github.com/kukapay/jupiter-mcp", "stored_at": "2025-06-24T08:43:26.213959"}, {"url": "https://github.com/anjor/coinmarket-mcp-server", "stored_at": "2025-06-24T08:43:26.213960"}, {"url": "https://github.com/samuelgu<PERSON>y/davinci-resolve-mcp", "stored_at": "2025-06-24T08:43:26.213963"}, {"url": "https://github.com/takashi<PERSON>da/arxiv-latex-mcp", "stored_at": "2025-06-24T08:43:26.213965"}, {"url": "https://github.com/aliyun/alibaba-cloud-ops-mcp-server", "stored_at": "2025-06-24T08:43:26.213967"}, {"url": "https://github.com/YCloud-Developers/ycloud-whatsapp-mcp-server", "stored_at": "2025-06-24T08:43:26.213969"}, {"url": "https://github.com/Dataring-engineering/mcp-server-trino", "stored_at": "2025-06-24T08:43:26.213971"}, {"url": "https://github.com/teddyzxcv/ntfy-mcp", "stored_at": "2025-06-24T08:43:26.213974"}, {"url": "https://github.com/Shopify/dev-mcp", "stored_at": "2025-06-24T08:43:26.213976"}, {"url": "https://github.com/comet-ml/opik-mcp", "stored_at": "2025-06-24T08:43:26.213977"}, {"url": "https://github.com/tinybirdco/mcp-tinybird", "stored_at": "2025-06-24T08:43:26.213979"}, {"url": "https://github.com/kelvin6365/plane-mcp-server", "stored_at": "2025-06-24T08:43:26.213981"}, {"url": "https://github.com/kukapay/nearby-search-mcp", "stored_at": "2025-06-24T08:43:26.214060"}, {"url": "https://github.com/CodeLogicIncEngineering/codelogic-mcp-server", "stored_at": "2025-06-24T08:43:26.214063"}, {"url": "https://github.com/briandconnelly/mcp-server-ipinfo", "stored_at": "2025-06-24T08:43:26.214065"}, {"url": "https://github.com/isaacphi/mcp-language-server", "stored_at": "2025-06-24T08:43:26.214067"}, {"url": "https://github.com/Coding-Solo/godot-mcp", "stored_at": "2025-06-24T08:43:26.214069"}, {"url": "https://github.com/ktanaka101/mcp-server-duckdb", "stored_at": "2025-06-24T08:43:26.214071"}, {"url": "https://github.com/openMF/mcp-mifosx", "stored_at": "2025-06-24T08:43:26.214073"}, {"url": "https://github.com/JordanDalton/DoorDash-MCP-Server", "stored_at": "2025-06-24T08:43:26.214075"}, {"url": "https://github.com/adhikasp/mcp-twikit", "stored_at": "2025-06-24T08:43:26.214077"}, {"url": "https://github.com/ipfred/aiwen-mcp-server-geoip", "stored_at": "2025-06-24T08:43:26.214079"}, {"url": "https://github.com/ckreiling/mcp-server-docker", "stored_at": "2025-06-24T08:43:26.214081"}, {"url": "https://github.com/ddukbg/github-enterprise-mcp", "stored_at": "2025-06-24T08:43:26.214083"}, {"url": "https://github.com/hyperb1iss/droidmind", "stored_at": "2025-06-24T08:43:26.214085"}, {"url": "https://github.com/alimo7amed93/webhook-tester-mcp", "stored_at": "2025-06-24T08:43:26.214087"}, {"url": "https://github.com/metoro-io/metoro-mcp-server", "stored_at": "2025-06-24T08:43:26.214089"}, {"url": "https://github.com/QuantGeekDev/mongo-mcp", "stored_at": "2025-06-24T08:43:26.214091"}, {"url": "https://github.com/kopfrechner/gitlab-mr-mcp", "stored_at": "2025-06-24T08:43:26.214093"}, {"url": "https://github.com/esignaturescom/mcp-server-esignatures", "stored_at": "2025-06-24T08:43:26.214095"}, {"url": "https://github.com/bytebase/dbhub", "stored_at": "2025-06-24T08:43:26.214097"}, {"url": "https://github.com/pydantic/logfire-mcp", "stored_at": "2025-06-24T08:43:26.214099"}, {"url": "https://github.com/laukikk/alpaca-mcp", "stored_at": "2025-06-24T08:43:26.214101"}, {"url": "https://github.com/gbrigandi/mcp-server-cortex", "stored_at": "2025-06-24T08:43:26.214103"}, {"url": "https://github.com/cantian-ai/bazi-mcp", "stored_at": "2025-06-24T08:43:26.214105"}, {"url": "https://github.com/sirmews/mcp-pinecone", "stored_at": "2025-06-24T08:43:26.214107"}, {"url": "https://github.com/polygon-io/mcp_polygon", "stored_at": "2025-06-24T08:43:26.214109"}, {"url": "https://github.com/datalayer/jupyter-mcp-server", "stored_at": "2025-06-24T08:43:26.214111"}, {"url": "https://github.com/Automata-Labs-team/MCP-Server-Playwright", "stored_at": "2025-06-24T08:43:26.214113"}, {"url": "https://github.com/designcomputer/mysql_mcp_server", "stored_at": "2025-06-24T08:43:26.214115"}, {"url": "https://github.com/jagan-shanmugam/open-streetmap-mcp", "stored_at": "2025-06-24T08:43:26.214118"}, {"url": "https://github.com/billster45/mcp-chatgpt-responses", "stored_at": "2025-06-24T08:43:26.214120"}, {"url": "https://github.com/isnow890/naver-search-mcp", "stored_at": "2025-06-24T08:43:26.214122"}, {"url": "https://github.com/andybrandt/mcp-simple-pubmed", "stored_at": "2025-06-24T08:43:26.214124"}, {"url": "https://github.com/21st-dev/magic-mcp", "stored_at": "2025-06-24T08:43:26.214126"}, {"url": "https://github.com/hydrolix/mcp-hydrolix", "stored_at": "2025-06-24T08:43:26.214128"}, {"url": "https://github.com/runekaagaard/mcp-alchemy", "stored_at": "2025-06-24T08:43:26.214130"}, {"url": "https://github.com/higress-group/higress-ops-mcp-server", "stored_at": "2025-06-24T08:43:26.214132"}, {"url": "https://github.com/SecretiveShell/MCP-searxng", "stored_at": "2025-06-24T08:43:26.214134"}, {"url": "https://github.com/takumi0706/google-calendar-mcp", "stored_at": "2025-06-24T08:43:26.214136"}, {"url": "https://github.com/ckanthony/gin-mcp", "stored_at": "2025-06-24T08:43:26.214139"}, {"url": "https://github.com/getrupt/ashra-mcp", "stored_at": "2025-06-24T08:43:26.214141"}, {"url": "https://github.com/rember/rember-mcp", "stored_at": "2025-06-24T08:43:26.214142"}, {"url": "https://github.com/langfuse/mcp-server-langfuse", "stored_at": "2025-06-24T08:43:26.214144"}, {"url": "https://github.com/OpenLinkSoftware/mcp-sqlalchemy-server", "stored_at": "2025-06-24T08:43:26.214146"}, {"url": "https://github.com/idosal/git-mcp", "stored_at": "2025-06-24T08:43:26.214149"}, {"url": "https://github.com/ckanthony/openapi-mcp", "stored_at": "2025-06-24T08:43:26.214151"}, {"url": "https://github.com/kukapay/bridge-rates-mcp", "stored_at": "2025-06-24T08:43:26.214152"}, {"url": "https://github.com/IlyaGulya/gradle-mcp-server", "stored_at": "2025-06-24T08:43:26.214154"}, {"url": "https://github.com/BurtTheCoder/mcp-shodan", "stored_at": "2025-06-24T08:43:26.214156"}, {"url": "https://github.com/ivo-toby/contentful-mcp", "stored_at": "2025-06-24T08:43:26.214158"}, {"url": "https://github.com/hyperb1iss/lucidity-mcp", "stored_at": "2025-06-24T08:43:26.214160"}, {"url": "https://github.com/tevonsb/homeassistant-mcp", "stored_at": "2025-06-24T08:43:26.214162"}, {"url": "https://github.com/glenngillen/mcpmcp-server", "stored_at": "2025-06-24T08:43:26.214165"}, {"url": "https://github.com/PV-Bhat/vibe-check-mcp-server", "stored_at": "2025-06-24T08:43:26.214167"}, {"url": "https://github.com/Harry-027/JotDown", "stored_at": "2025-06-24T08:43:26.214169"}, {"url": "https://github.com/xing5/mcp-google-sheets", "stored_at": "2025-06-24T08:43:26.214170"}, {"url": "https://github.com/grafana/mcp-grafana", "stored_at": "2025-06-24T08:43:26.214172"}, {"url": "https://github.com/QuantGeekDev/docker-mcp", "stored_at": "2025-06-24T08:43:26.214174"}, {"url": "https://github.com/thunderboltsid/mcp-nutanix", "stored_at": "2025-06-24T08:43:26.214176"}, {"url": "https://github.com/nictuku/meta-ads-mcp", "stored_at": "2025-06-24T08:43:26.214178"}, {"url": "https://github.com/SureScaleAI/openai-gpt-image-mcp", "stored_at": "2025-06-24T08:43:26.214180"}, {"url": "https://github.com/hardik-id/azure-resource-graph-mcp-server", "stored_at": "2025-06-24T08:43:26.214182"}, {"url": "https://github.com/graphlit/graphlit-mcp-server", "stored_at": "2025-06-24T08:43:26.214185"}, {"url": "https://github.com/firstorderai/authenticator_mcp", "stored_at": "2025-06-24T08:43:26.214187"}, {"url": "https://github.com/hannesrudolph/sqlite-explorer-fastmcp-mcp-server", "stored_at": "2025-06-24T08:43:26.214189"}, {"url": "https://github.com/8enSmith/mcp-open-library", "stored_at": "2025-06-24T08:43:26.214191"}, {"url": "https://github.com/cyclops-ui/mcp-cyclops", "stored_at": "2025-06-24T08:43:26.214193"}, {"url": "https://github.com/kj455/mcp-kibela", "stored_at": "2025-06-24T08:43:26.214195"}, {"url": "https://github.com/angheljf/nyt", "stored_at": "2025-06-24T08:43:26.214197"}, {"url": "https://github.com/cswkim/discogs-mcp-server", "stored_at": "2025-06-24T08:43:26.214199"}, {"url": "https://github.com/0xDAEF0F/job-searchoor", "stored_at": "2025-06-24T08:43:26.214201"}, {"url": "https://github.com/jagan-shanmugam/mattermost-mcp-host", "stored_at": "2025-06-24T08:43:26.214204"}, {"url": "https://github.com/QuantGeekDev/coincap-mcp", "stored_at": "2025-06-24T08:43:26.214206"}, {"url": "https://github.com/opgginc/opgg-mcp", "stored_at": "2025-06-24T08:43:26.214208"}, {"url": "https://github.com/apify/mcp-server-rag-web-browser", "stored_at": "2025-06-24T08:43:26.214210"}, {"url": "https://github.com/tomekkorbak/oura-mcp-server", "stored_at": "2025-06-24T08:43:26.214212"}, {"url": "https://github.com/cjo4m06/mcp-shrimp-task-manager", "stored_at": "2025-06-24T08:43:26.214214"}, {"url": "https://github.com/tumf/web3-mcp", "stored_at": "2025-06-24T08:43:26.214216"}, {"url": "https://github.com/UnitVectorY-Labs/mcp-graphql-forge", "stored_at": "2025-06-24T08:43:26.214218"}, {"url": "https://github.com/iaptic/mcp-server-iaptic", "stored_at": "2025-06-24T08:43:26.214221"}, {"url": "https://github.com/joshuayoes/ios-simulator-mcp", "stored_at": "2025-06-24T08:43:26.214222"}, {"url": "https://github.com/executeautomation/mcp-playwright", "stored_at": "2025-06-24T08:43:26.214225"}, {"url": "https://github.com/hellokaton/unsplash-mcp-server", "stored_at": "2025-06-24T08:43:26.214227"}, {"url": "https://github.com/adhikasp/mcp-git-ingest", "stored_at": "2025-06-24T08:43:26.214229"}, {"url": "https://github.com/longevity-genie/biothings-mcp", "stored_at": "2025-06-24T08:43:26.214231"}, {"url": "https://github.com/JoshuaRileyDev/app-store-connect-mcp-server", "stored_at": "2025-06-24T08:43:26.214233"}, {"url": "https://github.com/co-browser/attestable-mcp-server", "stored_at": "2025-06-24T08:43:26.214235"}, {"url": "https://github.com/tobymao/sqlglot", "stored_at": "2025-06-24T08:43:26.214237"}, {"url": "https://github.com/skysqlinc/skysql-mcp", "stored_at": "2025-06-24T08:43:26.214239"}, {"url": "https://github.com/currents-dev/currents-mcp", "stored_at": "2025-06-24T08:43:26.214241"}, {"url": "https://github.com/blazickjp/arxiv-mcp-server", "stored_at": "2025-06-24T08:43:26.214243"}, {"url": "https://github.com/blurrah/mcp-graphql", "stored_at": "2025-06-24T08:43:26.214245"}, {"url": "https://github.com/translated/lara-mcp", "stored_at": "2025-06-24T08:43:26.214247"}, {"url": "https://github.com/gwbischof/bluesky-social-mcp", "stored_at": "2025-06-24T08:43:26.214249"}, {"url": "https://github.com/tumf/mcp-shell-server", "stored_at": "2025-06-24T08:43:26.214251"}, {"url": "https://github.com/ivnvxd/mcp-server-odoo", "stored_at": "2025-06-24T08:43:26.214253"}, {"url": "https://github.com/mcpdotdirect/starknet-mcp-server", "stored_at": "2025-06-24T08:43:26.214255"}, {"url": "https://github.com/jdubois/azure-cli-mcp", "stored_at": "2025-06-24T08:43:26.214257"}, {"url": "https://github.com/HagaiHen/facebook-mcp-server", "stored_at": "2025-06-24T08:43:26.214259"}, {"url": "https://github.com/mark3labs/mcp-filesystem-server", "stored_at": "2025-06-24T08:43:26.214261"}, {"url": "https://github.com/effytech/freshdesk_mcp", "stored_at": "2025-06-24T08:43:26.214263"}, {"url": "https://github.com/YuChenSSR/mindmap-mcp-server", "stored_at": "2025-06-24T08:43:26.214265"}, {"url": "https://github.com/InditexTech/mcp-server-simulator-ios-idb", "stored_at": "2025-06-24T08:43:26.214267"}, {"url": "https://github.com/xspadex/bilibili-mcp.git", "stored_at": "2025-06-24T08:43:26.214269"}, {"url": "https://github.com/TheRaLabs/legion-mcp", "stored_at": "2025-06-24T08:43:26.214318"}, {"url": "https://github.com/kenliao94/mcp-server-rabbitmq", "stored_at": "2025-06-24T08:43:26.214321"}, {"url": "https://github.com/ydb-platform/ydb-mcp", "stored_at": "2025-06-24T08:43:26.214323"}, {"url": "https://github.com/mcpdotdirect/evm-mcp-server", "stored_at": "2025-06-24T08:43:26.214325"}, {"url": "https://github.com/Couchbase-Ecosystem/mcp-server-couchbase", "stored_at": "2025-06-24T08:43:26.214327"}, {"url": "https://github.com/punkpeye/awesome-mcp-clients", "stored_at": "2025-06-24T08:43:26.214329"}, {"url": "https://github.com/zcaceres/fetch-mcp", "stored_at": "2025-06-24T08:43:26.214331"}, {"url": "https://github.com/alibaba/higress", "stored_at": "2025-06-24T08:43:26.214333"}, {"url": "https://github.com/kukapay/crypto-sentiment-mcp", "stored_at": "2025-06-24T08:43:26.214335"}, {"url": "https://github.com/r-huijts/rijksmuseum-mcp", "stored_at": "2025-06-24T08:43:26.214337"}, {"url": "https://github.com/webcoderz/MCP-Geo", "stored_at": "2025-06-24T08:43:26.214339"}, {"url": "https://github.com/vectorize-io/vectorize-mcp-server", "stored_at": "2025-06-24T08:43:26.214340"}, {"url": "https://github.com/koro<PERSON><PERSON>/slack-mcp-server", "stored_at": "2025-06-24T08:43:26.214342"}, {"url": "https://github.com/CircleCI-Public/mcp-server-circleci", "stored_at": "2025-06-24T08:43:26.214344"}, {"url": "https://github.com/yuna0x0/anilist-mcp", "stored_at": "2025-06-24T08:43:26.214346"}, {"url": "https://github.com/tinyfish-io/agentql-mcp", "stored_at": "2025-06-24T08:43:26.214348"}, {"url": "https://github.com/mattijsdp/dbt-docs-mcp", "stored_at": "2025-06-24T08:43:26.214350"}, {"url": "https://github.com/Zhwt/go-mcp-mysql", "stored_at": "2025-06-24T08:43:26.214352"}, {"url": "https://github.com/BurtTheCoder/mcp-dnstwist", "stored_at": "2025-06-24T08:43:26.214354"}, {"url": "https://github.com/securityfortech/secops-mcp", "stored_at": "2025-06-24T08:43:26.214356"}, {"url": "https://github.com/yWorks/mcp-typescribe", "stored_at": "2025-06-24T08:43:26.214358"}, {"url": "https://github.com/r33drichards/mcp-js", "stored_at": "2025-06-24T08:43:26.214360"}, {"url": "https://github.com/alexander-zuev/supabase-mcp-server", "stored_at": "2025-06-24T08:43:26.214362"}, {"url": "https://github.com/doggybee/mcp-server-ccxt", "stored_at": "2025-06-24T08:43:26.214364"}, {"url": "https://github.com/browserbase/mcp-server-browserbase", "stored_at": "2025-06-24T08:43:26.214366"}, {"url": "https://github.com/andybrandt/mcp-simple-arxiv", "stored_at": "2025-06-24T08:43:26.214368"}, {"url": "https://github.com/erikhoward/adls-mcp-server", "stored_at": "2025-06-24T08:43:26.214370"}, {"url": "https://github.com/hungthai1401/bruno-mcp", "stored_at": "2025-06-24T08:43:26.214372"}, {"url": "https://github.com/flowcore-io/mcp-flowcore-platform", "stored_at": "2025-06-24T08:43:26.214374"}, {"url": "https://github.com/kukapay/funding-rates-mcp", "stored_at": "2025-06-24T08:43:26.214376"}, {"url": "https://github.com/isaacwasserman/mcp-vegalite-server", "stored_at": "2025-06-24T08:43:26.214378"}, {"url": "https://github.com/kukapay/cryptopanic-mcp-server", "stored_at": "2025-06-24T08:43:26.214380"}, {"url": "https://github.com/kiliczsh/mcp-mongo-server", "stored_at": "2025-06-24T08:43:26.214382"}, {"url": "https://github.com/strowk/mcp-k8s-go", "stored_at": "2025-06-24T08:43:26.214384"}, {"url": "https://github.com/maxim-saplin/mcp_safe_local_python_executor", "stored_at": "2025-06-24T08:43:26.214386"}, {"url": "https://github.com/kukapay/defi-yields-mcp", "stored_at": "2025-06-24T08:43:26.214388"}, {"url": "https://github.com/reading-plus-ai/mcp-server-data-exploration", "stored_at": "2025-06-24T08:43:26.214390"}, {"url": "https://github.com/LuniaKunal/mcp-twitter", "stored_at": "2025-06-24T08:43:26.214392"}, {"url": "https://github.com/blackwhite084/playwright-plus-python-mcp", "stored_at": "2025-06-24T08:43:26.214394"}, {"url": "https://github.com/NON906/omniparser-autogui-mcp", "stored_at": "2025-06-24T08:43:26.214396"}, {"url": "https://github.com/HuggingAGI/mcp-baostock-server", "stored_at": "2025-06-24T08:43:26.214398"}, {"url": "https://github.com/jlowin/fastmcp", "stored_at": "2025-06-24T08:43:26.214400"}, {"url": "https://github.com/webscraping-ai/webscraping-ai-mcp-server", "stored_at": "2025-06-24T08:43:26.214402"}, {"url": "https://github.com/Pratyay/mac-monitor-mcp", "stored_at": "2025-06-24T08:43:26.214404"}, {"url": "https://github.com/SecretiveShell/MCP-wolfram-alpha", "stored_at": "2025-06-24T08:43:26.214406"}, {"url": "https://github.com/pwh-pwh/coin-mcp-server", "stored_at": "2025-06-24T08:43:26.214408"}, {"url": "https://github.com/kukapay/rug-check-mcp", "stored_at": "2025-06-24T08:43:26.214410"}, {"url": "https://github.com/zinja-coder/jadx-ai-mcp", "stored_at": "2025-06-24T08:43:26.214412"}, {"url": "https://github.com/ferdousbhai/wsb-analyst-mcp", "stored_at": "2025-06-24T08:43:26.214414"}, {"url": "https://github.com/QAInsights/locust-mcp-server", "stored_at": "2025-06-24T08:43:26.214416"}, {"url": "https://github.com/TechDocsStudio/biel-mcp", "stored_at": "2025-06-24T08:43:26.214418"}, {"url": "https://github.com/MarkusPfundstein/mcp-gsuite", "stored_at": "2025-06-24T08:43:26.214420"}, {"url": "https://github.com/cr7258/elasticsearch-mcp-server", "stored_at": "2025-06-24T08:43:26.214422"}, {"url": "https://github.com/alexbakers/mcp-ipfs", "stored_at": "2025-06-24T08:43:26.214424"}, {"url": "https://github.com/BurtTheCoder/mcp-maigret", "stored_at": "2025-06-24T08:43:26.214426"}, {"url": "https://github.com/jinzcdev/leetcode-mcp-server", "stored_at": "2025-06-24T08:43:26.214427"}, {"url": "https://github.com/lharries/whatsapp-mcp", "stored_at": "2025-06-24T08:43:26.214429"}, {"url": "https://github.com/freema/mcp-gsheets", "stored_at": "2025-06-24T08:43:26.214431"}, {"url": "https://github.com/jyjune/mcp_vms", "stored_at": "2025-06-24T08:43:26.214433"}, {"url": "https://github.com/kshern/mcp-tavily.git", "stored_at": "2025-06-24T08:43:26.214435"}, {"url": "https://github.com/KashiwaByte/vikingdb-mcp-server", "stored_at": "2025-06-24T08:43:26.214437"}, {"url": "https://github.com/jagan-shanmugam/climatiq-mcp-server", "stored_at": "2025-06-24T08:43:26.214439"}, {"url": "https://github.com/furey/mongodb-lens", "stored_at": "2025-06-24T08:43:26.214441"}, {"url": "https://github.com/ReAPI-com/mcp-openapi", "stored_at": "2025-06-24T08:43:26.214443"}, {"url": "https://github.com/diivi/aseprite-mcp", "stored_at": "2025-06-24T08:43:26.214445"}, {"url": "https://github.com/Rai220/think-mcp", "stored_at": "2025-06-24T08:43:26.214447"}, {"url": "https://github.com/orellazri/coda-mcp", "stored_at": "2025-06-24T08:43:26.214448"}, {"url": "https://github.com/sapientpants/sonarqube-mcp-server", "stored_at": "2025-06-24T08:43:26.214450"}, {"url": "https://github.com/posthog/mcp", "stored_at": "2025-06-24T08:43:26.214452"}, {"url": "https://github.com/k-jarzyna/mcp-miro", "stored_at": "2025-06-24T08:43:26.214474"}, {"url": "https://github.com/tacticlaunch/mcp-linear", "stored_at": "2025-06-24T08:43:26.214477"}, {"url": "https://github.com/tooyipjee/yahoofinance-mcp.git", "stored_at": "2025-06-24T08:43:26.214480"}, {"url": "https://github.com/qiniu/qiniu-mcp-server", "stored_at": "2025-06-24T08:43:26.214482"}, {"url": "https://github.com/TimLuka<PERSON>tmann/mcp-weather", "stored_at": "2025-06-24T08:43:26.214484"}, {"url": "https://github.com/dkvdm/onepassword-mcp-server", "stored_at": "2025-06-24T08:43:26.214486"}, {"url": "https://github.com/keturiosakys/bluesky-context-server", "stored_at": "2025-06-24T08:43:26.214488"}, {"url": "https://github.com/OpenLinkSoftware/mcp-odbc-server", "stored_at": "2025-06-24T08:43:26.214490"}, {"url": "https://github.com/caol64/wenyan-mcp", "stored_at": "2025-06-24T08:43:26.214493"}, {"url": "https://github.com/hijaz/postmancer", "stored_at": "2025-06-24T08:43:26.214495"}, {"url": "https://github.com/ChanMeng666/server-google-news", "stored_at": "2025-06-24T08:43:26.214497"}, {"url": "https://github.com/chaindead/telegram-mcp", "stored_at": "2025-06-24T08:43:26.214499"}, {"url": "https://github.com/growthbook/growthbook-mcp", "stored_at": "2025-06-24T08:43:26.214501"}, {"url": "https://github.com/wowinter13/solscan-mcp", "stored_at": "2025-06-24T08:43:26.214503"}, {"url": "https://github.com/jovezhong/mcp-timeplus", "stored_at": "2025-06-24T08:43:26.214505"}, {"url": "https://github.com/silenceper/mcp-k8s", "stored_at": "2025-06-24T08:43:26.214507"}, {"url": "https://github.com/RomThpt/mcp-xrpl", "stored_at": "2025-06-24T08:43:26.214509"}, {"url": "https://github.com/rohitg00/kubectl-mcp-server", "stored_at": "2025-06-24T08:43:26.214510"}, {"url": "https://github.com/isaacwasserman/mcp-snowflake-server", "stored_at": "2025-06-24T08:43:26.214512"}, {"url": "https://github.com/rossshannon/weekly-weather-mcp.git", "stored_at": "2025-06-24T08:43:26.214514"}, {"url": "https://github.com/softeria/ms-365-mcp-server", "stored_at": "2025-06-24T08:43:26.214516"}, {"url": "https://github.com/aircodelabs/grasp", "stored_at": "2025-06-24T08:43:26.214518"}, {"url": "https://github.com/leehanchung/bing-search-mcp", "stored_at": "2025-06-24T08:43:26.214544"}, {"url": "https://github.com/ipfind/ipfind-mcp-server", "stored_at": "2025-06-24T08:43:26.214547"}, {"url": "https://github.com/cloudflare/mcp-server-cloudflare", "stored_at": "2025-06-24T08:43:26.214563"}, {"url": "https://github.com/Aiven-Open/mcp-aiven", "stored_at": "2025-06-24T08:43:26.214566"}, {"url": "https://github.com/dave-wind/mysql-mcp-server", "stored_at": "2025-06-24T08:43:26.214568"}, {"url": "https://github.com/keboola/keboola-mcp-server", "stored_at": "2025-06-24T08:43:26.214570"}, {"url": "https://github.com/lpigeon/ros-mcp-server", "stored_at": "2025-06-24T08:43:26.214572"}, {"url": "https://github.com/joelio/stocky", "stored_at": "2025-06-24T08:43:26.214574"}, {"url": "https://github.com/slouchd/cyberchef-api-mcp-server", "stored_at": "2025-06-24T08:43:26.214576"}, {"url": "https://github.com/QAInsights/k6-mcp-server", "stored_at": "2025-06-24T08:43:26.214578"}, {"url": "https://github.com/kukapay/cointelegraph-mcp", "stored_at": "2025-06-24T08:43:26.214580"}, {"url": "https://github.com/unibaseio/membase-mcp", "stored_at": "2025-06-24T08:43:26.214582"}, {"url": "https://github.com/kukapay/crypto-rss-mcp", "stored_at": "2025-06-24T08:43:26.214584"}, {"url": "https://github.com/jae-jae/g-search-mcp", "stored_at": "2025-06-24T08:43:26.214763"}, {"url": "https://github.com/allenporter/mcp-server-home-assistant", "stored_at": "2025-06-24T08:43:26.214766"}, {"url": "https://github.com/ferdousbhai/investor-agent", "stored_at": "2025-06-24T08:43:26.214768"}, {"url": "https://github.com/GLips/Figma-Context-MCP", "stored_at": "2025-06-24T08:43:26.214770"}, {"url": "https://github.com/SDGLBL/mcp-claude-code", "stored_at": "2025-06-24T08:43:26.214772"}, {"url": "https://github.com/awslabs/mcp", "stored_at": "2025-06-24T08:43:26.214774"}, {"url": "https://github.com/yamanoku/baseline-mcp-server", "stored_at": "2025-06-24T08:43:26.214776"}, {"url": "https://github.com/ricocf/mcp-wolframalpha", "stored_at": "2025-06-24T08:43:26.214796"}, {"url": "https://github.com/googleapis/genai-toolbox", "stored_at": "2025-06-24T08:43:26.214799"}, {"url": "https://github.com/yuna0x0/hackmd-mcp", "stored_at": "2025-06-24T08:43:26.214801"}, {"url": "https://github.com/kukapay/twitter-username-changes-mcp", "stored_at": "2025-06-24T08:43:26.214803"}, {"url": "https://github.com/13bm/GhidraMCP", "stored_at": "2025-06-24T08:43:26.214805"}, {"url": "https://github.com/erithwik/mcp-hn", "stored_at": "2025-06-24T08:43:26.214850"}, {"url": "https://github.com/fatwang2/search1api-mcp", "stored_at": "2025-06-24T08:43:26.214853"}, {"url": "https://github.com/GreptimeTeam/greptimedb-mcp-server", "stored_at": "2025-06-24T08:43:26.214855"}, {"url": "https://github.com/kukapay/modbus-mcp", "stored_at": "2025-06-24T08:43:26.214857"}, {"url": "https://github.com/kukapay/uniswap-trader-mcp", "stored_at": "2025-06-24T08:43:26.214859"}, {"url": "https://github.com/waystation-ai/mcp", "stored_at": "2025-06-24T08:43:26.214861"}, {"url": "https://github.com/gbrigandi/mcp-server-thehive", "stored_at": "2025-06-24T08:43:26.214863"}, {"url": "https://github.com/vivekVells/mcp-pandoc", "stored_at": "2025-06-24T08:43:26.214865"}, {"url": "https://github.com/FreePeak/db-mcp-server", "stored_at": "2025-06-24T08:43:26.214867"}, {"url": "https://github.com/yoelbassin/gnuradioMCP", "stored_at": "2025-06-24T08:43:26.214869"}, {"url": "https://github.com/fotoetienne/gqai", "stored_at": "2025-06-24T08:43:26.214871"}, {"url": "https://github.com/githejie/mcp-server-calculator", "stored_at": "2025-06-24T08:43:26.214898"}, {"url": "https://github.com/berlinbra/alpha-vantage-mcp", "stored_at": "2025-06-24T08:43:26.214902"}, {"url": "https://github.com/evalstate/mcp-miro", "stored_at": "2025-06-24T08:43:26.214904"}, {"url": "https://github.com/kukapay/opcua-mcp", "stored_at": "2025-06-24T08:43:26.214906"}, {"url": "https://github.com/cyclotruc/gitingest", "stored_at": "2025-06-24T08:43:26.214908"}, {"url": "https://github.com/co-browser/browser-use-mcp-server", "stored_at": "2025-06-24T08:43:26.214910"}, {"url": "https://github.com/PatrickPalmer/MayaMCP", "stored_at": "2025-06-24T08:43:26.214912"}, {"url": "https://github.com/edwinbernadus/nocodb-mcp-server", "stored_at": "2025-06-24T08:43:26.214914"}, {"url": "https://github.com/zenml-io/mcp-zenml", "stored_at": "2025-06-24T08:43:26.214916"}, {"url": "https://github.com/tradercjz/dolphindb-mcp-server", "stored_at": "2025-06-24T08:43:26.214918"}, {"url": "https://github.com/tigranbs/mcgravity", "stored_at": "2025-06-24T08:43:26.214920"}, {"url": "https://github.com/ariadng/metatrader-mcp-server", "stored_at": "2025-06-24T08:43:26.214922"}, {"url": "https://github.com/open-strategy-partners/osp_marketing_tools", "stored_at": "2025-06-24T08:43:26.214924"}, {"url": "https://github.com/dotemacs/domain-lookup-mcp", "stored_at": "2025-06-24T08:43:26.214926"}, {"url": "https://github.com/chigwell/telegram-mcp", "stored_at": "2025-06-24T08:43:26.214928"}, {"url": "https://github.com/LucasHild/mcp-server-bigquery", "stored_at": "2025-06-24T08:43:26.214930"}, {"url": "https://github.com/r-huijts/ns-mcp-server", "stored_at": "2025-06-24T08:43:26.214932"}, {"url": "https://github.com/ttommyth/interactive-mcp", "stored_at": "2025-06-24T08:43:26.214934"}, {"url": "https://github.com/kukapay/token-minter-mcp", "stored_at": "2025-06-24T08:43:26.214936"}, {"url": "https://github.com/sunriseapps/imagesorcery-mcp", "stored_at": "2025-06-24T08:43:26.214939"}, {"url": "https://github.com/Xuanwo/mcp-server-opendal", "stored_at": "2025-06-24T08:43:26.214941"}, {"url": "https://github.com/aliyun/alibabacloud-tablestore-mcp-server", "stored_at": "2025-06-24T08:43:26.214943"}, {"url": "https://github.com/jjsantos01/qgis_mcp", "stored_at": "2025-06-24T08:43:26.214945"}, {"url": "https://github.com/reeeeemo/ancestry-mcp", "stored_at": "2025-06-24T08:43:26.214947"}, {"url": "https://github.com/wenb1n-dev/mysql_mcp_server_pro", "stored_at": "2025-06-24T08:43:26.214949"}, {"url": "https://github.com/amidabuddha/unichat-mcp-server", "stored_at": "2025-06-24T08:43:26.214951"}, {"url": "https://github.com/gofireflyio/firefly-mcp", "stored_at": "2025-06-24T08:43:26.214953"}, {"url": "https://github.com/tumf/mcp-text-editor", "stored_at": "2025-06-24T08:43:26.214955"}, {"url": "https://github.com/r-huijts/strava-mcp", "stored_at": "2025-06-24T08:43:26.214957"}, {"url": "https://github.com/utensils/mcp-nixos", "stored_at": "2025-06-24T08:43:26.214958"}, {"url": "https://github.com/ekkyarmandi/ticktick-mcp", "stored_at": "2025-06-24T08:43:26.214961"}, {"url": "https://github.com/sergehuber/inoyu-mcp-unomi-server", "stored_at": "2025-06-24T08:43:26.214962"}, {"url": "https://github.com/gannonh/firebase-mcp", "stored_at": "2025-06-24T08:43:26.214964"}, {"url": "https://github.com/reza-gholizade/k8s-mcp-server", "stored_at": "2025-06-24T08:43:26.214966"}, {"url": "https://github.com/rishijatia/fantasy-pl-mcp", "stored_at": "2025-06-24T08:43:26.214968"}, {"url": "https://github.com/andybrandt/mcp-simple-openai-assistant", "stored_at": "2025-06-24T08:43:26.214970"}, {"url": "https://github.com/OctoMind-dev/octomind-mcp", "stored_at": "2025-06-24T08:43:26.214972"}, {"url": "https://github.com/InditexTech/mcp-teams-server", "stored_at": "2025-06-24T08:43:26.214974"}, {"url": "https://github.com/wegotdocs/open-mcp", "stored_at": "2025-06-24T08:43:26.214976"}, {"url": "https://github.com/QAInsights/jmeter-mcp-server", "stored_at": "2025-06-24T08:43:26.214978"}, {"url": "https://github.com/danielkennedy1/pdf-tools-mcp", "stored_at": "2025-06-24T08:43:26.214980"}, {"url": "https://github.com/kukapay/etf-flow-mcp", "stored_at": "2025-06-24T08:43:26.214982"}, {"url": "https://github.com/weibaohui/k8m", "stored_at": "2025-06-24T08:43:26.214985"}, {"url": "https://github.com/JordiNeil/mcp-databricks-server", "stored_at": "2025-06-24T08:43:26.214986"}, {"url": "https://github.com/hannesrudolph/mcp-ragdocs", "stored_at": "2025-06-24T08:43:26.214988"}, {"url": "https://github.com/pab1it0/tripadvisor-mcp", "stored_at": "2025-06-24T08:43:26.214991"}, {"url": "https://github.com/mzxrai/mcp-openai", "stored_at": "2025-06-24T08:43:26.214993"}, {"url": "https://github.com/fireproof-storage/mcp-database-server", "stored_at": "2025-06-24T08:43:26.214994"}, {"url": "https://github.com/fr0gger/MCP_Security", "stored_at": "2025-06-24T08:43:26.214997"}, {"url": "https://github.com/gotoolkits/mcp-wecombot-server.git", "stored_at": "2025-06-24T08:43:26.214999"}, {"url": "https://github.com/yepcode/mcp-server-js", "stored_at": "2025-06-24T08:43:26.215001"}, {"url": "https://github.com/artmann/package-registry-mcp", "stored_at": "2025-06-24T08:43:26.215003"}, {"url": "https://github.com/ndthanhdev/mcp-browser-kit", "stored_at": "2025-06-24T08:43:26.215005"}, {"url": "https://github.com/biegehydra/BifrostMCP", "stored_at": "2025-06-24T08:43:26.215007"}, {"url": "https://github.com/Mtehabsim/ScreenPilot", "stored_at": "2025-06-24T08:43:26.215009"}, {"url": "https://github.com/tuannvm/mcp-trino", "stored_at": "2025-06-24T08:43:26.215011"}, {"url": "https://github.com/Tiberriver256/mcp-server-azure-devops", "stored_at": "2025-06-24T08:43:26.215013"}, {"url": "https://github.com/pinecone-io/assistant-mcp", "stored_at": "2025-06-24T08:43:26.215015"}, {"url": "https://github.com/confluentinc/mcp-confluent", "stored_at": "2025-06-24T08:43:26.215067"}, {"url": "https://github.com/EKibort/wrike-mcp-server", "stored_at": "2025-06-24T08:43:26.215070"}, {"url": "https://github.com/awwaiid/mcp-server-taskwarrior", "stored_at": "2025-06-24T08:43:26.215072"}, {"url": "https://github.com/kukapay/crypto-feargreed-mcp", "stored_at": "2025-06-24T08:43:26.215074"}, {"url": "https://github.com/feuerdev/keep-mcp", "stored_at": "2025-06-24T08:43:26.215076"}, {"url": "https://github.com/mindsdb/mindsdb", "stored_at": "2025-06-24T08:43:26.215078"}, {"url": "https://github.com/AbdelStark/bitcoin-mcp", "stored_at": "2025-06-24T08:43:26.215080"}, {"url": "https://github.com/cyberchitta/llm-context.py", "stored_at": "2025-06-24T08:43:26.215082"}, {"url": "https://github.com/recursechat/mcp-server-apple-shortcuts", "stored_at": "2025-06-24T08:43:26.215084"}, {"url": "https://github.com/f4ww4z/mcp-mysql-server", "stored_at": "2025-06-24T08:43:26.215086"}, {"url": "https://github.com/delano/postman-mcp-server", "stored_at": "2025-06-24T08:43:26.215088"}, {"url": "https://github.com/idoru/influxdb-mcp-server", "stored_at": "2025-06-24T08:43:26.215090"}, {"url": "https://github.com/weibaohui/kom", "stored_at": "2025-06-24T08:43:26.215093"}, {"url": "https://github.com/qdrant/mcp-server-qdrant", "stored_at": "2025-06-24T08:43:26.215095"}, {"url": "https://github.com/kw510/strava-mcp", "stored_at": "2025-06-24T08:43:26.215096"}, {"url": "https://github.com/bart6114/my-bear-mcp-server", "stored_at": "2025-06-24T08:43:26.215098"}, {"url": "https://github.com/eyalzh/browser-control-mcp", "stored_at": "2025-06-24T08:43:26.215100"}, {"url": "https://github.com/weaviate/mcp-server-weaviate", "stored_at": "2025-06-24T08:43:26.215102"}, {"url": "https://github.com/sxhxliang/mcp-access-point", "stored_at": "2025-06-24T08:43:26.215104"}, {"url": "https://github.com/johnneerdael/netskope-mcp", "stored_at": "2025-06-24T08:43:26.215107"}, {"url": "https://github.com/gitmotion/ntfy-me-mcp", "stored_at": "2025-06-24T08:43:26.215109"}, {"url": "https://github.com/mrexodia/user-feedback-mcp", "stored_at": "2025-06-24T08:43:26.215110"}, {"url": "https://github.com/ferrislucas/iterm-mcp", "stored_at": "2025-06-24T08:43:26.215112"}, {"url": "https://github.com/ChronulusAI/chronulus-mcp", "stored_at": "2025-06-24T08:43:26.215114"}, {"url": "https://github.com/nwiizo/tfmcp", "stored_at": "2025-06-24T08:43:26.215219"}, {"url": "https://github.com/janswist/mcp-dexscreener", "stored_at": "2025-06-24T08:43:26.215246"}, {"url": "https://github.com/hiromitsusasaki/raindrop-io-mcp-server", "stored_at": "2025-06-24T08:43:26.215248"}, {"url": "https://github.com/github/github-mcp-server", "stored_at": "2025-06-24T08:43:26.215251"}, {"url": "https://github.com/XixianLiang/HarmonyOS-mcp-server", "stored_at": "2025-06-24T08:43:26.215253"}, {"url": "https://github.com/prisma/prisma", "stored_at": "2025-06-24T08:43:26.215255"}, {"url": "https://github.com/r-huijts/xcode-mcp-server", "stored_at": "2025-06-24T08:43:26.215257"}, {"url": "https://github.com/guillochon/mlb-api-mcp", "stored_at": "2025-06-24T08:43:26.215259"}, {"url": "https://github.com/armorwallet/armor-crypto-mcp", "stored_at": "2025-06-24T08:43:26.215261"}, {"url": "https://github.com/hloiseaufcms/mcp-gopls", "stored_at": "2025-06-24T08:43:26.215263"}, {"url": "https://github.com/nick1udwig/kibitz", "stored_at": "2025-06-24T08:43:26.215265"}, {"url": "https://github.com/microsoft/playwright-mcp", "stored_at": "2025-06-24T08:43:26.215267"}, {"url": "https://github.com/gwbischof/free-will-mcp", "stored_at": "2025-06-24T08:43:26.215269"}, {"url": "https://github.com/jaipandya/producthunt-mcp-server", "stored_at": "2025-06-24T08:43:26.215271"}, {"url": "https://github.com/danhilse/notion_mcp", "stored_at": "2025-06-24T08:43:26.215273"}, {"url": "https://github.com/reading-plus-ai/mcp-server-deep-research", "stored_at": "2025-06-24T08:43:26.215274"}, {"url": "https://github.com/pskill9/hn-server", "stored_at": "2025-06-24T08:43:26.215277"}, {"url": "https://github.com/VeriTeknik/pluggedin-mcp-proxy", "stored_at": "2025-06-24T08:43:26.215279"}, {"url": "https://github.com/baba786/phabricator-mcp-server", "stored_at": "2025-06-24T08:43:26.215281"}, {"url": "https://github.com/narumiruna/yfinance-mcp", "stored_at": "2025-06-24T08:43:26.215283"}, {"url": "https://github.com/dbt-labs/dbt-mcp", "stored_at": "2025-06-24T08:43:26.215285"}, {"url": "https://github.com/pskill9/web-search", "stored_at": "2025-06-24T08:43:26.215287"}, {"url": "https://github.com/kdqed/zaturn", "stored_at": "2025-06-24T08:43:26.215288"}, {"url": "https://github.com/zoomeye-ai/mcp_zoomeye", "stored_at": "2025-06-24T08:43:26.215290"}, {"url": "https://github.com/arrismo/kaggle-mcp", "stored_at": "2025-06-24T08:43:26.215292"}, {"url": "https://github.com/sooperset/mcp-atlassian", "stored_at": "2025-06-24T08:43:26.215294"}, {"url": "https://github.com/OpenLinkSoftware/mcp-jdbc-server", "stored_at": "2025-06-24T08:43:26.215296"}, {"url": "https://github.com/evalstate/mcp-hfspace", "stored_at": "2025-06-24T08:43:26.215298"}, {"url": "https://github.com/hamflx/imagen3-mcp", "stored_at": "2025-06-24T08:43:26.215300"}, {"url": "https://github.com/chroma-core/chroma-mcp", "stored_at": "2025-06-24T08:43:26.215302"}, {"url": "https://github.com/Seym0n/tiktok-mcp", "stored_at": "2025-06-24T08:43:26.215304"}, {"url": "https://github.com/ClickHouse/mcp-clickhouse", "stored_at": "2025-06-24T08:43:26.215306"}, {"url": "https://github.com/mikechao/metmuseum-mcp", "stored_at": "2025-06-24T08:43:26.215308"}, {"url": "https://github.com/kukapay/whale-tracker-mcp", "stored_at": "2025-06-24T08:43:26.215310"}, {"url": "https://github.com/akseyh/bear-mcp-server", "stored_at": "2025-06-24T08:43:26.215312"}, {"url": "https://github.com/kukapay/freqtrade-mcp", "stored_at": "2025-06-24T08:43:26.215314"}, {"url": "https://github.com/mediar-ai/screenpipe", "stored_at": "2025-06-24T08:43:26.215316"}, {"url": "https://github.com/IvanAmador/vercel-ai-docs-mcp", "stored_at": "2025-06-24T08:43:26.215318"}, {"url": "https://github.com/BitteProtocol/mcp", "stored_at": "2025-06-24T08:43:26.215320"}, {"url": "https://github.com/automation-ai-labs/mcp-link", "stored_at": "2025-06-24T08:43:26.215322"}, {"url": "https://github.com/jae-jae/fetcher-mcp", "stored_at": "2025-06-24T08:43:26.215341"}, {"url": "https://github.com/manusa/kubernetes-mcp-server", "stored_at": "2025-06-24T08:43:26.215343"}, {"url": "https://github.com/louiscklaw/hko-mcp", "stored_at": "2025-06-24T08:43:26.215345"}, {"url": "https://github.com/base/base-mcp", "stored_at": "2025-06-24T08:43:26.215347"}, {"url": "https://github.com/<PERSON><PERSON><PERSON>/notion-mcp", "stored_at": "2025-06-24T08:43:26.215348"}, {"url": "https://github.com/gotoolkits/mcp-difyworkflow-server", "stored_at": "2025-06-24T08:43:26.215350"}, {"url": "https://github.com/stass/exif-mcp", "stored_at": "2025-06-24T08:43:26.215352"}, {"url": "https://github.com/Canner/wren-engine", "stored_at": "2025-06-24T08:43:26.215354"}, {"url": "https://github.com/mmntm/weblate-mcp", "stored_at": "2025-06-24T08:43:26.215356"}, {"url": "https://github.com/alexei-led/k8s-mcp-server", "stored_at": "2025-06-24T08:43:26.215358"}, {"url": "https://github.com/zlinzzzz/finData-mcp-server", "stored_at": "2025-06-24T08:43:26.215360"}, {"url": "https://github.com/portainer/portainer-mcp", "stored_at": "2025-06-24T08:43:26.215362"}, {"url": "https://github.com/calclavia/mcp-obsidian", "stored_at": "2025-06-24T08:43:26.215364"}, {"url": "https://github.com/ShenghaiWang/xcodebuild", "stored_at": "2025-06-24T08:43:26.215366"}, {"url": "https://github.com/mikechao/brave-search-mcp", "stored_at": "2025-06-24T08:43:26.215368"}, {"url": "https://github.com/davidlin2k/pox-mcp-server", "stored_at": "2025-06-24T08:43:26.215370"}, {"url": "https://github.com/i-am-bee/acp-mcp", "stored_at": "2025-06-24T08:43:26.215372"}, {"url": "https://github.com/niledatabase/nile-mcp-server", "stored_at": "2025-06-24T08:43:26.215380"}, {"url": "https://github.com/aaronjmars/web3-research-mcp", "stored_at": "2025-06-24T08:43:26.215384"}, {"url": "https://github.com/djalal/quran-mcp-server", "stored_at": "2025-06-24T08:43:26.215387"}, {"url": "https://github.com/trilogy-group/aws-pricing-mcp", "stored_at": "2025-06-24T08:43:26.215389"}, {"url": "https://github.com/lostintangent/gistpad-mcp", "stored_at": "2025-06-24T08:43:26.215391"}, {"url": "https://github.com/awkoy/replicate-flux-mcp", "stored_at": "2025-06-24T08:43:26.215393"}, {"url": "https://github.com/zueai/mcp-manager", "stored_at": "2025-06-24T08:43:26.215395"}, {"url": "https://github.com/flipt-io/mcp-server-flipt", "stored_at": "2025-06-24T08:43:26.215397"}, {"url": "https://github.com/mrexodia/ida-pro-mcp", "stored_at": "2025-06-24T08:43:26.215399"}, {"url": "https://github.com/memgraph/mcp-memgraph", "stored_at": "2025-06-24T08:43:26.215403"}, {"url": "https://github.com/rashidazarang/airtable-mcp", "stored_at": "2025-06-24T08:43:26.215405"}, {"url": "https://github.com/chrishayuk/mcp-cli", "stored_at": "2025-06-24T08:43:26.215407"}, {"url": "https://github.com/johanne<PERSON>brand<PERSON>/typst-mcp", "stored_at": "2025-06-24T08:43:26.215409"}, {"url": "https://github.com/MladenSU/cli-mcp-server", "stored_at": "2025-06-24T08:43:26.215412"}, {"url": "https://github.com/jparkerweb/mcp-sqlite", "stored_at": "2025-06-24T08:43:26.215414"}, {"url": "https://github.com/genomoncology/biomcp", "stored_at": "2025-06-24T08:43:26.215416"}, {"url": "https://github.com/hmk/box-mcp-server", "stored_at": "2025-06-24T08:43:26.215418"}, {"url": "https://github.com/video-creator/ffmpeg-mcp.git", "stored_at": "2025-06-24T08:43:26.215419"}, {"url": "https://github.com/joshuarileydev/supabase", "stored_at": "2025-06-24T08:43:26.215421"}, {"url": "https://github.com/julien040/anyquery", "stored_at": "2025-06-24T08:43:26.215423"}, {"url": "https://github.com/admica/FileScopeMCP", "stored_at": "2025-06-24T08:43:26.215426"}, {"url": "https://github.com/wenhuwang/mcp-k8s-eye", "stored_at": "2025-06-24T08:43:26.215428"}, {"url": "https://github.com/axliupore/mcp-code-runner", "stored_at": "2025-06-24T08:43:26.215430"}, {"url": "https://github.com/zilliztech/mcp-server-milvus", "stored_at": "2025-06-24T08:43:26.215432"}, {"url": "https://github.com/tumf/grafana-loki-mcp", "stored_at": "2025-06-24T08:43:26.215434"}, {"url": "https://github.com/mzxrai/mcp-webresearch", "stored_at": "2025-06-24T08:43:26.215435"}, {"url": "https://github.com/kukapay/crypto-portfolio-mcp", "stored_at": "2025-06-24T08:43:26.215437"}, {"url": "https://github.com/jeannier/homebrew-mcp", "stored_at": "2025-06-24T08:43:26.215439"}, {"url": "https://github.com/markmap/markmap", "stored_at": "2025-06-24T08:43:26.215441"}, {"url": "https://github.com/bright8192/esxi-mcp-server", "stored_at": "2025-06-24T08:43:26.215443"}, {"url": "https://github.com/CoderGamester/mcp-unity", "stored_at": "2025-06-24T08:43:26.215445"}, {"url": "https://github.com/JetBrains/mcpProxy", "stored_at": "2025-06-24T08:43:26.215447"}, {"url": "https://github.com/LaurieWired/GhidraMCP", "stored_at": "2025-06-24T08:43:26.215449"}, {"url": "https://github.com/KS-GEN-AI/jira-mcp-server", "stored_at": "2025-06-24T08:43:26.215451"}, {"url": "https://github.com/ferdousbhai/tasty-agent", "stored_at": "2025-06-24T08:43:26.215453"}, {"url": "https://github.com/zhsama/duckduckgo-mpc-server", "stored_at": "2025-06-24T08:43:26.215455"}, {"url": "https://github.com/hmk/attio-mcp-server", "stored_at": "2025-06-24T08:43:26.215457"}, {"url": "https://github.com/apify/actors-mcp-server", "stored_at": "2025-06-24T08:43:26.215459"}, {"url": "https://github.com/the0807/GeekNews-MCP-Server", "stored_at": "2025-06-24T08:43:26.215461"}, {"url": "https://github.com/TencentEdgeOne/edgeone-pages-mcp", "stored_at": "2025-06-24T08:43:26.215463"}, {"url": "https://github.com/omni-mcp/isaac-sim-mcp", "stored_at": "2025-06-24T08:43:26.215479"}, {"url": "https://github.com/devilcoder01/weather-mcp-server", "stored_at": "2025-06-24T08:43:26.215481"}, {"url": "https://github.com/alfonsograziano/node-code-sandbox-mcp", "stored_at": "2025-06-24T08:43:26.215483"}, {"url": "https://github.com/kimtaeyoon83/mcp-server-youtube-transcript", "stored_at": "2025-06-24T08:43:26.215486"}, {"url": "https://github.com/kadykov/mcp-openapi-schema-explorer", "stored_at": "2025-06-24T08:43:26.215488"}, {"url": "https://github.com/Codex-Data/codex-mcp", "stored_at": "2025-06-24T08:43:26.215490"}, {"url": "https://github.com/wanaku-ai/wanaku", "stored_at": "2025-06-24T08:43:26.215492"}, {"url": "https://github.com/alexei-led/aws-mcp-server", "stored_at": "2025-06-24T08:43:26.215494"}, {"url": "https://github.com/kimtth/mcp-remote-call-ping-pong", "stored_at": "2025-06-24T08:43:26.215496"}, {"url": "https://github.com/abhiemj/manim-mcp-server", "stored_at": "2025-06-24T08:43:26.215580"}, {"url": "https://github.com/wuye-ai/mcp-server-wuye-ai", "stored_at": "2025-06-24T08:43:26.215583"}, {"url": "https://github.com/ArchAI-Labs/fastmcp-sonarqube-metrics", "stored_at": "2025-06-24T08:43:26.215585"}, {"url": "https://github.com/mbailey/voice-mcp", "stored_at": "2025-06-24T08:43:26.215589"}, {"url": "https://github.com/j4c0bs/mcp-server-sql-analyzer", "stored_at": "2025-06-24T08:43:26.215591"}, {"url": "https://github.com/tufantunc/ssh-mcp", "stored_at": "2025-06-24T08:43:26.215593"}, {"url": "https://github.com/roadwy/cve-search_mcp", "stored_at": "2025-06-24T08:43:26.215595"}, {"url": "https://github.com/semgrep/mcp", "stored_at": "2025-06-24T08:43:26.215597"}, {"url": "https://github.com/OpenDataMCP/OpenDataMCP", "stored_at": "2025-06-24T08:43:26.215599"}, {"url": "https://github.com/mikechao/balldontlie-mcp", "stored_at": "2025-06-24T08:43:26.215601"}, {"url": "https://github.com/mahdin75/geoserver-mcp", "stored_at": "2025-06-24T08:43:26.215605"}, {"url": "https://github.com/intruder-io/intruder-mcp", "stored_at": "2025-06-24T08:43:26.215606"}, {"url": "https://github.com/metatool-ai/metatool-app", "stored_at": "2025-06-24T08:43:26.215608"}, {"url": "https://github.com/Govcraft/rust-docs-mcp-server", "stored_at": "2025-06-24T08:43:26.215610"}, {"url": "https://github.com/mrjoshuak/godoc-mcp", "stored_at": "2025-06-24T08:43:26.215613"}, {"url": "https://github.com/kukapay/crypto-news-mcp", "stored_at": "2025-06-24T08:43:26.215615"}, {"url": "https://github.com/doggybee/mcp-server-leetcode", "stored_at": "2025-06-24T08:43:26.215617"}, {"url": "https://github.com/yincongcyincong/VictoriaMetrics-mcp-server", "stored_at": "2025-06-24T08:43:26.215619"}, {"url": "https://github.com/tgeselle/bugsnag-mcp", "stored_at": "2025-06-24T08:43:26.215630"}, {"url": "https://github.com/AbdelStark/nostr-mcp", "stored_at": "2025-06-24T08:43:26.215635"}, {"url": "https://github.com/UserAd/didlogic_mcp", "stored_at": "2025-06-24T08:43:26.215637"}, {"url": "https://github.com/gbrigandi/mcp-server-wazuh", "stored_at": "2025-06-24T08:43:26.215639"}, {"url": "https://github.com/line/line-bot-mcp-server", "stored_at": "2025-06-24T08:43:26.215692"}, {"url": "https://github.com/redis/mcp-redis-cloud", "stored_at": "2025-06-24T08:43:26.215754"}, {"url": "https://github.com/pwh-pwh/cal-mcp", "stored_at": "2025-06-24T08:43:26.215758"}, {"url": "https://github.com/sirmews/apple-notes-mcp", "stored_at": "2025-06-24T08:43:26.215760"}, {"url": "https://github.com/OthmaneBlial/term_mcp_deepseek", "stored_at": "2025-06-24T08:43:26.215762"}, {"url": "https://github.com/pab1it0/prometheus-mcp-server", "stored_at": "2025-06-24T08:43:26.215764"}, {"url": "https://github.com/ezyang/codemcp", "stored_at": "2025-06-24T08:43:26.215766"}, {"url": "https://github.com/salesforce-mcp/salesforce-mcp", "stored_at": "2025-06-24T08:43:26.215768"}, {"url": "https://github.com/juehang/vscode-mcp-server", "stored_at": "2025-06-24T08:43:26.215770"}, {"url": "https://github.com/yangkyeongmo/mcp-server-apache-airflow", "stored_at": "2025-06-24T08:43:26.215772"}, {"url": "https://github.com/sammcj/mcp-package-version", "stored_at": "2025-06-24T08:43:26.215774"}, {"url": "https://github.com/stefan-xyz/mcp-server-runescape", "stored_at": "2025-06-24T08:43:26.215776"}, {"url": "https://github.com/haris-musa/excel-mcp-server", "stored_at": "2025-06-24T08:43:26.215778"}, {"url": "https://github.com/Tomatio13/mcp-server-tavily", "stored_at": "2025-06-24T08:43:26.215780"}, {"url": "https://github.com/anaisbetts/mcp-youtube", "stored_at": "2025-06-24T08:43:26.215782"}, {"url": "https://github.com/JoshuaRileyDev/mac-apps-launcher", "stored_at": "2025-06-24T08:43:26.215784"}, {"url": "https://github.com/centralmind/gateway", "stored_at": "2025-06-24T08:43:26.215786"}, {"url": "https://github.com/openbnb-org/mcp-server-airbnb", "stored_at": "2025-06-24T08:43:26.215788"}, {"url": "https://github.com/mamertofabian/mcp-everything-search", "stored_at": "2025-06-24T08:43:26.215790"}, {"url": "https://github.com/gomarble-ai/facebook-ads-mcp-server", "stored_at": "2025-06-24T08:43:26.215792"}, {"url": "https://github.com/marcelmarais/spotify-mcp-server", "stored_at": "2025-06-24T08:43:26.215795"}, {"url": "https://github.com/ambar/simctl-mcp", "stored_at": "2025-06-24T08:43:26.215797"}, {"url": "https://github.com/c4pt0r/mcp-server-tidb", "stored_at": "2025-06-24T08:43:26.215799"}, {"url": "https://github.com/pierrebrunelle/mcp-server-openai", "stored_at": "2025-06-24T08:43:26.215822"}, {"url": "https://github.com/jsdelivr/globalping-mcp-server", "stored_at": "2025-06-24T08:43:26.215825"}, {"url": "https://github.com/nickclyde/duckduckgo-mcp-server", "stored_at": "2025-06-24T08:43:26.215827"}, {"url": "https://github.com/VmLia/books-mcp-server", "stored_at": "2025-06-24T08:43:26.215829"}, {"url": "https://github.com/apache/apisix", "stored_at": "2025-06-24T08:43:26.215831"}, {"url": "https://github.com/punkpeye/awesome-mcp-devtools", "stored_at": "2025-06-24T08:43:26.215833"}, {"url": "https://github.com/yashshingvi/databricks-genie-MCP", "stored_at": "2025-06-24T08:43:26.215835"}, {"url": "https://github.com/JordanDalton/RestCsvMcpServer", "stored_at": "2025-06-24T08:43:26.215837"}, {"url": "https://github.com/34892002/bilibili-mcp-js", "stored_at": "2025-06-24T08:43:26.215839"}, {"url": "https://github.com/pyroprompts/any-chat-completions-mcp", "stored_at": "2025-06-24T08:43:26.215841"}, {"url": "https://github.com/VictoriaMetrics-Community/mcp-victoriametrics", "stored_at": "2025-06-24T08:43:26.215844"}, {"url": "https://github.com/domdomegg/airtable-mcp-server", "stored_at": "2025-06-24T08:43:26.215846"}, {"url": "https://github.com/supabase-community/supabase-mcp", "stored_at": "2025-06-24T08:43:26.215848"}, {"url": "https://github.com/intentos-labs/beeper-mcp", "stored_at": "2025-06-24T08:43:26.215877"}, {"url": "https://github.com/lucygoodchild/mcp-national-rail", "stored_at": "2025-06-24T08:43:26.215880"}, {"url": "https://github.com/ragieai/ragie-mcp-server", "stored_at": "2025-06-24T08:43:26.215883"}, {"url": "https://github.com/MindscapeHQ/mcp-server-raygun", "stored_at": "2025-06-24T08:43:26.215885"}, {"url": "https://github.com/kukapay/token-revoke-mcp", "stored_at": "2025-06-24T08:43:26.215887"}, {"url": "https://github.com/kukapay/dune-analytics-mcp", "stored_at": "2025-06-24T08:43:26.215889"}, {"url": "https://github.com/Ryan0204/github-repo-mcp", "stored_at": "2025-06-24T08:43:26.215891"}, {"url": "https://github.com/SaintDoresh/Weather-MCP-ClaudeDesktop.git", "stored_at": "2025-06-24T08:43:26.215894"}, {"url": "https://github.com/SecretiveShell/MCP-timeserver", "stored_at": "2025-06-24T08:43:26.215896"}, {"url": "https://github.com/kukapay/thegraph-mcp", "stored_at": "2025-06-24T08:43:26.215898"}, {"url": "https://github.com/roychri/mcp-server-asana", "stored_at": "2025-06-24T08:43:26.215900"}, {"url": "https://github.com/zxkane/mcp-server-amazon-bedrock", "stored_at": "2025-06-24T08:43:26.215902"}, {"url": "https://github.com/jiayao/mcp-chess", "stored_at": "2025-06-24T08:43:26.215904"}, {"url": "https://github.com/r-huijts/opentk-mcp", "stored_at": "2025-06-24T08:43:26.215906"}, {"url": "https://github.com/Hypersequent/qasphere-mcp", "stored_at": "2025-06-24T08:43:26.215908"}, {"url": "https://github.com/NakaokaRei/swift-mcp-gui.git", "stored_at": "2025-06-24T08:43:26.215910"}, {"url": "https://github.com/DealExpress/mcp-server", "stored_at": "2025-06-24T08:43:26.215912"}, {"url": "https://github.com/automateyournetwork/pyATS_MCP", "stored_at": "2025-06-24T08:43:26.215914"}, {"url": "https://github.com/pskill9/website-downloader", "stored_at": "2025-06-24T08:43:26.215916"}, {"url": "https://github.com/FradSer/mcp-server-apple-reminders", "stored_at": "2025-06-24T08:43:26.215918"}, {"url": "https://github.com/kukapay/chainlink-feeds-mcp", "stored_at": "2025-06-24T08:43:26.215920"}, {"url": "https://github.com/lamemind/mcp-server-multiverse", "stored_at": "2025-06-24T08:43:26.215922"}, {"url": "https://github.com/Jktfe/serveMyAPI", "stored_at": "2025-06-24T08:43:26.215924"}, {"url": "https://github.com/emicklei/mcp-log-proxy", "stored_at": "2025-06-24T08:43:26.215926"}, {"url": "https://github.com/api7/apisix-mcp", "stored_at": "2025-06-24T08:43:26.215928"}, {"url": "https://github.com/nguyenvanduocit/all-in-one-model-context-protocol", "stored_at": "2025-06-24T08:43:26.215930"}, {"url": "https://github.com/entanglr/zettelkasten-mcp", "stored_at": "2025-06-24T08:43:26.215932"}, {"url": "https://github.com/alchemyplatform/alchemy-mcp-server", "stored_at": "2025-06-24T08:43:26.215934"}, {"url": "https://github.com/kimtth/mcp-aoai-web-browsing", "stored_at": "2025-06-24T08:43:26.215936"}, {"url": "https://github.com/exoticknight/mcp-file-merger", "stored_at": "2025-06-24T08:43:26.215939"}, {"url": "https://github.com/CheMiguel23/MemoryMesh", "stored_at": "2025-06-24T08:43:26.215941"}, {"url": "https://github.com/kukapay/crypto-trending-mcp", "stored_at": "2025-06-24T08:43:26.215943"}, {"url": "https://github.com/anaisbetts/mcp-installer", "stored_at": "2025-06-24T08:43:26.215945"}, {"url": "https://github.com/sawa-zen/vrchat-mcp", "stored_at": "2025-06-24T08:43:26.215946"}]