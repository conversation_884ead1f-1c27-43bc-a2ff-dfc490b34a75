2025-06-24 07:40:56,031 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 07:40:58,164 - INFO - Content processor connected to Redis event bus
2025-06-24 07:40:58,183 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:10,941 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:10,953 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:10,954 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:10,956 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:10,956 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:10,969 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:18,344 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:18,355 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:18,357 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:18,357 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:18,361 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:18,373 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:40,686 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:40,707 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:40,709 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:40,709 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:40,717 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:40,732 - INFO - Enhanced SEO for content: Transforming Workflows with MCP Servers: Python Executor & DroidMind
2025-06-24 07:41:40,732 - INFO - Enhanced SEO for content: Transforming Workflows with MCP Servers: Python Executor & DroidMind
2025-06-24 07:41:40,759 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:57,202 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:57,206 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:57,207 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:57,207 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:57,211 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:57,219 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:57,453 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 07:41:57,453 - INFO - Retrying request to /chat/completions in 3.000000 seconds
2025-06-24 07:42:07,476 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:07,481 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:07,481 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:07,486 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:07,493 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:07,999 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 07:42:08,000 - INFO - Retrying request to /chat/completions in 4.000000 seconds
2025-06-24 07:42:31,592 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:31,597 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:31,598 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:31,599 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:31,605 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:31,611 - INFO - Enhanced SEO for content: MCP-Server-RabbitMQ: Your Ultimate RabbitMQ Management Tool
2025-06-24 07:42:31,611 - INFO - Enhanced SEO for content: MCP-Server-RabbitMQ: Your Ultimate RabbitMQ Management Tool
2025-06-24 07:42:31,623 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:45,980 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:45,986 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:45,987 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:45,987 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:45,991 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:45,996 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:52,246 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:52,248 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:52,248 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:52,248 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:52,251 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:52,257 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:52,560 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 07:42:52,561 - INFO - Retrying request to /chat/completions in 20.000000 seconds
2025-06-24 07:43:38,416 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:43:38,427 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:43:38,428 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:43:38,428 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:43:38,440 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:43:38,446 - INFO - Enhanced SEO for content: ida-pro-mcp
2025-06-24 08:02:11,516 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:02:13,843 - INFO - Content processor connected to Redis event bus
2025-06-24 08:02:13,868 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:02:23,219 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:02:23,242 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:02:23,244 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:23,245 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:23,248 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:23,277 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:02:31,622 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:02:31,628 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:02:31,629 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:31,630 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:31,637 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:31,643 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:02:51,337 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:02:51,348 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:02:51,348 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:51,349 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:51,358 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:51,364 - ERROR - Content generation error: 'dict' object has no attribute 'lower'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/run_content_generation.py", line 115, in run_content_generation
    processor.process_filtered_urls(filter_urls, max_repos)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 2177, in process_filtered_urls
    processed_batch = self.process_batch(batch)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 1662, in process_batch
    final_item = self._intelligently_populate_all_fields(
        content_data, item, analysis_data, seo_data, run_instructions
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 479, in _intelligently_populate_all_fields
    final_item = self._add_eeat_enhancements(final_item, item)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 581, in _add_eeat_enhancements
    final_item['case_studies'] = self._generate_case_studies(final_item)
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 631, in _generate_case_studies
    if 'decision' in use_case.lower() or 'strategy' in use_case.lower():
                     ^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'lower'
2025-06-24 08:03:44,602 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:03:46,640 - INFO - Content processor connected to Redis event bus
2025-06-24 08:03:46,663 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:03:59,257 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:03:59,293 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:03:59,293 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:03:59,294 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:03:59,295 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:03:59,307 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:04:06,185 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:04:06,187 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:04:06,187 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:06,187 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:06,190 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:06,195 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:04:37,396 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:04:37,411 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:04:37,413 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:37,413 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:37,427 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:37,438 - ERROR - Content generation error: 'dict' object has no attribute 'lower'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/run_content_generation.py", line 118, in run_content_generation
    processor.process_limited(max_repos)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 2098, in process_limited
    processed_batch = self.process_batch(batch)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 1662, in process_batch
    final_item = self._intelligently_populate_all_fields(
        content_data, item, analysis_data, seo_data, run_instructions
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 479, in _intelligently_populate_all_fields
    final_item = self._add_eeat_enhancements(final_item, item)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 581, in _add_eeat_enhancements
    final_item['case_studies'] = self._generate_case_studies(final_item)
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 631, in _generate_case_studies
    if 'decision' in use_case.lower() or 'strategy' in use_case.lower():
                     ^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'lower'
2025-06-24 08:10:09,661 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:10:11,731 - INFO - Content processor connected to Redis event bus
2025-06-24 08:10:11,757 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:10:28,674 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:10:28,708 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:10:28,709 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:28,710 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:28,711 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:28,742 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:10:37,131 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:10:37,139 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:10:37,140 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:37,142 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:37,152 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:37,194 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:10:58,599 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:10:58,607 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:10:58,608 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:58,609 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:58,616 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:58,646 - INFO - Enhanced SEO for content: Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation
2025-06-24 08:20:25,220 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:20:27,214 - INFO - Content processor connected to Redis event bus
2025-06-24 08:20:27,252 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:20:43,801 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:20:43,827 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:20:43,828 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:43,829 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:43,831 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:43,877 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:20:56,975 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:20:56,987 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:20:56,988 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:56,988 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:56,997 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:57,043 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:21:21,216 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:21:21,221 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:21:21,225 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:21:21,225 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:21:21,240 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:21:21,282 - INFO - Enhanced SEO for content: Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability
2025-06-24 08:22:35,944 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:22:38,280 - INFO - Content processor connected to Redis event bus
2025-06-24 08:22:38,314 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:22:52,962 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:22:52,990 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:22:52,991 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:22:52,991 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:22:52,992 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:22:53,035 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:23:02,173 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:23:02,174 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:23:02,174 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:02,174 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:02,176 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:02,192 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:23:15,055 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:23:15,083 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:23:15,084 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:15,101 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:15,141 - INFO - Enhanced SEO for content: imessage-query-fastmcp-mcp-server
